/* 通话中，小的悬浮窗 */
"use strict";
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const LayoutManager = require("yunos/ui/markup/LayoutManager");
const WindowCAF = require("yunos/ui/view/Window");
const TextView = require("yunos/ui/view/TextView");
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const Utils = require("../utils/Utils");
const BaseLayout = require("./BaseLayout");
const CallService = require("../services/CallService");
const Log = require("../utils/Logs");
const TapRecognizer = require("yunos/ui/gesture/TapRecognizer");
const TrackerUtil = require("../utils/TrackerUtil");
const View = require("yunos/ui/view/View");
const DialsConstants = require("../utils/DialsConstants");
const TAG = "IncallingFloatWindowTwo";
const TEXT_MIN_LENTH = 130;
const TEXT_MAX_LENTH = 180;
class IncallingFloatWindowTwo extends BaseLayout {
    constructor(controller, callService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();
        return;
    }
    createLayout() {
        this.mainView = LayoutManager.loadSync("MoBubbleLayout.xml");
        this._view = this.mainView;
        let screenSize = this.getScreenSize();
        this.winParam = {};
        this.winParam.height = screenSize.height;
        // must know content's length, and then re-calculate width
        this.winParam.width = screenSize.width;
        this.winParam.x = 0;
        this.winParam.y = 0;
        this.window = WindowCAF.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ? Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        this.window.specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";
        this._view.top = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_top"));
        this._view.left = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_left"));
        this._view.capInsets = [Res.getConfig("bubble_capinsets_dh1"), Res.getConfig("bubble_capinsets_dv1"),
            Res.getConfig("bubble_capinsets_dh2"), Res.getConfig("bubble_capinsets_dv2")];
        this.root = this._view.findViewById("root");
        this.icon = this._view.findViewById("icon");
        this.smallHangUp = this._view.findViewById("smallHangUp");
        this.callState = this._view.findViewById("callState");
        this.callname = this._view.findViewById("callname");
        this.info = this._view.findViewById("info");
        this.iconCar = this._view.findViewById("iconCar");
        Log.v(TAG, "IncallingFloatWindow createLayout", this.window.height, this.window.width);
        this.window.addChild(this.mainView);
        this.info.addGestureRecognizer(new TapRecognizer());
        this.smallHangUp.addGestureRecognizer(new TapRecognizer());
        this.info.on("tap", () => {
            this.switchToFullScreen();
        });
        this.callname.on("textchange", () => {
            Log.d(TAG, "callname textchange");
            this.resizeView();
        });
        this.smallHangUp.on("tap", () => {
            this.controller.closeNotificationCenter();
            var hangupType = {
                hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
                hupWay: TrackerUtil.HANGUP_WAY.CLICK
            };
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.CallResult, {
                type: "byhand", result: "hangUp"
            });
        });
        this._view.on("touchstart", (e) => {
            this.startX = e.changedTouches[0].screenX;
            this.startY = e.changedTouches[0].screenY;
            this.originX = this._view.left;
            this.originY = this._view.top;
        });
        this._view.on("touchmove", (e) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this._view.on("touchend", (e) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        return;
    }
    _updateViewPosition(touchPositionX, touchPositionY) {
        let left = touchPositionX - this.startX + this.originX;
        let top = touchPositionY - this.startY + this.originY;
        top = Math.min(Math.max(top, 0), this.window.height - this._view.height);
        left = Math.min(Math.max(left, 0), this.window.width - this._view.width);
        this._view.left = left;
        this._view.top = top;
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }
    getScreenSize() {
        return {
            width: Utils.getTotalScreenWidth(), // 获取全屏幕的宽度，横屏时也包含状态栏的宽度
            height: Utils.getScreenHeight()
        };
    }
    update(param, timecount) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }
        let callCell = param[0];
        let call = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        this.callType = call.callType;
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        }
        else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && timecount) {
                this.callState.text = Utils.getTimeLength(timecount, callCell.startCount);
            }
            else {
                this.callState.text = this.controller.callStatusToString(callCell, timecount);
            }
        }
        if (call.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
            this.iconCar.visibility = View.Visibility.Visible;
            this.icon.visibility = View.Visibility.None;
        }
        else {
            this.iconCar.visibility = View.Visibility.None;
            this.icon.visibility = View.Visibility.Visible;
        }
        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    if (this.callname) {
                        this.callname.text = name;
                    }
                }
                else {
                    this._callnameText = call.lineid;
                }
            });
        }
    }
    updateCallState(callList, curtime) {
        let callCell = callList[0];
        let call = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                }
                else {
                    this._callnameText = call.lineid;
                }
            });
            return;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
                this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
            }
            else {
                this.callState.text = this.controller.callStatusToString(callCell, curtime);
            }
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount, " this.callname.text=  this.callState.text=", this.callState.text);
    }
    hide() {
        //if (this.visible) {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
        TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CallPage, { from: this.callType === "bt" ? "bluetooth" : "carchat" });
        Log.d(TAG, "set this.visible to false");
        // }
    }
    show() {
        // if (!this.visible) {
        this.window.show();
        this.visible = true;
        Log.d(TAG, "show called, set this.visible to true");
        TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CallPage, null, {});
        // }
    }
    switchToFullScreen() {
        Log.d(TAG, "switchToFullScreen called");
        this.controller.closeNotificationCenter();
        if (this.controller.mainPage && this.controller.mainPage.destroyed === false
            && this.controller.mainPage.pageapi) {
            // main page is still alive
            Log.d(TAG, "calldisplayer main page is still alive");
            let callList = this.callService.getExistCallList();
            let call = callList[0].callinfo || callList[0];
            if (callList.length === 1 && call.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
                Log.d(TAG, "one call carchart switch fullview need to hideself and sendlink to carchart");
                this.controller.setThirdPartOutgoingCall(true);
                this.controller.sendLink2ChartCarPage();
            }
            this.controller.toggleFloatFullWindow(1); // ViewController.FULL_WINDOW);
            this.controller.sendLink2MainPage();
        }
        else {
            // should not come here
            Log.e(TAG, "why call displayer main page not alive?");
        }
    }
    onWindowTouchDown() {
    }
    onWindowTouchUp() {
        this.switchToFullScreen();
    }
    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        if (this._view) {
            this._view.top = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_top"));
            this._view.left = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_left"));
        }
        if (this.window.destroyed === false) {
            this.window.top = Utils.isLandscape() ? 0 : Utils.getPortscapeTopPos(),
                this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
        }
    }
    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        if (this.callState) {
            this.callState.text = "";
        }
        this.callname.text = "";
        this.clearPhoneNameAndNumber();
    }
    resetWidth() {
        Log.d(TAG, "resetWidth this.visible = " + this.visible);
        this.root.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_window_width_two"));
        this.info.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_text_width_two_sdp"));
        this.callname.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_text_width_two_sdp"));
        this.callState.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_text_width_two_sdp"));
    }
    clearPhoneNameAndNumber() {
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }
    resizeView() {
        let zoomWidth = 0;
        this.callname.elideMode = TextView.ElideMode.ElideNone;
        if (this.callname.contentWidth > TEXT_MAX_LENTH) {
            zoomWidth = TEXT_MAX_LENTH - TEXT_MIN_LENTH;
            this.callname.elideMode = TextView.ElideMode.ElideRight;
        }
        else if (this.callname.contentWidth > TEXT_MIN_LENTH) {
            zoomWidth = this.callname.contentWidth - TEXT_MIN_LENTH;
        }
        else {
            Log.d(TAG, "resizeView 0");
        }
        this.resetWidth();
        if (zoomWidth > 0) {
            Log.d(TAG, "zoomout");
            this.root.width += screenInstance.getPixelBySDp(zoomWidth);
            this.info.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callname.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callState.width += screenInstance.getPixelBySDp(zoomWidth);
        }
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }
}
module.exports = IncallingFloatWindowTwo;
