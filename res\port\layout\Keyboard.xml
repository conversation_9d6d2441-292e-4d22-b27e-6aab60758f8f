<CompositeView >
    <TextView id="key1" text="1#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key2" text="2#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key3" text="3#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key4" text="4#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key5" text="5#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key6" text="6#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key7" text="7#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key8" text="8#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key9" text="9#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="keyStar" text="*" width="{sdp(90)}" height="{sdp(90)}" paddingTop="{dp(10)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="key0" text="0#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
    <TextView id="keySharp" text="#" width="{sdp(90)}" height="{sdp(90)}"
        fontSize="{sdp(44)}" color="{theme.color.White_1}" multiState="{config.dailer_key_multiState}"
        align="{enum.TextView.Align.Center}" verticalAlign="{enum.TextView.VerticalAlign.Middle}" />
</CompositeView>
