
"use strict";

const path = require("path");
const rootDir = path.resolve(__dirname, "..");
process.env.APPLICATION_DIR_PATH = rootDir;

log.E("CallDisplayer", "UTIndex.js");

const utTools = require("./UTTools");
utTools.redirectRequire(new Map([
    ["yunos/telecom/InCallService", path.resolve(rootDir, "test/mock", "MockInCallService.js")]
]));
utTools.setModeName("CALL");

const Jasmine = require("/usr/lib/node_modules/jasmine");
// const Jasmine = require("./jasmine/lib/jasmine");

const MainPage = require(rootDir + "/src/index.js");
const uiInf = require("./UiInterface");
const caseList = [
    // "test/specs/test1.js",
    "test/specs/MoBasic.js",
    "test/specs/MtBasic.js",
    "test/specs/ActiveBasic.js",
    "test/specs/SecondCall.js",
    "test/specs/TwoCall.js",
    "test/specs/VoLTECall.js",
    "test/specs/CdmaCall.js"
];

function UTProxy(utPage) {
    var UBus = require("ubus");
    var busName = "com.yunos.calldisplayerutadapter";
    var busPath = "/com/yunos/calldisplayerutadapter";
    var busInterface = "com.yunos.calldisplayerutadapter.interface";
    var ubus = new UBus("dbus");
    this._iface = ubus.createInterface(busName, busPath, busInterface);
    this._iface.onBirth = function() {
        utTools.log("UTProxy onBirth");
    };
    this._iface.onDeath = function() {
        utTools.log("UTProxy onDeath");
        utPage.stopUTTest();
    };
}

UTProxy.prototype.destroyIface = function(disableCallback = true) {
    this._iface.destroy(disableCallback);
};

UTProxy.prototype.sendUTResult = function(utCompleted, result) {
    var self = this;

    return new Promise(function(resolve, reject) {
        var msg = self._iface.createMethodCallMessage("sendUTResult");
        msg.writeBool(utCompleted);
        msg.writeString(result);

        self._iface.sendMethodCallMessage(msg, function(err, reply) {
            if (err) {
                reject(err);
            } else {
                var ret = reply;
                resolve(ret);
                reply.destroy();
            }
        }, 5000);
    });
};

class UTIndex extends MainPage {
    constructor(...args) {
        super(...args);
        utTools.log("constructor");

        // this.magicNumber = 19837;
        // global.UTINSTANT.mainPage = this;
        // utTools.mainPage = this;
        this.utProxy = new UTProxy(this);
    }

    onCreate(...args) {
        super.onCreate(...args);
        utTools.log("onCreate");
    }

    onStart(...args) {
        super.onStart(...args);

        utTools.log("pagelink is:", this.sourceLink.__impl._uri);

        uiInf.setUiRoot(this);
        this._jasmine = new Jasmine({
            projectBaseDir: process.env.APPLICATION_DIR_PATH
        });

        this._jasmine.configureDefaultReporter({
            showColors: true,
            print: this.printToBuffer.bind(this),
            onComplete: this.onJasmineCompleted.bind(this)
        });

        this._jasmine.execute(caseList);
    }

    printToBuffer(content) {
        this._reporterContent += content;
    }

    onJasmineCompleted() {
        utTools.log("onJasmineCompleted");
        this.sendUTResult(true);
        this.stopUTTest();
    }

    onLink(...args) {
        super.onLink(...args);
        utTools.log("onLink");
    }

    stopUTTest() {
        utTools.log("stopUTTest");
        this.utProxy.destroyIface();
        this.stopPage();
    }

    sendUTResult(utCompleted) {
        let result = utCompleted && this._reporterContent ? this._reporterContent : "";
        utTools.log("sendUTResult, result = " + result);

        this.utProxy.sendUTResult(utCompleted, result);
    }
}

module.exports = UTIndex;
