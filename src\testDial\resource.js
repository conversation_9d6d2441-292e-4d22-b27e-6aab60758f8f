"use strict";
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
var resource = {
    drawable: function (name) {
        // return DIR_DRAWABLE + "/" + name;
        let path = Res.getImageSrc("images/" + name);
        return path;
    },
    string: function () {
        let str = Res.getString.apply(Res, arguments);
        return str;
    },
    getPixelByDp: function (dp) {
        // return screenIns.getPixelByDp(dp);
        return 1.5 * dp;
    }
};
module.exports = resource;
