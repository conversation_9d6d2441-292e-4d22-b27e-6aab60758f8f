<CompositeView id="callInfoRoot" layout="{layout.callInfoLayout}" width="{sdp(872)}" height="{sdp(350)}" >
    <CompositeView id="callInfoOne" layout="{layout.callInfoOneItemLayout}" width="{sdp(872)}" height="{sdp(350)}">
        <ImageView id="avatar" src="{img(images/qms_call_icon_portrait.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(200)}" width="{sdp(200)}"/>
        <ImageView id="avatarDing" src="{img(images/qms_call_btn_dingding_large.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(58)}" width="{sdp(58)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callname" align="{enum.TextView.Align.Center}" height="{sdp(40)}" width="{sdp(200)}" propertySetName="extend/hdt/FontTitle1" color="{theme.color.White_2}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callState" align="{enum.TextView.Align.Center}" height="{sdp(32)}" width="{sdp(200)}" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_3}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
    </CompositeView>
    <CompositeView id="callInfoTwo" layout="{layout.callInfoTwoItemLayout}"  width="{sdp(872)}" height="{sdp(350)}" visibility="{enum.View.Visibility.None}">
        <ImageView id="avatarOne" src="{img(images/qms_call_icon_portrait.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(200)}" width="{sdp(200)}"/>
        <ImageView id="avatarOneDing" src="{img(images/qms_call_btn_dingding_large.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(58)}" width="{sdp(58)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callnameOne" align="{enum.TextView.Align.Center}" height="{sdp(40)}" width="{sdp(200)}" propertySetName="extend/hdt/FontTitle1" color="{theme.color.White_2}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callStateOne" align="{enum.TextView.Align.Center}" height="{sdp(32)}" width="{sdp(200)}" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_3}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <ImageView id="avatarTwo" src="{img(images/qms_call_ic_portrait_medium.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(144)}" width="{sdp(144)}"/>
        <ImageView id="avatarTwoDing" src="{img(images/qms_call_btn_dingding_small60.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(42)}" width="{sdp(42)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callnameTwo" align="{enum.TextView.Align.Center}" height="{sdp(40)}" width="{sdp(200)}" propertySetName="extend/hdt/FontTitle3" color="{theme.color.White_4}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callStateTwo" align="{enum.TextView.Align.Center}" height="{sdp(32)}" width="{sdp(200)}" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_4}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
    </CompositeView>
</CompositeView>
