"use strict";

import Page = require("yunos/page/Page");
import CompositeView = require("yunos/ui/view/CompositeView");
import TextField = require("yunos/ui/view/TextField.js");
import TextView = require("yunos/ui/view/TextView.js");
import ImageView = require("yunos/ui/view/ImageView.js");
import View = require("yunos/ui/view/View.js");
import RelativeLayout = require("yunos/ui/layout/RelativeLayout.js");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer.js");
import LongPressRecognizer = require("yunos/ui/gesture/LongPressRecognizer.js");
import providerPackage = require("yunos/provider");
import Settings = require("yunos/content/Settings");

import TextUtils = require("./TextUtils");
import Log = require("../utils/Logs");

const TelecomManager = require("yunos/telecom/TelecomManager").getInstance();
import SpecialCharSequenceMgr = require("./SpecialCharSequenceMgr");
const DataObserver = providerPackage.DataObserver;
import DataResolver = require("yunos/provider/DataResolver");

import resource = require("./resource");

const DEBUG = true;
const TAG = "DialpadView";

interface IPage {
    isStopped: () => boolean;
}

interface Hidable {
    hidePage : () => void;
}

class DTSettingObserver extends DataObserver {
    private _dialpad: DialpadView;

    constructor(dialpad: DialpadView) {
        let dtSettingUri = Settings.System.getUriFor(Settings.System.DTMF_TONE_WHEN_DIALING);
        super(dtSettingUri);
        this._dialpad = dialpad;
    }

    onChange(uri: string) {
        if ((<IPage><object>this._dialpad.mPage).isStopped()) {
            Log.d(TAG, "DTSettingObserver.onChange: page stopped.");
            return;
        }
        Log.d(TAG, "DTSettingObserver.onChange:, uri=" + uri);
        this._dialpad.refreshDtmfEnabled();
    }
}

class DialpadView extends CompositeView {
    public mPage: Page;
    private _slot0SimOperator: string;
    private _slot1SimOperator: string;
    private activeOneSim: number;
    private _slot0PhoneState: number;
    private _slot1PhoneState: number;
    private _textInput: TextField;
    private _dtSettingObserver: DTSettingObserver;
    private _headerView: TextField;
    private maxInputTextSize: string;
    private midInputTextSize: string;
    private minInputTextSize: string;
    private _dialpadView: CompositeView;
    private _dtmfEnabled: number;
    private _simState: number;
    private mTextSelectStart: number;
    private mTextSelectEnd: number;

    constructor(context: Page) {
        super(context);
        if (DEBUG) {
            Log.d(TAG, "constructor");
        }
        this.mPage = context;
        this.initDtmfTones();
        this.onCreate();
    }

    onCreate() {
        this.background = "#000000";
        this._slot0SimOperator = "";
        this._slot1SimOperator = "";
        this.activeOneSim = 0;
        // 0: "CALL_STATE_IDLE";
        // 1: "CALL_STATE_RINGING";
        // 2: "CALL_STATE_OFFHOOK";
        this._slot0PhoneState = 0;
        this._slot1PhoneState = 0;

        this.createView();

    }

    destroy(recursive: boolean) {
        // TODO: maybe we need to unlisten the sim state and airplane mode.
        this._textInput.removeAllListeners("textchange");
        this._textInput.removeAllListeners("tap");
        this._textInput.removeAllListeners("focuschange");

        if (this._dtSettingObserver) {
            Log.d(TAG, "unregisterObserver");
            DataResolver.getInstance(this.mPage).unregisterObserver(this._dtSettingObserver, null);
            this._dtSettingObserver = null;
        }
        this._releaseTonePlayer();
        if (this.layout) {
            this.layout.destroy();
            this.layout = null;
        }
        // if (this._headerView && this._headerView.layout) {
        //     this._headerView.layout.destroy();
        //     this._headerView.layout = null;
        // }
        super.destroy(recursive);
    }

    setInputNumber(number: string) {
        if (DEBUG) {
            Log.d(TAG, "setInputNumber: number = " + number);
        }

        this.updateTextSize(true);
        this.updateTextSize(true);
    }

    setOperator(sp1: string, sp2: string) {
        this._slot0SimOperator = sp1;
        this._slot1SimOperator = sp2;
    }

    createView() {
        // 拨号盘分三部分
        // header包括输入栏和最上面的一条线还有两个按钮，高度可变
        // dialpad区包括12个按钮，基本固定不变

        this.maxInputTextSize = "36sp";
        this.midInputTextSize = "28sp";
        this.minInputTextSize = "20sp";

        this._headerView = this.createHeaderView();
        this._dialpadView = this.createDialpadView();

        this.addChild(this._headerView);
        this.addChild(this._dialpadView);

        // 由于有两部分高度可变，所以整体的高度会变化，需要计算
        this.height = this._headerView.height + this._dialpadView.height;

        var layout = new RelativeLayout();
        layout.setLayoutParam(0, "align", {top: "parent", center: "parent"});
        layout.setLayoutParam(0, "margin", {top: resource.getPixelByDp(15)});
        layout.setLayoutParam(1, "align", {top: {target: 0, side: "bottom"}, center: "parent"});
        this.layout = layout;
    }

    createHeaderView() {
        let textInput = this._textInput = new TextField();
        this._textInput.id = "dialpad_input_text";
        textInput.height = resource.getPixelByDp(50);
        textInput.width = resource.getPixelByDp(400);
        this._textInput.validator = TextUtils.PHONE_NUMBER_VALIDATOR;
        textInput.background = "#FFFFFF00";
        // this._textInput.color = "white";
        this._textInput.fontSize = "40sp";
        this._textInput.borderWidth = 0;
        this._textInput.borderRadius = 0;
        this._textInput.align = TextField.Align.Center;
        this._textInput.inputType = TextField.InputType.NotShowOnFocus;
        this._textInput.addEventListener("textchange", this.onNumberTextChanged.bind(this));
        this._textInput.addEventListener("tap", this.onNumberTap.bind(this));
        this._textInput.addEventListener("focuschange", this.onNumberFocusChanged.bind(this));

        return textInput;
    }

    createDialpadView() {
        var grid = new CompositeView();
        grid.height = resource.getPixelByDp(400);
        grid.width = resource.getPixelByDp(500);
        var cellWidth = resource.getPixelByDp(100);
        var cellHeight = resource.getPixelByDp(80);
        var row;
        var col;
        for (var i = 0; i < 12; i++) {
            // var convertView = new TextView();
            let convertView = this.createConvertView(i);
            convertView.soundEffectsEnabled = false;
            convertView.width = cellWidth;
            convertView.height = cellHeight;
            // convertView.color = "white";

            row = Math.floor(i / 3);
            col = i % 3;
            convertView.top = row * cellHeight;
            convertView.left = col * cellWidth;

            convertView.multiState = {
                normal: {background: "#FFFFFF00"},
                pressed: {background: "#FFFFFF33"}
            };
            grid.addChild(convertView);
        }
        return grid;
    }

    createConvertView(index: number) {
        var convertView = new CompositeView();
        var text = new TextView();
        text.id = "text";
        text.fontSize = "40sp";
        text.color = "white";
        convertView.addChild(text);

        var symbol = new TextView();
        symbol.id = "symbol";
        symbol.fontSize = "36sp";
        symbol.color = "white";
        convertView.addChild(symbol);

        var image = new ImageView();
        image.id = "image";
        convertView.addChild(image);

        convertView.layout = new RelativeLayout();
        convertView.layout.setLayoutParam("text", "align", {center: "parent", middle: "parent"});
        convertView.layout.setLayoutParam("symbol", "align", {left: {target: "text", side: "right"}, bottom: {target: "text", side: "bottom"}});
        convertView.layout.setLayoutParam("image", "align", {center: "parent", middle: "parent"});

        convertView.addGestureRecognizer(new TapRecognizer());
        convertView.addGestureRecognizer(new LongPressRecognizer());
        var clickText = "";
        var longClickText = "";
        switch (index) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 7:
                clickText = longClickText = String(index + 1);
                text.text = clickText;
                image.visibility = View.Visibility.None;
                symbol.visibility = View.Visibility.None;
                break;
            case 6:
                image.visibility = View.Visibility.None;
                clickText = "7";
                longClickText = "*";
                text.text = clickText;
                symbol.text = longClickText;
                break;
            case 8:
                clickText = "9";
                longClickText = "#";
                text.text = clickText;
                symbol.text = longClickText;
                image.visibility = View.Visibility.None;
                break;
            case 10:
                clickText = "0";
                longClickText = "+";
                text.text = clickText;
                symbol.text = longClickText;
                image.visibility = View.Visibility.None;
                break;
            case 9:
                text.visibility = View.Visibility.None;
                symbol.visibility = View.Visibility.None;
                image.src = resource.drawable("dial/ic_delete_button.png");
                convertView.addEventListener("tap", this.deleteInputText.bind(this, false));
                break;
            case 11:
                text.visibility = View.Visibility.None;
                symbol.visibility = View.Visibility.None;
                image.src = resource.drawable("dial/ic_callout.png");
                convertView.addEventListener("tap", this.makeCall.bind(this));
                break;
        }
        if (index !== 9/* || index !== 11*/) {
            convertView.addEventListener("tap", this.keyBtnClick.bind(this, clickText));
            convertView.addEventListener("longpress",
                this.keyBtnLongClick.bind(this, longClickText));
        }
        return convertView;
    }

    keyBtnClick(clickText: string) {
        this.playTone();
        this.updateInputText(clickText);
    }

    initDtmfTones() {
        // this._tonePlayer = null;
        this._dtmfEnabled = 1;

        this._dtSettingObserver = new DTSettingObserver(this);
        try {
            Log.d(TAG, "registerObserver");
            DataResolver.getInstance(this.mPage).registerObserver(this._dtSettingObserver, null);
        } catch (e) {
            Log.w(TAG, "initDtmfTones: error in registerObserver.", e, e.stack);
        }
        this.refreshDtmfEnabled();
    }

    playTone() {
        Log.d(TAG, "playTone: dtmf enabled=" + this._dtmfEnabled);
        // if (this._dtmfEnabled !== 0) {
        //     if (!this._tonePlayer) {
        //         const AudioManager = require("yunos/device/AudioManager");
        //         TonePlayer = require("yunos/multimedia/TonePlayer");
        //         this._tonePlayer = new TonePlayer(AudioManager.StreamType.AUDIO_STREAM_DTMF);
        //     }
        //     this._tonePlayer.play(TonePlayer.ToneType.TONE_TYPE_PROP_BEEP, 100 /* duration */);
        // }
    }

    _releaseTonePlayer() {
        // if (this._tonePlayer) {
        //     this._tonePlayer.release();
        // }
    }

    refreshDtmfEnabled() {
        Settings.System.getNumber(DataResolver.getInstance(this.mPage), Settings.System.DTMF_TONE_WHEN_DIALING, function(err: Error, val: number) {
            if (err) {
                Log.w(TAG, "refreshDtmfEnabled: error in callback.", err, err.stack);
                return;
            }
            Log.d(TAG, "refreshDtmfEnabled: val=" + val);
            this._dtmfEnabled = val;
        }.bind(this));
    }

    // 此函数会在sim卡状态变化时被调用
    updateHeaderView(state: number) {
        switch (state) {
            case 0: // 没有sim或者飞行模式需要显示提示
                // 更新header高度，输入框铺满header
                this._textInput.fontSize = this.maxInputTextSize;
                break;
            case 1:
                // 输入区
                this._textInput.fontSize = this.maxInputTextSize;
                break;
            case 2: // 两张sim，显示双卡效果
                // this._textInput.fontSize = this.maxInputTextSize;
                if (!TextUtils.isEmpty(this._textInput.text)) {
                    this.updateTextSize(true);
                }
                this._textInput.placeholder = "";
                break;
            default:
        }
    }

    // 根据sim状态刷UI
    updateDialpadLayout(state: number) {
        if (DEBUG) {
            Log.d(TAG, "updateDialpadLayout state " + state);
        }
        this._simState = state;

        this.updateHeaderView(state);
    }

    setOneActiviteSim(sim: number) {
        this.activeOneSim = sim;
    }

    // 更新输入内容
    updateInputText(addText: string) {
        if (DEBUG) {
            Log.d(TAG, "updateInputText add " + addText);
        }
        let oldWidth = this._textInput.contentWidth;
        // 如果原来就有输入，那么再次输入一个字符无需update header UI
        let needUpdate = this._textInput.text.length === 0;
        if (DEBUG) {
            Log.d(TAG, "updateInputText oldWidth = " + oldWidth + ", _textInput.text =" + this._textInput.text + ", needUpdate = " + needUpdate);
        }

        if (this.isSelectMode()) {// 选中就替换
            this.replaceText(addText);
        } else {// 非选中就插入
            var pos = this._textInput.cursorPosition;
            if (DEBUG) {
                Log.d(TAG, "updateInputText : cursor pos " + pos);
            }
            if (pos || pos === 0) {
                this._textInput.insert(pos, addText);
            } else {
                this._textInput.text = addText;
            }
        }

        let newWidth = this._textInput.contentWidth;
        if (DEBUG) {
            Log.d(TAG, "updateInputText : newWidth " + newWidth + ", oldWidth " + oldWidth);
        }
        if (oldWidth !== newWidth) {
            this.updateTextSize(oldWidth < newWidth);
        }
    }

    updateTextSize(add: boolean) {
        if (DEBUG) {
            Log.d(TAG, "updateTextSize : add " + add);
        }
    }

    replaceText(text: string) {
        let originText = this._textInput.text;
        if (TextUtils.isEmpty(originText) ||
            this.mTextSelectStart < 0 ||
            this.mTextSelectEnd > originText.length) {
            return originText;
        }
        originText = originText.substr(0, this.mTextSelectStart) +
            text + originText.substr(this.mTextSelectEnd, originText.length);
        this._textInput.text = originText;
        this._textInput.cursorPosition = this.mTextSelectStart + text.length;
    }

    deletePosText(pos: number) {
        if (pos <= 0 || pos > this._textInput.text.length) {
            return;
        }
        let originText = this._textInput.text;
        this._textInput.text = originText.substr(0, pos - 1) +
            originText.substr(pos, originText.length);
        this._textInput.cursorPosition = pos - 1;
    }

    isSelectMode() {
        this.mTextSelectStart = this._textInput.selectionStart;
        this.mTextSelectEnd = this._textInput.selectionEnd;
        if (DEBUG) {
            Log.d(TAG, "isSelectMode : " + this.mTextSelectStart +
                ", " + this.mTextSelectEnd);
        }
        return this.mTextSelectStart < this.mTextSelectEnd;
    }

    keyBtnLongClick(longClickText: string) {
        if (DEBUG) {
            Log.d(TAG, "keyBtnLongClick " + longClickText);
        }
        switch (longClickText) {
            case "1":
            case "2":
            case "3":
            case "4":
            case "5":
            case "6":
            case "7":
            case "8":
            case "9":
                // 检查快速拨号
                break;
            case "*":
            case "#":
            case "+":
                this.updateInputText(longClickText);
                break;
            default:
        }
    }

    startPhoneNumberSelectPage() {
        var PageLink = require("yunos/page/PageLink");

        var data = {
            /* jshint ignore:start */
            // jscs:disable
            action: "android.intent.action.PICK",
            pick_content: "vnd.android.cursor.dir/phone_v2"
            // jscs:enable
            /* jshint ignore:end */
        };

        let PageTransition = require("yunos/page/PageTransition");
        var link = new PageLink("page://alicontacts.yunos.com/ContactSelection");
        let transition = new PageTransition(PageTransition.TransitionType.TRANSITION_TYPE_SLIDE_IN_OUT_BOTTOM);
        link.transition = transition;
        var linkStr = JSON.stringify(data);
        link.data = linkStr;
        link.inGroup = true;
        this.mPage.sendLink(link, function(err, ret) {
            if (DEBUG) {
                Log.d(TAG, "MainView: start ContactSelection result err " +
                    err + ", ret " + ret);
            }
        });
    }

    isTwoSimState() {
        Log.d(TAG, "isTwoSimState: simState=" + this._simState);
        return this._simState === 2;
    }

    deleteInputText(all: boolean) {
        if (DEBUG) {
            Log.d(TAG, "deleteInputText: all " + all);
        }
        var inputText = this._textInput.text;
        let oldWidth = this._textInput.contentWidth;
        if (inputText && inputText.length > 0) { // 需要对非双卡UI进行调整
            if (all) {
                inputText = "";
                this._textInput.text = inputText;
            } else {
                if (this.isSelectMode()) {// 选中就全删选中部分
                    this.replaceText("");
                } else {// 非选中就删除光标前
                    var pos = this._textInput.cursorPosition;
                    if (DEBUG) {
                        Log.d(TAG, "deleteInputText : cursor pos " + pos);
                    }
                    this.deletePosText(pos);
                }
            }

        }

        let newWidth = this._textInput.contentWidth;
        if (oldWidth !== newWidth) {
            this.updateTextSize(oldWidth < newWidth);
        }
    }

    getInputNumber() {
        return this._textInput.text;
    }

    // setLatestCallLogNumberAsInputNumber() {
    //     let latestNumber = global.mainView._peoplePathView.mCallLogView.getLatestCallNumber();
    //     if (latestNumber) {
    //         this.setInputNumber(latestNumber);
    //     }
    // }

    makeCall() {
        let number = this.getInputNumber();
        Log.d(TAG, "makeCall: number=" + number);
        TelecomManager.placeCall({number: number});

        // clear inputText after call
        this.deleteInputText(true);

        (<Hidable><object>(this.mPage)).hidePage();
    }

    onNumberTap() {
        Log.d(TAG, "onNumberTap: ");
    }

    onNumberFocusChanged() {
        Log.d(TAG, "onNumberFocusChanged: ");
    }

    onNumberTextChanged(txt: string) {
        if (DEBUG) {
            Log.d(TAG, "onNumberTextChanged: txt=" + txt);
        }

        if (SpecialCharSequenceMgr.handleChars(txt)) {
            this._textInput.text = "";
            return;
        }

    }
}

export = DialpadView;
