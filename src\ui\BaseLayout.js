"use strict";
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const DialsConstants = require("../utils/DialsConstants");
const Log = require("../utils/Logs");
const TAG = "BaseLayout";
class BaseLayout {
    constructor(controller, callService) {
        this.OUTSTACK = 3;
        this.stackinfo = { status: this.OUTSTACK };
        this._view = null;
        Log.d(TAG, "constructor called");
        this.controller = controller;
        this.callService = callService;
        if (this._view && DialsConstants.Customization.ENABLE_BLUR) {
            this._view.enableBehindWindowsBlur = true;
            this._view.background = Res.getConfig("color_blur_cover");
        }
    }
}
module.exports = BaseLayout;
