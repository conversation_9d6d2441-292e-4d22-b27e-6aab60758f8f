/* 通话中，小的悬浮窗 */
"use strict";
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const LayoutManager = require("yunos/ui/markup/LayoutManager");
const WindowCAF = require("yunos/ui/view/Window");
const TextView = require("yunos/ui/view/TextView");
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const Utils = require("../utils/Utils");
const BaseLayout = require("./BaseLayout");
const CallService = require("../services/CallService");
const Log = require("../utils/Logs");
const TapRecognizer = require("yunos/ui/gesture/TapRecognizer");
const TAG = "AiHoldFloatWindow";
const TEXT_MIN_LENTH = 130;
const TEXT_MAX_LENTH = 180;
class AiHoldFloatWindow extends BaseLayout {
    constructor(controller, callService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();
        return;
    }
    createLayout() {
        this.mainView = LayoutManager.loadSync("AiHoldFloatLayout.xml");
        this._view = this.mainView;
        this.window = WindowCAF.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ? Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        this.window.specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";
        this._view.top = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_top"));
        this._view.left = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_left"));
        this._view.capInsets = [Res.getConfig("bubble_capinsets_dh1"), Res.getConfig("bubble_capinsets_dv1"),
            Res.getConfig("bubble_capinsets_dh2"), Res.getConfig("bubble_capinsets_dv2")];
        this.root = this._view.findViewById("root");
        this.callState = this._view.findViewById("callState");
        this.callname = this._view.findViewById("callname");
        this.window.addChild(this.mainView);
        this.callname.on("textchange", () => {
            Log.d(TAG, "callname textchange");
            this.resizeView();
        });
        this._view.on("touchstart", (e) => {
            this.startX = e.changedTouches[0].screenX;
            this.startY = e.changedTouches[0].screenY;
            this.originX = this._view.left;
            this.originY = this._view.top;
        });
        this._view.on("touchmove", (e) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this._view.on("touchend", (e) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this.root.addGestureRecognizer(new TapRecognizer());
        this.root.on("tap", () => {
            this.controller.showIncomingWindow(this.callService.getExistCallList());
        });
        return;
    }
    _updateViewPosition(touchPositionX, touchPositionY) {
        let left = touchPositionX - this.startX + this.originX;
        let top = touchPositionY - this.startY + this.originY;
        top = Math.min(Math.max(top, 0), this.window.height - this._view.height);
        left = Math.min(Math.max(left, 0), this.window.width - this._view.width);
        this._view.left = left;
        this._view.top = top;
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }
    update(param, timecount) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }
        let callCell = param[0];
        let call = callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        }
        else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && timecount) {
                this.callState.text = Utils.getTimeLength(timecount, callCell.startCount);
            }
            else {
                this.callState.text = this.controller.callStatusToString(callCell, timecount);
            }
        }
        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    if (this.callname) {
                        this.callname.text = name;
                    }
                }
                else {
                    this._callnameText = call.lineid;
                }
            });
        }
    }
    updateCallState(callList, curtime) {
        let callCell = callList[0];
        let call = callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                }
                else {
                    this._callnameText = call.lineid;
                }
            });
            return;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
                this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
            }
            else {
                this.callState.text = this.controller.callStatusToString(callCell, curtime);
            }
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount, " this.callname.text=  this.callState.text=", this.callState.text);
    }
    hide() {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
    }
    show() {
        this.window.show();
        this.visible = true;
        Log.d(TAG, "show called, set this.visible to true");
    }
    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        if (this._view) {
            this._view.top = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_top"));
            this._view.left = screenInstance.getPixelBySDp(Res.getConfig("bubble_margin_left"));
        }
        if (this.window) {
            this.window.top = Utils.isLandscape() ? 0 : Utils.getPortscapeTopPos();
            this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
        }
    }
    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        if (this.callState) {
            this.callState.text = "";
        }
        this.callname.text = "";
        this.clearPhoneNameAndNumber();
    }
    resetWidth() {
        Log.d(TAG, "resetWidth this.visible = " + this.visible);
        this.root.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_window_width_two"));
        this.callname.width = screenInstance.getPixelBySDp(Res.getConfig("mo_float_text_width_two_sdp"));
    }
    clearPhoneNameAndNumber() {
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }
    resizeView() {
        let zoomWidth = 0;
        this.callname.elideMode = TextView.ElideMode.ElideNone;
        if (this.callname.contentWidth > TEXT_MAX_LENTH) {
            zoomWidth = TEXT_MAX_LENTH - TEXT_MIN_LENTH;
            this.callname.elideMode = TextView.ElideMode.ElideRight;
        }
        else if (this.callname.contentWidth > TEXT_MIN_LENTH) {
            zoomWidth = this.callname.contentWidth - TEXT_MIN_LENTH;
        }
        else {
            Log.d(TAG, "resizeView 0");
        }
        this.resetWidth();
        if (zoomWidth > 0) {
            Log.d(TAG, "zoomout");
            this.root.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callname.width += screenInstance.getPixelBySDp(zoomWidth);
        }
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }
}
module.exports = AiHoldFloatWindow;
