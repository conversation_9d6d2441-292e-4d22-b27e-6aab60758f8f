"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");
const TouchEvent = require("yunos/ui/event/TouchEvent");
const View = require("yunos/ui/view/View");

const callANumber = "15811111111";
const callBNumber = "15800000000";

const dialingCallA = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 2
};

const activeCallA = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 384,
    _phoneType: 2
};

const hangupCallA = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: 2,
    _capability: 384,
    _phoneType: 2
};

const dialingCallB = {
    _subId: 0,
    _callId: 217,
    _state: 3,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 2
};

const waitingCallB = {
    _subId: 0,
    _callId: 217,
    _state: 6,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2253673,
    _phoneType: 2
};

const activeCallB = {
    _subId: 0,
    _callId: 217,
    _state: 1,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 384,
    _phoneType: 2
};

const hangupCallB = {
    _subId: 0,
    _callId: 217,
    _state: 7,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 384,
    _phoneType: 2
};

const touchup = new TouchEvent({
    type: "touchend",
    changedTouches: [{
        clientX: 100,
        clientY: 10,
        identifier: 0
    }],
    eventPhase: TouchEvent.EventPhase.Target
});

describe("CdmaCall", function() {
    utTools.log("CdmaCall start");

    var originalTimeout;

    beforeAll(() => {
        utTools.log("CdmaCall beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("CdmaCall afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("CdmaCall beforeEach");

        // call a dialing -> call a active
        utTools.asyncRun(done, function *() {
            utTools.log("CdmaCall beforeEach run");
            utTools.MockInCallService.emit("calladded", dialingCallA);
            yield 3000;
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), moLayout);

            utTools.MockInCallService.emit("callstatechanged", activeCallA);
            yield 3000;
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
        }());
    });

    afterEach((done) => {
        utTools.log("CdmaCall afterEach");

        // call a disconnect
        utTools.asyncRun(done, function *() {
            utTools.log("CdmaCall afterEach run");
            utTools.MockInCallService.emit("callstatechanged", hangupCallA);
            yield 3000;
            const controler = uiInf.getControler();
            utTools.equal(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
        }());
    });

    // call a active -> call a hold -> call a unhold -> call a disconnect
    it("hold", function(done) {
        utTools.log("CdmaCall hold in");
        utTools.asyncRun(done, function *() {
            utTools.log("CdmaCall hold run");
            const moLayout = uiInf.getMOLayout();
            moLayout.holdCall.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.holdCall.selected, true);

            moLayout.holdCall.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.holdCall.selected, false);
        }());
    });

    // call a active -> call b waiting -> call b active -> call a disconnect
    it("twocalls", function(done) {
        utTools.log("CdmaCall twocalls in");
        utTools.asyncRun(done, function *() {
            // waiting call
            utTools.log("CdmaCall twocalls run");
            utTools.MockInCallService.emit("callstatechanged", waitingCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activeCallB);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.equal(moLayout.call1ActionImg.visibility, View.Visibility.Hidden);
            utTools.equal(moLayout.call1Number.text, activeCallA._callNumber);
            utTools.equal(moLayout.convertCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.convertCall.enabled, true);
            utTools.strEqual(moLayout.convertCall.text, "BTN_FLASH");
            utTools.equal(moLayout.mergeCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.mergeCall.enabled, false);

            utTools.MockInCallService.emit("callstatechanged", hangupCallB);
        }());
    });

    // call a active -> call b dialing -> call b active -> merge to conference -> disconnect
    it("conferencecall", function(done) {
        utTools.log("CdmaCall conferencecall in");
        utTools.asyncRun(done, function *() {
            utTools.log("CdmaCall conferencecall run");
            utTools.MockInCallService.emit("calladded", dialingCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activeCallB);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.convertCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.convertCall.enabled, false);
            utTools.equal(moLayout.mergeCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.mergeCall.enabled, true);

            moLayout.mergeCall.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(moLayout.number.text, "");
            utTools.equal(moLayout.area.text, "");
            utTools.strEqual(moLayout.callname.text, "TEXT_CDMA_MULTI_CALL");
            utTools.equal(moLayout.addCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.convertCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.convertCall.enabled, true);
            utTools.equal(moLayout.holdCall.visibility, View.Visibility.None);
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.None);
            utTools.equal(moLayout.phoneInfoLayout.visibility, View.Visibility.Visible);

            moLayout.hangUp.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", hangupCallB);
        }());
    });
});
