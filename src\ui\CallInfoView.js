/* 通话中，全屏界面中的头像、联系人姓名、状态等item的view */
"use strict";
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const CompositeView = require("yunos/ui/view/CompositeView");
const ColumnLayout = require("yunos/ui/layout/ColumnLayout");
const TextView = require("yunos/ui/view/TextView");
const ImageView = require("yunos/ui/view/ImageView");
const Log = require("../utils/Logs");
const TAG = "CallInfoView";
class CallInfoView extends CompositeView {
    constructor() {
        super();
        Log.d(TAG, "constructor");
        this._avatar = new ImageView();
        this._avatar.id = "_avatar";
        this._avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
        this._avatar.width = screenInstance.getPixelByDp(Res.getConfig("callinfo_avatar_width"));
        this._avatar.height = screenInstance.getPixelByDp(Res.getConfig("callinfo_avatar_width"));
        this._avatar.borderRadius = screenInstance.getPixelByDp(Res.getConfig("callinfo_avatar_width") / 2);
        this._avatar.scaleType = ImageView.ScaleType.Fitxy;
        this._callname = new TextView();
        this._callname.id = "_callname";
        this._callname.fontSize = Res.getConfig("callinfo_name_text_size");
        this._callname.color = "#FFFFFF";
        this._callname.align = TextView.Align.Center;
        this._callname.multiLine = false;
        this._callname.maxLineCount = 1;
        this._callname.elideMode = TextView.ElideMode.ElideRight;
        this._callState = new TextView();
        this._callState.id = "_callState";
        this._callState.fontSize = Res.getConfig("callinfo_state_text_size");
        this._callState.color = "#FFFFFF";
        this._callState.opacity = 0.6;
        this.addChild(this._avatar);
        this.addChild(this._callname);
        this.addChild(this._callState);
        let layout = new ColumnLayout();
        layout.defaultLayoutParam = { align: ColumnLayout.Align.Center };
        layout.wrapContent = true;
        layout.setLayoutParam(0, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(1, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(1, "margin", { top: screenInstance.getPixelBySDp(Res.getConfig("callinfo_callname_margin_top")) });
        layout.setLayoutParam(2, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(2, "margin", { top: screenInstance.getPixelBySDp(Res.getConfig("callinfo_callstatus_margin_top")) });
        this.layout = layout;
    }
    set avatar(icon) {
        Log.v(TAG, "set avatar icon:", icon);
        if (this._avatar) {
            this._avatar.src = icon;
        }
        else {
            Log.d(TAG, "set avatar: not init...");
        }
    }
    get avatar() {
        return this._avatar.src;
    }
    set callname(name) {
        Log.v(TAG, "set callname name:", name);
        if (this._callname) {
            this._callname.text = name;
        }
        else {
            Log.d(TAG, "set callname: not init...");
        }
    }
    get callname() {
        return this._callname.text;
    }
    set callState(state) {
        Log.v(TAG, "set callState state:", state);
        if (this._callState && this._callState.hasOwnProperty("_text")) {
            this._callState.text = state;
        }
        else {
            Log.d(TAG, "set callState: not init...");
        }
    }
    get callState() {
        return this._callState.text;
    }
    getWidth() {
        let callnameWidth = 0;
        let callStateWidth = 0;
        let avatarWidth = 0;
        if (this._callname && this._callname.text) {
            callnameWidth = this._callname.width;
        }
        if (this._callState && this._callState.text) {
            callStateWidth = this._callState.width;
        }
        if (this._avatar && this._avatar.src) {
            avatarWidth = screenInstance.getPixelByDp(Res.getConfig("callinfo_avatar_width"));
        }
        Log.d(TAG, "getWidth:", avatarWidth, callnameWidth, callStateWidth);
        return Math.max(callnameWidth, callStateWidth, avatarWidth);
    }
    setCallnameWidth(width) {
        this._callname.width = width;
    }
    setForegroundState(isFrontCall) {
        if (isFrontCall) {
            this._avatar.opacity = 1;
            this._callname.opacity = 1;
            this._callState.opacity = 0.6;
        }
        else {
            this._avatar.opacity = 0.4;
            this._callname.opacity = 0.4;
            this._callState.opacity = 0.4;
        }
    }
    resetUIContent() {
        if (this._avatar) {
            this._avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
        }
        if (this._callname) {
            this._callname.text = "";
        }
        if (this._callState) {
            this._callState.text = "";
        }
    }
}
module.exports = CallInfoView;
