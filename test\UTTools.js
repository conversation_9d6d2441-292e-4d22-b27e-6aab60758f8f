"use strict";
const Module = require("module");

class UTTools {
    static getInstance() {
        if (!UTTools._inst) {
            UTTools._inst = new UTTools();
        }

        return UTTools._inst;
    }

    constructor() {
        this.TAG = "[UT]";
    }


    redirectRequire(mockList) {
        if (this.stdRequire) {
            return;
        }

        this.stdRequire = Module.prototype.require;

        // this.log("stdRequire: ", this.stdRequire);

        const self = this;

        self.log("need redirect: ", mockList);

        Module.prototype.require = function(modulePath) {
            const newModulePath = mockList.get(modulePath);

            if (!newModulePath) {
                // self.log("stdRequire: ", modulePath);
                return self.stdRequire.call(this, modulePath);
            } else {
                self.log(modulePath, "is mocked: ", newModulePath);
                return self.stdRequire.call(this, newModulePath);
            }
        };
    }

    setModeName(moduleName) {
        if (moduleName) {
            this.TAG += "_" + moduleName;
        }
    }

    log(...args) {
        log.E(this.TAG, ...args);
    }

    asyncRun(done, go) {
        const ret = go.next();

        if (ret.done) {
            done();
            return;
        }

        if (typeof ret.value === "number") {
            setTimeout(() => {
                this.asyncRun(done, go);
            }, ret.value);
            return;
        }

        this.log(ret.value);
        return this.asyncRun(done, go);
    }

    equal(left, right) {
        expect(left).toBe(right);
    }

    strEqual(left, right) {
        expect(left).toBe(global.res.getString(right));
    }

    viewEqual(left, right) {
        if (left !== right) {
            this.log("is not equal real: " + left + " hope: " + right);
            let error = new Error("is not equal real: " + left + " hope: " + right);
            this.log("UT item fail: ", error.stack);
            expect(true).toBe(false);
        }
    }

    notEqual(left, right) {
        expect(left).not.toBe(right);
    }

    pattern(pattern, val) {
        if (!pattern.test(val)) {
            expect(pattern).toBe(val);
        }
    }

    ObjectEqual(left, right) {
        left = JSON.stringify(left);
        right = JSON.stringify(right);
        expect(left).toBe(right);
    }

    setRecordPermission(allowed) {
        var fsContext = require("yunos/cloudfs/Context").getInstance();
        var filePath = fsContext.getLocalDir() + "/preference.txt";

        if (allowed) {
            var fs = require("fs");
            try {
                fs.writeFileSync(filePath, JSON.stringify({"media_recorder": true}), "utf8");
            } catch (err) {
                this.log("setRecordPermission, writeFileSync fail err = " + err);
            }
        } else {
            var File = require("yunos/cloudfs/File");
            var fileRemoveSync = new File(filePath);
            try {
                var ret = fileRemoveSync.removeSync();
                this.log("setRecordPermission, removeSync result = " + ret);
            } catch (err) {
                this.log("setRecordPermission, removeSync faile err = " + err);
            }
        }
    }
}

module.exports = UTTools.getInstance();
