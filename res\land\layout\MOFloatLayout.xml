<CompositeView id="root"  layout="{layout.moFloatLayout}"
        background="{img(images/qms_tip_bg_dialing_normal.png)}" capInsets="{config.mo_float_window_bg_capinset}">
    <ImageView id="icon" src="{img(images/qms_tip_ic_calling.png)}"/>
    <TextView id="callname" fontSize="{config.mo_float_window_text_size}" color="#FFFFFF"
        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
    <TextView id="callState" fontSize="{config.mo_float_window_text_size}" color="#FFFFFF"/>
</CompositeView>
