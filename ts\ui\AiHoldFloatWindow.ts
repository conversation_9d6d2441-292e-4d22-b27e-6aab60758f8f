/* 通话中，小的悬浮窗 */
"use strict";

import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import WindowCAF = require("yunos/ui/view/Window");
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import TouchEvent = require("yunos/ui/event/TouchEvent");
import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();

import Utils = require("../utils/Utils");
import BaseLayout = require("./BaseLayout");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import Log = require("../utils/Logs");
import View = require("yunos/ui/view/View");
import DialsConstants = require("../utils/DialsConstants");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer");

const TAG = "AiHoldFloatWindow";
const TEXT_MIN_LENTH = 130;
const TEXT_MAX_LENTH = 180;

interface IMyWindow {
    specifiedMode: number;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class AiHoldFloatWindow extends BaseLayout {
    public visible: boolean;
    private mainView: CompositeView;
    private window: WindowCAF;
    private root: CompositeView;
    private callname: TextView;
    private callState: TextView;
    private _callnameText: string;
    private _nameUpdatedNumber: string;
    private startX: number;
    private startY: number;
    private originX: number;
    private originY: number;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();

        return;
    }

    createLayout() {
        this.mainView = <CompositeView> LayoutManager.loadSync("AiHoldFloatLayout.xml");
        this._view = this.mainView;

        this.window = <WindowCAF> WindowCAF.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ? Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        (<IMyWindow><object> this.window).specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";

        this._view.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
        this._view.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        this._view.capInsets = [<number>Res.getConfig("bubble_capinsets_dh1"), <number>Res.getConfig("bubble_capinsets_dv1"),
                                <number>Res.getConfig("bubble_capinsets_dh2"), <number>Res.getConfig("bubble_capinsets_dv2")];

        this.root = <CompositeView> this._view.findViewById("root");
        this.callState = <TextView> this._view.findViewById("callState");
        this.callname = <TextView> this._view.findViewById("callname");

        this.window.addChild(this.mainView);

        this.callname.on("textchange", ()=> {
            Log.d(TAG, "callname textchange");
            this.resizeView();
        });

        this._view.on("touchstart", (e: TouchEvent) => {
            this.startX = e.changedTouches[0].screenX;
            this.startY = e.changedTouches[0].screenY;
            this.originX = this._view.left;
            this.originY = this._view.top;
        });
        this._view.on("touchmove", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this._view.on("touchend", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this.root.addGestureRecognizer(new TapRecognizer());
        this.root.on("tap", ()=> {
            this.controller.showIncomingWindow(this.callService.getExistCallList());
        });

        return;
    }

    _updateViewPosition(touchPositionX: number, touchPositionY: number) {
        let left = touchPositionX - this.startX + this.originX;
        let top = touchPositionY - this.startY + this.originY;
        top = Math.min(Math.max(top, 0), this.window.height - this._view.height);
        left = Math.min(Math.max(left, 0), this.window.width - this._view.width);
        this._view.left = left;
        this._view.top = top;
        this.window.setInputRegion(this._view.left, this._view.top,
            this._view.width, this._view.height);
    }

    update(param: Array<IMyCallCell>, timecount: number) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }

        let callCell = param[0];
        let call: IMyCallCell = callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        } else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && timecount) {
                this.callState.text = Utils.getTimeLength(timecount, callCell.startCount);
            } else {
                this.callState.text = this.controller.callStatusToString(callCell, timecount);
            }
        }

        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;

        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    if (this.callname) {
                        this.callname.text = name;
                    }
                } else {
                    this._callnameText = call.lineid;
                }
            });
        }
    }

    updateCallState(callList: Array<IMyCallCell>, curtime: number) {
        let callCell = callList[0];
        let call: IMyCallCell = callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);

        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                } else {
                    this._callnameText = call.lineid;
                }
            });
            return;
        }

        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
                this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
            } else {
                this.callState.text = this.controller.callStatusToString(callCell, curtime);
            }
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount,
            " this.callname.text=  this.callState.text=", this.callState.text);
    }

    hide() {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
    }

    show() {
        this.window.show();
        this.visible = true;
        Log.d(TAG, "show called, set this.visible to true");
    }

    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        if (this._view) {
            this._view.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
            this._view.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        }
        if (this.window) {
            this.window.top = Utils.isLandscape() ? 0 : Utils.getPortscapeTopPos();
            this.window.setInputRegion(this._view.left, this._view.top,
                this._view.width, this._view.height);
        }
    }

    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        if (this.callState) {
            this.callState.text = "";
        }
        this.callname.text = "";
        this.clearPhoneNameAndNumber();
    }

    resetWidth() {
        Log.d(TAG, "resetWidth this.visible = " + this.visible);
        this.root.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_window_width_two"));
        this.callname.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_text_width_two_sdp"));
    }

    clearPhoneNameAndNumber() {
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }

    resizeView() {
        let zoomWidth = 0;
        this.callname.elideMode = TextView.ElideMode.ElideNone;
        if (this.callname.contentWidth > TEXT_MAX_LENTH) {
            zoomWidth = TEXT_MAX_LENTH - TEXT_MIN_LENTH;
            this.callname.elideMode = TextView.ElideMode.ElideRight;
        } else if (this.callname.contentWidth > TEXT_MIN_LENTH) {
            zoomWidth = this.callname.contentWidth - TEXT_MIN_LENTH;
        } else {
            Log.d(TAG, "resizeView 0");
        }
        this.resetWidth();
        if (zoomWidth > 0) {
            Log.d(TAG, "zoomout");
            this.root.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callname.width += screenInstance.getPixelBySDp(zoomWidth);
        }
        this.window.setInputRegion(this._view.left, this._view.top,
            this._view.width, this._view.height);
    }

}

export = AiHoldFloatWindow;
