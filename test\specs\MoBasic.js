"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");

const addCall = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 1
};

// huangup call
const huangupcall = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const ringcall = {
    _subId: 0,
    _callId: 1,
    _state: 4,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const activecall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

describe("MoBasic", function() {
    utTools.log("MoBasic start");

    var originalTimeout;

    beforeAll(() => {
        utTools.log("MoBasic beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("MoBasic afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("MoBasic beforeEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("calladded", addCall);
            yield 3000;
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), moLayout);
        }());
    });

    afterEach((done) => {
        utTools.log("MoBasic afterEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            const controler = uiInf.getControler();
            utTools.equal(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
            yield 3000;
        }());
    });

    it("dialing", function(done) {
        utTools.log("MoBasic dialing in");
        utTools.asyncRun(done, function *() {
            utTools.log("MoBasic dialing run");
            yield 3000; // as the callname value from qiandun sometimes need a long time
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.number.text, addCall._callNumber);
            utTools.equal(moLayout.callname.text, String.fromCodePoint(0x4e2d, 0x56fd, 0x79fb, 0x52a8)); // ChinaMobile
            // utTools.equal(moLayout.avatar.src, global.res.getImageSrc("images/ic_avatar_none.png"));
            utTools.equal(moLayout.callState.text, global.res.getString("TEXT_DIALING"));
            yield 3000;
        }());
    });

    it("ringing", function(done) {
        utTools.log("MoBasic ringing in");
        utTools.asyncRun(done, function *() {
            utTools.log("MoBasic ringing run");
            utTools.MockInCallService.emit("callstatechanged", ringcall);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.equal(moLayout.callState.text, global.res.getString("TEXT_RINGING"));
            yield 3000;
        }());
    });

    it("active", function(done) {
        utTools.log("MoBasic active in");
        utTools.asyncRun(done, function *() {
            utTools.log("MoBasic active run");
            const moLayout = uiInf.getMOLayout();

            utTools.equal(moLayout.addCall.enabled, false);
            utTools.equal(moLayout.holdCall.enabled, false);
            utTools.equal(moLayout.facetime.enabled, false);
            utTools.equal(moLayout.record.enabled, false);
            utTools.equal(moLayout.keyboard.enabled, true);
            utTools.equal(moLayout.contacts.enabled, false);

            utTools.MockInCallService.emit("callstatechanged", ringcall);
            yield 3000;
            utTools.equal(moLayout.addCall.enabled, false);
            utTools.equal(moLayout.holdCall.enabled, false);
            utTools.equal(moLayout.facetime.enabled, false);
            utTools.equal(moLayout.record.enabled, false);
            utTools.equal(moLayout.keyboard.enabled, true);
            utTools.equal(moLayout.contacts.enabled, false);

            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            utTools.equal(moLayout.addCall.enabled, true);
            utTools.equal(moLayout.holdCall.enabled, true);
            utTools.equal(moLayout.facetime.enabled, true);
            utTools.equal(moLayout.record.enabled, true);
            utTools.equal(moLayout.keyboard.enabled, true);
            utTools.equal(moLayout.contacts.enabled, true);
        }());
    });
});
