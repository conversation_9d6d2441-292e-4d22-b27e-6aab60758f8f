"use strict";

import Page = require("yunos/page/Page");
import Screen = require("yunos/device/Screen");
const screen = Screen.getInstance();
import SettingsDB = require("yunos/content/Settings");
import DataResolver = require("yunos/provider/DataResolver");
import AudioManager = require("yunos/device/AudioManager");
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import Settings = require("yunos/content/Settings");

import Log = require("./Logs");
const TAG = "Utils";

const PROP_YUNOS_SUPPORT_CTA = "ro.yunos.support.cta";
const YES = "yes";

const ZH_DIGITS = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
const ZERO_ASCII = 48;
const NINE_ASCII = 57;

const MOBILE_NOTI_REGEX = "^106(5|9)[0-9]{4}[0-9]*|^95[0-9]{3}[0-9]*|^100[0-9]{2}[0-9]*|^12(1|3)[0-9]{2}[0-9]*|^125200[0-9]*";
const MobilePattern = new RegExp(MOBILE_NOTI_REGEX);
const SCREEN_OFF_PROP = "persistent.mode.screen_off";

let statusBarHeight: number = 0;
let screenWidth: number = 0;
let screenHeight: number = 0;
let totalScreenWidth: number = 0;
let totalScreenHeight: number = 0;
let landscape: boolean;
let hasSession = false;
let screenHeightWithMenu: number = 0;
let menuWidth: number = 0;

interface ICall {
    status?: number;
    subid?: number;
    callid?: number;
    phoneType?: number;
    capability?: number;
    disconnectReason?: number;
}

class Utils {
    static readonly PHONE_TYPE_NONE = 0;
    static readonly PHONE_TYPE_GSM = 1;
    static readonly PHONE_TYPE_CDMA = 2;
    static readonly PHONE_TYPE_IMS = 3;
    static readonly PHONE_TYPE_SIP = 4;

    static supportViLTECall() {
        // support video over lte
        return false;
    }

    static isScrrenOff(){
        return false;
    }

    static supportConferenceExt() {
        // can select multi contact first and call together
        return false;
    }

    static supportGesture() {
        let gesture = Settings.Secure.getNumberSync(DataResolver.getInstance(), "context_motion_enable", 0);
        Log.i(TAG, "support gesture result: ", gesture);
        return gesture === 1;
    }

    static isVoLTECall(call: ICall) {
        if (call && call.phoneType &&
                call.phoneType === Utils.PHONE_TYPE_IMS) {
            return true;
        }
        if (call && call.capability) {
            // TODO: return call.capability & Call.CAPABILITY_HD !== 0;
            return false;
        }
        return false;
    }

    static isCDMACall(call: ICall) {
        if (call && call.phoneType &&
                call.phoneType === Utils.PHONE_TYPE_CDMA) {
            return true;
        }
        return false;
    }

    static getString() {
        let str = Res.getString.apply(Res, arguments);
        return str;
    }

    static isSupportCta() {
        return false;
    }

    static getTimeLength(timecount: number, startCount: number, isRound: boolean = true) {
        timecount -= startCount;
        if (isRound) {
            timecount = Math.round(timecount);
        } else {
            timecount = Math.ceil(timecount);
        }
        if (timecount < 0) {
            Log.e(TAG, "getTimeLength: timecount is: " + timecount);
            timecount = 0;
        }
        let min = timecount / 60;
        let second = timecount % 60;
        let time = "";

        if (min < 10) {
            time += "0" + Math.floor(min);
        } else {
            time += Math.floor(min);
        }
        time += ":";

        if (second < 10) {
            time += "0" + Math.floor(second);
        } else {
            time += Math.floor(second);
        }
        return time;
    }

    static getTimeLengthForInt(timecount: number, startCount: number, isRound: boolean = true) {
        timecount -= startCount;
        if (isRound) {
            timecount = Math.round(timecount);
        } else {
            timecount = Math.ceil(timecount);
        }
        return timecount;
    }

    static removeAllSpace(str: string) {
        return str.replace(/\s+/g, "");
    }

    static dismissKeyguard() {
        let systemService = require("core/system_service");
        let keyguardServiceProxy = systemService.getService("keyguardservice");
        let self = this;
        Log.d(TAG, "dismiss() called");
        keyguardServiceProxy.dismiss().then(
            function() {
                Log.d(TAG, "dismiss() - OK");
            },
            function(error: Error) {
                Log.d(TAG, "dismiss() err:", error);
            }
        );
    }

    static trimSpace(str: string) {
        if (!str) {
            return str;
        }
        return str.replace(new RegExp("\\s+", "gm"), "");
    }

    static isPhoneNumber(number: string) {
        if (!number) {
            return false;
        }
        return /^\+?[-,;*#0-9]*$/.test(number);
    }

    static isCnapSpecialCaseRestricted(number: string) {
        if (!number) {
            return false;
        }
        return number.toUpperCase() === "PRIVATE" ||
            number.toUpperCase() === "P" ||
            number.toUpperCase() === "RES";
    }

    static isCnapSpecialCaseUnknown(number: string) {
        if (!number) {
            return false;
        }
        return number.toUpperCase() === "UNAVAILABLE" ||
            number.toUpperCase() === "UNKNOWN" ||
            number.toUpperCase() === "UNA" ||
            number.toUpperCase() === "U";
    }

    static containsAbsentNumber(number: string) {
        if (!number) {
            return false;
        }
        return number.toUpperCase().indexOf("ABSENT NUMBER") >= 0 ||
            number.toUpperCase().indexOf("ABSENTNUMBER") >= 0;
    }

    static getNameForSpecialCnapCases(number: string) {
        if (!number) {
            return Res.getString("TEXT_UNKNOWN_NUMBER");
        }
        let phoneNumber = null;
        if (this.containsAbsentNumber(number)) {
            phoneNumber = Res.getString("TEXT_UNKNOWN_NUMBER");
        } else {
            if (this.isCnapSpecialCaseRestricted(number)) {
                phoneNumber = Res.getString("TEXT_PRIVATE_NUMBER");
            } else if (this.isCnapSpecialCaseUnknown(number)) {
                phoneNumber = Res.getString("TEXT_UNKNOWN_NUMBER");
            }
        }
        return phoneNumber;
    }

    static desensitizeNumber(number: string) {
        if (!number) {
            return "[empty]";
        }
        let len = number.length;
        if (len <= 4) {
            return number;
        }
        return "**" + number.substring(len - 4, len);
    }

    static getStatusBarHeight() {
        if (!statusBarHeight) {
            statusBarHeight = parseInt("NAN");
            if (isNaN(statusBarHeight) || typeof statusBarHeight !== "number") {
                statusBarHeight = <number>Res.getConfig("status_bar_height");
            }
            statusBarHeight = screen.getPixelBySDp(statusBarHeight);
        }
        Log.v(TAG, "getStatusBarHeight: ", statusBarHeight);
        return statusBarHeight;
    }

    static getMenuWidth() {
        if (!menuWidth) {
            menuWidth = parseInt("NAN");
            if (isNaN(menuWidth) || typeof menuWidth !== "number") {
                menuWidth = <number>Res.getConfig("root_menu_width");
            }
            menuWidth = screen.getPixelBySDp(menuWidth);
        }
        Log.v(TAG, "getMenuWidth: ", menuWidth);
        return menuWidth;
    }

    static getScreenWidth() {
        if (!screenWidth) {
            screenWidth = Utils.getTotalScreenWidth();
            if (Utils.isLandscape()) {
                screenWidth -= Utils.getMenuWidth();
            }
        }
        Log.v(TAG, "getScreenWidth: ", screenWidth);
        return screenWidth;
    }

    static getTotalScreenWidth() {
        if (!totalScreenWidth) {
            totalScreenWidth = screen.resolution.width;
            if (typeof totalScreenWidth !== "number") {
                totalScreenWidth = <number>Res.getConfig("screen_width");
            }
        }

        return totalScreenWidth;
    }

    static getScreenHeight() {
        if (!screenHeight) {
            screenHeight = Utils.getTotalScreenHeight();
            if (!Utils.isLandscape()) {
                screenHeight -= Utils.getStatusBarHeight() + <number>Res.getConfig("root_menu_heiht");
            }
            screenHeight = screenHeight;
        }
        Log.v(TAG, "getScreenHeight: ", screenHeight);
        return screenHeight;
    }

    static getScreenHeightWithoutStatusBar() {
        if (!screenHeight) {
            screenHeight = Utils.getTotalScreenHeight();
            screenHeight -= Utils.getStatusBarHeight();
        }
        Log.v(TAG, "getScreenHeight: ", screenHeight);
        return screenHeight;
    }

    static getScreenHeightWithMenu() {
        if (!screenHeightWithMenu) {
            screenHeightWithMenu = Utils.getTotalScreenHeight();
            if (!Utils.isLandscape()) {
                screenHeightWithMenu -= Utils.getStatusBarHeight();
            }
        }
        Log.v(TAG, "screenHeightWithMenu: ", screenHeightWithMenu);
        return screenHeightWithMenu;
    }

    static getTotalScreenHeight() {
        if (!totalScreenHeight) {
            totalScreenHeight = screen.resolution.height;
            if (typeof totalScreenHeight !== "number") {
                totalScreenHeight = <number>Res.getConfig("screen_height");
            }
        }

        return totalScreenHeight;
    }

    static isLandscape() {
        if (!landscape) {
            landscape = <boolean>Res.getConfig("TEST_FORCE_LANDSCAPE") ||
                Utils.getTotalScreenWidth() > Utils.getTotalScreenHeight();
        }
        return landscape;
    }

    // "ab12+1234" => "ab一二+一二三四"
    static digitsToZh(digits: string) {
        if (typeof digits !== "string") {
            Log.e(TAG, "digitsToZh, wrong type " + typeof digits);
            return "";
        }

        let result = "";
        for (let i = 0; i < digits.length; i++) {
            let asciiCode = digits.charCodeAt(i);
            if (asciiCode >= ZERO_ASCII && asciiCode <= NINE_ASCII) {
                result += ZH_DIGITS[Number(digits.charAt(i))];
            } else {
                result += digits.charAt(i);
            }
        }

        return result;
    }

    static getXiaoyunEnable(context: Page, callback: (enable: boolean) => void) {
        const dataResolver = DataResolver.getInstance(context);
        SettingsDB.Secure.getNumberWithDefault(dataResolver,
            SettingsDB.Secure.VOICE_TIPS_ENABLE, 1,
            (value) => {
                Log.d(TAG, "getXiaoyunEnable, enabled = " + value);
                if (callback) {
                    callback(Boolean(value));
                }
            }
        );
    }

    static getXiaoyunEnableSync(context: Page) {
        const dataResolver = DataResolver.getInstance(context);
        return SettingsDB.Secure.getNumberSync(dataResolver,
            SettingsDB.Secure.VOICE_TIPS_ENABLE, 1);
    }

    static setXiaoyunEnable(context: Page, enable: boolean, callback: (error: Error) => void) {
        const dataResolver = DataResolver.getInstance(context);
        SettingsDB.Secure.putNumber(dataResolver,
            SettingsDB.Secure.VOICE_TIPS_ENABLE, Number(enable),
            (err) => {
                Log.d(TAG, "setXiaoyunEnable, set xiaoyun " + enable + ", result = " + err);
                if (callback) {
                    callback(err);
                }
            }
        );
    }

    static setXiaoyunEnableSync(context: Page, enable: boolean) {
        const dataResolver = DataResolver.getInstance(context);
        SettingsDB.Secure.putNumberSync(dataResolver,
            SettingsDB.Secure.VOICE_TIPS_ENABLE, Number(enable));
        Log.d(TAG, "setXiaoyunEnableSync, set xiaoyun " + enable);
    }

    static requestAudioSession() {
        try {
            if (hasSession) {
                return;
            }
            let am = AudioManager.getInstance();
            let ret = am.requestAudioSession(AudioManager.StreamType.AUDIO_STREAM_TTS,
                AudioManager.AudioSessionType.AUDIOSESSION_REQUEST_PAUSE_OTHERS, "smsReject",
                () => {});
            if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
                hasSession = true;
                Log.d(TAG, "requestAudioSession, requested");
            }
        } catch (e) {
            Log.e(TAG, "requestAudioSession, error = " + e);
        }
    }

    static releaseAudioSession() {
        try {
            if (!hasSession) {
                return;
            }
            let am = AudioManager.getInstance();
            let ret = am.abandonAudioSession("smsReject", () => {});
            if (ret === AudioManager.AudioSessionResult.AUDIOSESSION_RESULT_GRANTED) {
                hasSession = false;
                Log.d(TAG, "releaseAudioSession, released");
            }
        } catch (e) {
            Log.e(TAG, "releaseAudioSession, error = " + e);
        }
    }

    // 下面是一些与电话号码相关的转换函数
    static normalizeNumber(rowNumber: string) {
        if (!rowNumber) {
            return;
        }

        let number = [];
        for (let i = 0; i < rowNumber.length; i++) {
            let c = rowNumber.charAt(i);
            if (number.length === 0 && c === "+") {
                number.push(c);
            } else if (!isNaN(Number(c))) {
                number.push(c);
            }
        }

        Log.d(TAG, "normalizeNumber", rowNumber, number.join(""));
        return number.join("");
    }

    static isNormalNumber(number: string) {
        return !MobilePattern.test(number);
    }

    static normalizeNumberForQianDun(number: string) {
        if (!number) {
            Log.i(TAG, "normalizeNumberForQianDun number is null!!");
            return null;
        }

        let formatNumber = number;
        try {
            // Remove " ", "-" chars
            formatNumber = formatNumber.replace(/[-\s]/g, "");

            let VALID_NUMBER_PATTERN_LOOSELY = /^(\+\d{3,}|\d{2,})/; // loose condition
            let result = VALID_NUMBER_PATTERN_LOOSELY.exec(formatNumber);

            if (result) {
                formatNumber = result[0];
            } else {
                Log.d(TAG, "normalizeNumberForQianDun failed, return null!!");
                return null;
            }
        } catch (e) {
            Log.w(TAG, "normalizeNumberForQianDun exception:" + e.stack);
            formatNumber = null;
        }

        Log.d(TAG, "normalizeNumberForQianDun, format = " + formatNumber + ", number = " + number);
        return formatNumber;
    }

    static getCurrentHomeshell() {
        const PageManager = require("yunos/page/PageManager");
        let pm = new PageManager();
        let home = pm.getCurrentHomeshell();
        Log.d(TAG, "getCurrentHomeshell, name = " + home.name + ", uri = " + home.uri);
        if (home) {
            return home.uri;
        } else {
            return null;
        }
    }

    static getPortscapeTopPos() {
        let homeUri = Utils.getCurrentHomeshell();
        let topPos = Utils.getStatusBarHeight();
        if (homeUri && -1 !== homeUri.indexOf("map")) {  //A homeshell
            Log.d(TAG, "getPortscapeTopPos,  homeshell A, add navigate height ");
            topPos += screen.getPixelBySDp(<number>Res.getConfig("root_menu_heiht"));
        }
        return topPos;
    }
}

export = Utils;
