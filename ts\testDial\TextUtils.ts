"use strict";

class TextUtils {
    static readonly PHONE_NUMBER_VALIDATOR = "^[+]?[0-9- ,;()#*]*$";

    static isEmpty(str: string) {
        let ret = true;
        // FIXME: here we trimed str, which is a bomb!!!
        if (str && this.trimSpace(str) && this.trimSpace(str).length > 0) {
            ret = false;
        }
        return ret;
    }

    static trimSpace(str: string): string {
        if (!str) {
            return null;
        }
        return str.replace(new RegExp("\\s+", "gm"), "");
    }
}

export = TextUtils;
