/* 通话中，小的悬浮窗 */
"use strict";
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import WindowCAF = require("yunos/ui/view/Window");
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import TouchEvent = require("yunos/ui/event/TouchEvent");

import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();

import Utils = require("../utils/Utils");
import BaseLayout = require("./BaseLayout");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import Log = require("../utils/Logs");
import TrackerUtil = require("../utils/TrackerUtil");

const TAG = "IncallingFloatWindow";

interface IPage {
    pageapi: object;
}

interface IWindowParams {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
}

interface IMyWindow {
    specifiedMode: number;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class IncallingFloatWindow extends BaseLayout {
    private visible: boolean;
    private mainView: CompositeView;
    private winParam: IWindowParams;
    private window: WindowCAF;
    private root: CompositeView;
    private icon: ImageView;
    private callname: TextView;
    private callState: TextView;
    private _callnameText: string;
    private _nameUpdatedNumber: string;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();

        return;
    }

    createLayout() {
        this.mainView = <CompositeView> LayoutManager.loadSync("MOFloatLayout.xml");
        this._view = this.mainView;
        this.mainView.left = 0;
        this.mainView.top = 0;
        this.mainView.width = 0;
        this.mainView.height = <number> Res.getConfig("mo_float_window_height");
        this.mainView.clipBound = false;

        this.winParam = {};
        this.winParam.height = <number> Res.getConfig("mo_float_window_height");
        // must know content's length, and then re-calculate width
        this.winParam.width = 0;
        this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        this.winParam.y = screenInstance.getPixelBySDp(<number> Res.getConfig("mo_float_window_margin_top"));

        this.window = <WindowCAF> WindowCAF.create(this.controller.mainPage, {
            left: this.winParam.x,
            top:  Utils.isLandscape() ? this.winParam.y : this.winParam.y + Utils.getStatusBarHeight(),
            width: this.winParam.width,
            height: this.winParam.height,
            type: 2006,
            layoutFlags: 0x00000008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        (<IMyWindow><object> this.window).specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";

        this.root = <CompositeView> this._view.findViewById("root");
        this.icon = <ImageView> this._view.findViewById("icon");
        this.callname = <TextView> this._view.findViewById("callname");
        this.callState = <TextView> this._view.findViewById("callState");

        Log.v(TAG, "IncallingFloatWindow createLayout", this.window.height, this.window.width);
        this.window.addChild(this.mainView);
        this.window.addEventListener("touchstart", this.onWindowTouchDown.bind(this));
        this.window.addEventListener("touchend", this.onWindowTouchUp.bind(this));
        this.window.addEventListener("touchcancel", this.onWindowTouchUp.bind(this));

        return;
    }

    update(param: Array<IMyCallCell>, timecount: number) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callname || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }

        let callCell = param[0];
        let call: IMyCallCell = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        } else {
            let info = callCell.callerInfo;
            this.callname.text = this._callnameText || info.name;
        }
        this.callState.text = this.controller.callStatusToString(callCell, timecount);

        this.resizeWindow();

        // if (!this.visible) {
        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;
        // } else {
        //     Log.d(TAG, "has shown already");
        // }

        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    this.callname.text = name;
                } else {
                    this._callnameText = call.lineid;
                    this.callname.text = call.lineid;
                }
                this.resizeWindow();
            });
        }
    }

    updateCallState(callList: Array<IMyCallCell>, curtime: number) {
        let callCell = callList[0];
        let call: IMyCallCell = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);

        if(this._nameUpdatedNumber !== call.lineid){
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    this.callname.text = name;
                } else {
                    this._callnameText = call.lineid;
                    this.callname.text = call.lineid;
                }
            });
            return ;
        }

        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        } else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
            this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
        } else {
            this.callState.text = this.controller.callStatusToString(callCell, curtime);
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount,
            " this.callname.text=", this.callname.text, " this.callState.text=", this.callState.text);
        this.resizeWindow();
    }

    hide() {
        //if (this.visible) {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
        TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CallPage, {from: "bluetooth"});
        Log.d(TAG, "set this.visible to false");
        // }
    }

    show() {
        // if (!this.visible) {
        this.window.show();
        this.visible = true;
        TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CallPage, null, {});
        Log.d(TAG, "show called, set this.visible to true");
        // }
    }

    switchToFullScreen() {
        Log.d(TAG, "switchToFullScreen called");
        this.controller.closeNotificationCenter();
        if (this.controller.mainPage && this.controller.mainPage.destroyed === false
            && (<IPage><object> this.controller.mainPage).pageapi) {
            // main page is still alive
            Log.d(TAG, "calldisplayer main page is still alive");
            this.controller.toggleFloatFullWindow(1); // ViewController.FULL_WINDOW);
            this.controller.sendLink2MainPage();
        } else {
            // should not come here
            Log.e(TAG, "why call displayer main page not alive?");
        }
    }

    onWindowTouchDown() {
        if (this.root) {
            this.root.background = Res.getImageSrc("images/qms_tip_bg_dialing_pressed.png");
        }
    }

    onWindowTouchUp() {
        if (this.root) {
            this.root.background = Res.getImageSrc("images/qms_tip_bg_dialing_normal.png");
        }
        this.switchToFullScreen();
    }

    resetPosition() {
        this.mainView.top = this.mainView.left = 0;
        this.window.left = this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        this.window.top = this.winParam.y = Utils.isLandscape() ? screenInstance.getPixelBySDp(<number> Res.getConfig("mo_float_window_margin_top")) : screenInstance.getPixelBySDp(<number> Res.getConfig("mo_float_window_margin_top")) + Utils.getStatusBarHeight();
    }

    resizeWindow() {
        let nameWidth = this.callname.width;
        let nameWidthMax =
            (Utils.isLandscape() ? Utils.getScreenWidth() : Utils.getScreenHeight()) / 3;
        if (nameWidth >= nameWidthMax) {
            nameWidth = nameWidthMax;
            this.callname.width = nameWidthMax;
        } else {
            this.callname.width = nameWidth;
        }
        let stateWidth = this.callState.width;
        // window width = margin + spacing + icon width + name width + state width
        this.winParam.width = ((<number> Res.getConfig("mo_float_window_margin_left")) * 2 +
            (<number> Res.getConfig("mo_float_window_spacing")) * 2 +
            (<number> Res.getConfig("mo_float_windoe_icon_width")) +
            (nameWidth) + (stateWidth));
        Log.v(TAG, "resizeWindow: ", (nameWidth), (stateWidth), this.winParam.width);
        this.root.width = this.winParam.width;
        this.mainView.width = this.winParam.width;
        this.window.width = this.winParam.width;

        this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        if (Utils.isLandscape()) {
            this.winParam.x += Utils.getStatusBarHeight();
        }
        this.window.left = this.winParam.x;
    }

    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        this.callname.text = "";
        this.callState.text = "";
        this.resizeWindow();
    }

    clearPhoneNameAndNumber(){
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }
}

export = IncallingFloatWindow;
