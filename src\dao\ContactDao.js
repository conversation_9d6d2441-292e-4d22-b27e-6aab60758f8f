/* 根据号码查询电话本接口*/
"use strict";
const DataProvider = require("yunos/provider");
const Resolver = DataProvider.DataResolver;
const DataValues = DataProvider.DataValues;
const Log = require("../utils/Logs");
const Settings = require("yunos/content/Settings");
const TABLE_CONTACTS_URI = "page://contactsprovider.yunos.com/provider?table=contacts";
const NUMBER = "number";
const DISPLAY_NAME = "display_name";
const PHONEBOOK_LABEL = "phonebook_label";
const TAG = "ContactDao";
var _instance = null;
class ContactDao {
    static getDao() {
        return _instance || (_instance = new ContactDao());
    }
    getContactInfo(phoneNumber, cb) {
        Log.i(TAG, "getContactInfo:" + phoneNumber);
        Resolver.query(TABLE_CONTACTS_URI, [DISPLAY_NAME], NUMBER + "='" + phoneNumber + "'", null, null, (error, cursor) => {
            if (error !== null) {
                Log.i(TAG, "dealContactId - query, return error = " + error);
                cb && cb("query fail", null, null);
                return;
            }
            if (cursor === null || cursor.isClosed()) {
                Log.i(TAG, "dealContactId - query, cursor is null  or closed");
                cb && cb("query fail", null, null);
                return;
            }
            if (cursor.count <= 0) {
                Log.i(TAG, "dealContactId - query, cursor is empty");
                cursor.close();
                let name = this.queryFromCustomNameMap(phoneNumber);
                if (!name || name === "") {
                    cb && cb("query fail", null, null);
                }
                else {
                    cb && cb(null, name, null);
                }
                return;
            }
            Log.i(TAG, "query result count" + cursor.count);
            cursor.moveToFirst((err) => {
                if (err !== null) {
                    Log.i(TAG, "moveToFirst - return error = " + error);
                    cb && cb("query fail", null, null);
                    cursor.close();
                    return;
                }
                let name = cursor.getValue(cursor.getColumnIndex(DISPLAY_NAME));
                Log.i(TAG, "query result name" + name);
                if (!name || name === "") {
                    name = this.queryFromCustomNameMap(phoneNumber);
                }
                cb && cb(null, name, null);
                cursor.close();
            });
        });
    }
    queryFromCustomNameMap(phoneNumber) {
        let name = "";
        try {
            if (!this._customMapReady) {
                this._customMap = new Map();
                let customer_service_telephone = Settings.Global.getStringSync(Resolver, "customer_service_telephone", "");
                let shanghai_service_telephone = Settings.Global.getStringSync(Resolver, "shanghai_service_telephone", "");
                Log.i(TAG, "customer_service_telephone:" + customer_service_telephone +
                    ",shanghai_service_telephone;" + shanghai_service_telephone);
                const Resource = require("yunos/content/resource/Resource");
                const R = Resource.getInstance();
                if (customer_service_telephone && customer_service_telephone !== "") {
                    customer_service_telephone = customer_service_telephone.replace(/-/g, "");
                    this._customMap.set(customer_service_telephone, R.getString("TEXT_BANMA_SERVER"));
                    this._customMapReady = true;
                }
                if (shanghai_service_telephone && shanghai_service_telephone !== "") {
                    shanghai_service_telephone = shanghai_service_telephone.replace(/-/g, "");
                    this._customMap.set(shanghai_service_telephone, R.getString("TEXT_SHANGQI_SERVER"));
                    this._customMapReady = true;
                }
            }
            name = this._customMap.get(phoneNumber);
        }
        catch (e) {
            Log.i(TAG, "query custom number exception", e);
            name = "";
        }
        Log.i(TAG, "query custom number result:" + name);
        return name;
    }
}
module.exports = ContactDao;
