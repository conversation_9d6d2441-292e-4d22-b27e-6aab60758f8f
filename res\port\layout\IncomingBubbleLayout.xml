<CompositeView id="imcommingBubbleRoot" layout="{layout.imcommingBubbleLayout}" background="{img(images/qms_call_bg_conversation_popup.png)}"
    width="{config.mo_float_window_width_sdp_two}" height="{config.mo_float_window_height_sdp_two}" >
    <ButtonBM id="smallHangUp" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_hang_up_04_normal.png)}"
         buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
    <CompositeView id="info" layout="{layout.imcommingBubbleLayoutInfo}" width="{config.mt_float_text_width_two}" height="{sdp(72)}" >
        <TextView id="imcommingText" propertySetName="extend/hdt/Caption2" color="{theme.color.White_3}" height="{config.mt_float_state_height_two}" width="{config.mt_float_text_width_two}"
            align = "{enum.TextView.Align.Center}" text = "{string.TEXT_INCOMING_CALL}" multiLine="false" maxLineCount="1"/>
        <TextView id="imcommingName" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_1}" height="{config.mt_float_text_height_two}"
            width="{config.mt_float_text_width_two}" align = "{enum.TextView.Align.Center}" multiLine="false" maxLineCount="1"/>
    </CompositeView>
    <ButtonBM id="smallAnswer" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_answer_04_normal.png)}"
        buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
</CompositeView>
