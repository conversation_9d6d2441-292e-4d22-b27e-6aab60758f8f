"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");

const addCall = {
    _subId: 0,
    _callId: 1,
    _state: 5,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

// huangup call
const huangupcall = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

// active call
const activecall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

describe("MtBasic", function() {
    utTools.log("MtBasic start");

    var originalTimeout;

    beforeAll(() => {
        utTools.log("MtBasic beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("MtBasic afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("MtBasic beforeEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("calladded", addCall);
            yield 3000;
            utTools.log("MtBasic beforeEach 1");
            const controler = uiInf.getControler();
            const mtLayout = uiInf.getMTLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), mtLayout);
            utTools.log("MtBasic beforeEach 2");
        }());
    });

    afterEach((done) => {
        utTools.log("MtBasic afterEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            const controler = uiInf.getControler();
            utTools.equal(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
        }());
    });

    it("incoming", function(done) {
        utTools.log("MtBasic incoming in");
        utTools.asyncRun(done, function *() {
            utTools.log("MtBasic incoming run");
            yield 3000;
            const controler = uiInf.getControler();
            const mtLayout = uiInf.getMTLayout();
            const mtFloatLayout = uiInf.getMTFloatLayout();
            utTools.equal(controler.appVisible, true);
            if (mtLayout) {
                utTools.log("MtBasic incoming using mtLayout");
                utTools.viewEqual(uiInf.getTopLayout(), mtLayout);
                utTools.equal(mtLayout.callname.text, addCall._callNumber);
                utTools.equal(mtLayout.avatar.src, global.res.getImageSrc("images/ic_avatar_none.png"));
                utTools.strEqual(mtLayout.callState.text, "TEXT_INCOMING_CALL");
            } else if (mtFloatLayout) {
                utTools.log("MtBasic incoming using mtFloatLayout");
                // float window not save to layoutStack, so cannot check
                // utTools.viewEqual(uiInf.getTopLayout(), mtFloatLayout);
                utTools.equal(mtFloatLayout.callname.text, addCall._callNumber);
                utTools.equal(mtFloatLayout.avatar.src, global.res.getImageSrc("images/ic_avatar_none.png"));
            }
            yield 3000;
        }());
    });

    it("answer", function(done) {
        utTools.log("MtBasic answer in");
        utTools.asyncRun(done, function *() {
            utTools.log("MtBasic answer run");
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
        }());
    });
});
