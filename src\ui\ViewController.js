"use strict";
const Feature = require("yunos/util/Feature");
const TextView = require("yunos/ui/view/TextView");
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const Page = require("yunos/page/Page");
const PageLink = require("yunos/page/PageLink");
const AlertDialog = require("yunos/ui/widget/AlertDialog");
const MotionDialog = require("yunos/ui/widget/MotionDialog");
const Toast = require("yunos/ui/widget/Toast");
const VoiceInteractor = require("yunos/speech/VoiceInteractor");
const TTSPlayer = require("yunos/speech/TTSPlayer");
const Log = require("../utils/Logs");
const Utils = require("../utils/Utils");
const CallService = require("../services/CallService");
const DialsConstants = require("../utils/DialsConstants");
const IncallingFullWindow = require("./IncallingFullWindow");
//import IncomingCallFloatWindow = require("./IncomingCallFloatWindow");
//import IncallingFloatWindow = require("./IncallingFloatWindow");
const IncomingCallFloatWindow = require("./IncomingCallFloatWindowTwo");
const IncallingFloatWindow = require("./IncallingFloatWindowTwo");
const IncomingBubble = require("./IncomingBubble");
const AiHoldFloatWindow = require("./AiHoldFloatWindow");
const TrackerUtil = require("../utils/TrackerUtil");
const trackerUtil = TrackerUtil.getInstance();
const ContactDao = require("../dao/ContactDao");
const TAG = "ViewController";
const TOAST_DURATION = 3000;
const MOTION_INDEX_LEFT = 0;
const MOTION_INDEX_TOP = 1;
const MOTION_INDEX_RIGHT = 2;
class ViewController {
    constructor(mainPage) {
        this.preloadTimer = null;
        this.screenonHandle = null;
        this.screenoffHandle = null;
        this.trimMemoryTimer = null;
        this.adjTimer = null;
        Log.d(TAG, "constructor called");
        this.mainPage = mainPage;
        this.window = mainPage.window;
        if (DialsConstants.Customization.ENABLE_NAV_BAR && Feature.has("YUNOS_SYSCAP_NAVIGATION_BAR")) {
            this.navigationBarHeight = this.window.navigationBarHeight;
        }
        this.callService = new CallService(this);
        this.appVisible = false;
        this.mNeedRestoreXiaoyun = false;
        this.isFullScreen = false;
        this.toggleFullWindowFirst = true;
        this.toggleFloatWindowFirst = true;
        if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
            this.initMotionManager();
        }
        //this.mIncallingFullWindow = new IncallingFullWindow(this, this.callService);
        this.mIncallingFloatWindow = new IncallingFloatWindow(this, this.callService);
        //this.mIncomingCallFloatWindow = new IncomingCallFloatWindow(this, this.callService);
        //this.mIncomingBubble = new IncomingBubble(this, this.callService);
    }
    initMotionManager() {
        Log.d(TAG, "initMotionManager called");
        this.motionDialog = new MotionDialog();
        this.motionDialog.height = 430;
        Log.d(TAG, "change called for 12.3screen");
        this.motionDialog.takeOverMode = true;
        this.motionDialog.dialogLevel = AlertDialog.DialogLevel.SystemAlert;
        this.motionDialog.background = Res.getImageSrc("images/pic_gestures_panel.png");
        this.motionDialog.on("result", this.onMotionResult.bind(this));
        this.motionDialog.on("show", this.onMotionShow.bind(this));
        this.motionDialog.on("close", this.onMotionClosed.bind(this));
        let ImageView = require("yunos/ui/view/ImageView");
        let image = new ImageView();
        image.src = Res.getImageSrc("images/ic_gestures_user.png");
        let text = new TextView();
        text.color = "#FFFFFFFF";
        text.fontSize = 28;
        this.motionDialog.contentText = text;
        let RelativeLayout = require("yunos/ui/layout/RelativeLayout");
        let content = this.motionDialog.getContentView();
        content.addChild(image);
        content.addChild(text);
        content.layout = new RelativeLayout();
        content.layout.setLayoutParam(0, "align", { center: "parent", top: "parent" });
        content.layout.setLayoutParam(0, "margin", { top: 156 });
        content.layout.setLayoutParam(1, "align", { center: "parent", top: { target: 0, side: "bottom" } });
        // content.layout.setLayoutParam(1, "margin", {top: 20});
    }
    // main page的"backKey"事件处理函数
    hideFullWindow() {
        Log.d(TAG, "hideFullWindow, callui full window hide button pressed or back key pressed in main page");
        if (DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
            Log.v(TAG, "callui hideFullWindow");
            this.mainPage.myHidePage();
        }
        return true;
    }
    onLink(link) {
        this.updateAdjConfig(true);
        let event = link.getEventName();
        let data = link.getData();
        Log.v(TAG, "onLink evnent=", event, " data=", data);
        if (data) {
            try {
                let jsonData = JSON.parse(data);
                this.callService.onCallChanged(jsonData, false);
            }
            catch (e) {
                Log.i(TAG, "onLink JSON parse exception", e);
            }
        }
    }
    onHide() {
        Log.d(TAG, "onHide called");
        this.isFullScreen = false;
        if (this.dialog) {
            this.dialog.close();
            this.dialog = null;
        }
        if (this.appVisible) {
            this.isFullScreen = false;
            if (this.mIncallingFullWindow) {
                this.mIncallingFullWindow.hide();
            }
            this.controlView({ needShowFloatWindow: true });
        }
        else {
            this.resetUIContent();
            this.updateAdjConfig(false);
        }
    }
    onStop() {
        this.callService.stopInCallAdapterProxy(true);
        if (this.preloadTimer) {
            clearTimeout(this.preloadTimer);
            this.preloadTimer = null;
        }
        if (this.trimMemoryTimer) {
            clearTimeout(this.trimMemoryTimer);
            this.trimMemoryTimer = null;
        }
        if (this.adjTimer) {
            clearTimeout(this.adjTimer);
            this.adjTimer = null;
        }
    }
    onMotionWakeup(data) {
        if (this.appVisible === false) {
            Log.d(TAG, "onMotionWakeup, ignore as app not visible");
            return;
        }
        if (data._shape === 2) {
            this.rejectWithSms();
        }
        else if (data._shape === 5) {
            this.showMotionDialog();
        }
    }
    rejectWithSms() {
        Log.d(TAG, "rejectWithSms called");
        let callCell = this.callService.getFrontCall();
        if (!callCell) {
            Log.d(TAG, "rejectWithSms, ignore as no call");
            return;
        }
        let call = callCell.callinfo || callCell;
        if (call.status !== CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING &&
            call.status !== CallService.CALLSTATE.TELE_CALL_STATUS_WAITING) {
            Log.d(TAG, "rejectWithSms, ignore as not incoming or waiting call");
            return;
        }
        this.stopTTS();
        this.callService.hangup(null, "");
        if (this.hasPostponedSms()) {
            Log.e(TAG, "rejectWithSms, already have postponed sms");
        }
        this.postponedSms = { number: call.lineid };
    }
    triggerSmsSender(lineid) {
        Log.d(TAG, "triggerSmsSender called");
        if (!this.hasPostponedSms()) {
            Log.e(TAG, "triggerSmsSender, postponedSms is not set");
            return;
        }
        let number = this.postponedSms.number;
        this.postponedSms = {};
        if (lineid !== number) {
            Log.e(TAG, "triggerSmsSender, ignore as number not match");
            return;
        }
        var callPageLink = new PageLink("page://calldisplayer.yunos.com/smssender");
        callPageLink.eventName = "start";
        let data = { number: lineid, resetXiaoyun: true };
        if (this.mNeedRestoreXiaoyun) {
            this.mNeedRestoreXiaoyun = false;
            data.resetXiaoyun = true;
            Log.d(TAG, "triggerSmsSender, xiaoyun enable is delayed after sms rejection");
        }
        callPageLink.data = JSON.stringify(data);
        Page.getInstance().sendLink(callPageLink, function (err) {
            if (err) {
                Log.e(TAG, "triggerSmsSender, sendlink error = " + err);
            }
        });
    }
    hasPostponedSms() {
        return this.postponedSms && this.postponedSms.number;
    }
    showMotionDialog() {
        if (this.motionDialog.isShowing()) {
            Log.d(TAG, "showMotionDialog, ignore as motion dialog already shown");
            return;
        }
        let callCell = this.callService.getFrontCall();
        if (!callCell) {
            Log.d(TAG, "showMotionDialog, ignore as no call");
            return;
        }
        let call = callCell.callinfo || callCell;
        Log.d(TAG, "showMotionDialog, call status: " + this.callService.transStatusToString(call.status));
        let icons = [null,
            null,
            Res.getImageSrc("images/ic_gestures_hang.png"),
            Res.getImageSrc("images/ic_gestures_down.png")];
        switch (call.status) {
            case CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE:
            case CallService.CALLSTATE.TELE_CALL_STATUS_HOLDING:
                icons[0] = Res.getImageSrc("images/ic_gestures_mute.png");
                icons[1] = null;
                this.motionDialog.colorMode = 1; // MotionDialog.ColorMode.RedRight;
                break;
            case CallService.CALLSTATE.TELE_CALL_STATUS_DIALING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_ALERTING:
                icons[0] = Res.getImageSrc("images/ic_gestures_continueanswer.png");
                icons[1] = null;
                this.motionDialog.colorMode = 1; // MotionDialog.ColorMode.RedRight;
                break;
            case CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_WAITING:
                icons[0] = Res.getImageSrc("images/ic_gestures_answer.png");
                icons[1] = Res.getImageSrc("images/ic_gestures_message.png");
                this.motionDialog.colorMode = 3; // MotionDialog.ColorMode.GreenLeft | MotionDialog.ColorMode.RedRight;
                break;
            default:
                Log.e(TAG, "showMotionDialog, ignore as wrong call status");
                return;
        }
        this.motionDialog.setIcons(icons);
        this.motionDialog.contentText.text =
            callCell.callerInfo ? callCell.callerInfo.name : call.lineid;
        this.motionDialog.call = {
            callid: call.callid,
            status: call.status
        };
        this.motionDialog.show();
    }
    onMotionResult(result) {
        Log.d(TAG, "onMotionResult, result = " + result);
        if (result === MOTION_INDEX_LEFT) {
            if (!this.motionDialog.call) {
                Log.e(TAG, "onMotionResult, there is no call attached on motion dialog");
                return;
            }
            if (this.motionDialog.call.status
                === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE ||
                this.motionDialog.call.status
                    === CallService.CALLSTATE.TELE_CALL_STATUS_HOLDING) {
                this.callService.changeMicrophoneMuteState();
            }
            else if (this.motionDialog.call.status
                === CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING ||
                this.motionDialog.call.status
                    === CallService.CALLSTATE.TELE_CALL_STATUS_WAITING) {
                this.callService.answer(0, null, 0);
            }
        }
        else if (result === MOTION_INDEX_RIGHT) {
            this.callService.hangup(null, "");
        }
        else if (result === MOTION_INDEX_TOP) {
            if (this.motionDialog.call.status
                === CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING ||
                this.motionDialog.call.status
                    === CallService.CALLSTATE.TELE_CALL_STATUS_WAITING) {
                this.rejectWithSms();
            }
        }
    }
    onMotionShow() {
        Log.d(TAG, "onMotionShow called");
        if (this.mIncomingCallFloatWindow && this.mIncomingCallFloatWindow.visible) {
            this.mIncomingCallFloatWindow.onMotionDialogVisible(true);
        }
    }
    onMotionClosed() {
        Log.d(TAG, "onMotionClosed called");
        this.motionDialog.call = null;
        if (this.mIncomingCallFloatWindow && this.mIncomingCallFloatWindow.visible) {
            this.mIncomingCallFloatWindow.onMotionDialogVisible(false);
        }
    }
    checkMotionDialogStatus() {
        if (!this.motionDialog.isShowing()) {
            return;
        }
        if (!this.motionDialog.call) {
            Log.e(TAG, "checkMotionDialogStatus, will close dialog as no call attached");
            this.motionDialog.close();
            return;
        }
        let callCell = this.callService.getFrontCall();
        if (!callCell) {
            Log.e(TAG, "checkMotionDialogStatus, will close dialog as there is no call");
            this.motionDialog.close();
            return;
        }
        let call = callCell.callinfo || callCell;
        if (this.motionDialog.call.callid !== call.callid ||
            this.motionDialog.call.status !== call.status) {
            Log.d(TAG, "checkMotionDialogStatus, will close dialog as call changed");
            this.motionDialog.close();
            return;
        }
    }
    showToast(text, replaceable = true) {
        if (!this.toast) {
            this.toast = new Toast();
            this.toast.duration = TOAST_DURATION;
            let self = this;
            this.toast.on("close", function () {
                if (self.toast.nextSlot) {
                    Log.d(TAG, "showToast-onClose, show next slot");
                    self.showToast(self.toast.nextSlot);
                    self.toast.nextSlot = "";
                }
            });
        }
        if (this.toast._isShowing
            && !this.toast.replaceable) {
            Log.d(TAG, "showToast, there is irreplaceable toast and will show this next slot");
            // next slot should be replaceable, or there too many delayed toasts
            this.toast.nextSlot = text;
            return;
        }
        this.toast.replaceable = replaceable;
        this.toast.text = text;
        this.toast.show();
    }
    isKeyguardOn() {
        if (!DialsConstants.Customization.ENABLE_KEYGUARD) {
            return false;
        }
        Log.d(TAG, "isKeyguardOn =", this.keyguardOn);
        return this.keyguardOn;
    }
    onCallAddedHandle(isIncoming, isFirstCall) {
        Log.w(TAG, "onCallAddedHandle, isIncoming=", isIncoming, " isFirstCall=", isFirstCall, " this.isFullScreen=", this.isFullScreen);
        if (!isIncoming || !DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
            // MO or !ENABLE_FLOAT_WINDOW
            if (this.isFullScreen) {
                this.callService.screenOn();
                this.callService.screenOff();
            }
        }
        else { // 来电
            if (!DialsConstants.Customization.ENABLE_KEYGUARD) {
                Log.d(TAG, "onCallAddedHandle incoming call, !DialsConstants.Customization.ENABLE_KEYGUARD branch");
            }
            else if (!this.isFullScreen) { // if last status is fullscreen, then fullscreen
                let SystemService = require("core/system_service");
                let keyguardServiceProxy = SystemService.getService("keyguardservice");
                let self = this;
                self.keyguardOn = undefined;
                keyguardServiceProxy.isShowing().then(function (isShowing) {
                    Log.w(TAG, "----------keyguardServiceProxy isShowing =", isShowing);
                    if (!self.callService.isIncomingStatus()) {
                        return;
                    }
                    self.keyguardOn = isShowing;
                    if (isShowing) {
                        if (!self.isFullScreen) {
                            self.needScreenOn = true;
                            self.controlView({ needShowFullWindow: true });
                        }
                    }
                    else {
                        // self.isFullScreen = false;
                        self.controlView();
                        self.callService.screenOn();
                    }
                }, function (error) {
                    Log.w(TAG, "keyguardServiceProxy isShowing return err:", error);
                    self.controlView();
                });
            }
            else {
                if (this.isFullScreen) {
                    this.callService.screenOn();
                }
            }
        }
        if (isFirstCall) {
            if (this.mIncallingFloatWindow) {
                this.mIncallingFloatWindow.resetPosition();
            }
            this.setAppVisible();
        }
    }
    prepareIncallingFullWindow() {
        if (this.mIncallingFullWindow || this.preloadTimer) {
            return;
        }
        this.preloadTimer = setTimeout(() => {
            Log.d(TAG, "prepareIncallingFullWindow ", this.mIncallingFullWindow);
            if (!this.mIncallingFullWindow) {
                this.mIncallingFullWindow = new IncallingFullWindow(this, this.callService);
            }
        }, 400);
    }
    showIncallingFullWindow(data, timecount, leaveIncoming, enterOutgoing, audioState) {
        Log.d(TAG, "showIncallingFullWindow called: timecount=", timecount, " leaveIncoming=", leaveIncoming, " enterOutgoing=", enterOutgoing, " this.isFullScreen=", this.isFullScreen);
        Log.v(TAG, "showIncallingFullWindow called: audioState=", JSON.stringify(audioState));
        Log.d(TAG, "showIncallingFullWindow will hide float window first");
        if (this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.hide();
        }
        if (this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow.hide();
        }
        // this.mainPage.stopFastRender();
        Log.d(TAG, "showIncallingFullWindow will update incalling full window");
        if (!this.mIncallingFullWindow) {
            this.mIncallingFullWindow = new IncallingFullWindow(this, this.callService);
        }
        this.mIncallingFullWindow.update(data, timecount, audioState);
        this.setAppVisible();
    }
    showIncallingFloatWindow(data, timecount) {
        Log.d(TAG, "showIncallingFloatWindow called, timecount=", timecount);
        if (DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
            // show float window
            if (!this.mIncallingFloatWindow) {
                this.mIncallingFloatWindow = new IncallingFloatWindow(this, this.callService);
            }
            if (this.mAiHoldFloatWindow) {
                this.mAiHoldFloatWindow.hide();
            }
            this.mIncallingFloatWindow.update(data, timecount);
            this.setAppVisible();
        }
    }
    showAilFloatWindow(data, timecount) {
        Log.d(TAG, "showAilFloatWindow called, timecount=", timecount);
        if (DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
            if (!this.mAiHoldFloatWindow) {
                this.mAiHoldFloatWindow = new AiHoldFloatWindow(this, this.callService);
            }
            this.mAiHoldFloatWindow.update(data, timecount);
            this.setAppVisible();
        }
    }
    updateCallState(data, timecount) {
        // 为了在切换窗口时，能够及时看到正确的通话时间和call state，所以同时做了两个窗口的刷新
        // 不管窗口是不是正在显示
        if (this.mIncallingFullWindow) {
            Log.d(TAG, "updateCallState update call info view in full window");
            this.mIncallingFullWindow.updateCallState(data, timecount);
        }
        if (this.mIncallingFloatWindow) {
            Log.d(TAG, "updateCallState update state and call state in float window");
            this.mIncallingFloatWindow.updateCallState(data, timecount);
        }
        if (this.mAiHoldFloatWindow) {
            Log.d(TAG, "updateCallState update state and call state in mAiHoldFloatWindow ");
            this.mAiHoldFloatWindow.updateCallState(data, timecount);
        }
        if (this.mIncomingCallFloatWindow) {
            Log.d(TAG, "updateCallState update state and call state in IncomingCallFloatWindow ");
            this.mIncomingCallFloatWindow.updateCallState(data, timecount);
        }
    }
    onDialFail(reason) {
        this.showToast("Dail failed!" + reason);
    }
    showIncomingWindow(callInfo) {
        Log.i(TAG, "showIncomingWindow called isFullScreen=", this.isFullScreen, " this.isKeyguardOn()=", this.isKeyguardOn());
        //  we don't show incoming call view before get keyguard info
        if (!this.isFullScreen && this.isKeyguardOn() === undefined) {
            Log.d(TAG, "showIncomingWindow wait keyguard status");
            return;
        }
        if (!this.isFullScreen && DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
            // 当前不是全屏界面显示时，要显示来电浮窗
            // don't show 2 float windows together
            if (this.mIncallingFloatWindow) {
                this.mIncallingFloatWindow.hide();
            }
            if (this.mAiHoldFloatWindow) {
                this.mAiHoldFloatWindow.hide();
            }
            // 显示来电悬浮窗
            if (!this.mIncomingCallFloatWindow) {
                this.mIncomingCallFloatWindow = new IncomingCallFloatWindow(this, this.callService);
            }
            this.mIncomingCallFloatWindow.update(callInfo);
            return;
        }
        // 处在通话中的全屏界面时，又有第2个来电，更新全屏界面的显示
        if (this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow.hide();
        }
        if (this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.hide();
        }
        if (this.dialog) {
            this.dialog.close();
            this.dialog = null;
        }
        // this.mainPage.stopFastRender();
        if (!this.mIncallingFullWindow) {
            this.mIncallingFullWindow = new IncallingFullWindow(this, this.callService);
        }
        this.mIncallingFullWindow.update(callInfo, 0, null);
    }
    hideAllWindow() {
        if (this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.hide();
        }
        if (this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow.hide();
        }
        if (this.mIncomingBubble) {
            this.mIncomingBubble.hide();
        }
        if (this.mIncallingFullWindow) {
            this.mIncallingFullWindow.hide();
        }
        if (this.mAiHoldFloatWindow) {
            this.mAiHoldFloatWindow.hide();
        }
        this.setAppInVisible();
        this.isFullScreen = false;
        this.toggleFloatWindowFirst = true;
        this.toggleFullWindowFirst = true;
        if (this.dialog) {
            this.dialog.close();
            this.dialog = null;
        }
        // reset float window content
        this.resetUIContent();
        this.updateAdjConfig(false);
        this.mainPage.myHidePage();
        trackerUtil.leavePage(TrackerUtil.TrackerPages.InCallMainPage, {});
    }
    handleServiceDeath() {
        Log.d(TAG, "handleServiceDeath");
        this.mainPage.stopPage();
    }
    handlePostDialWait(callId, remaining) {
        Log.d(TAG, "onPostDialWait called: ", callId, remaining);
        if (this.dialog) {
            this.dialog.message = remaining;
            this.dialog.show();
            return;
        }
        let dialog = new AlertDialog();
        dialog.title = Res.getString("TEXT_COMFIRN_SEND_DTMF");
        dialog.message = remaining;
        dialog.buttons = [Res.getString("TEXT_CANCEL"), Res.getString("TEXT_COMFIRN")];
        dialog.addEventListener("result", (index) => {
            Log.d(TAG, "onPostDialWait result: ", index);
            if (index === 1) {
                let remainIdx = 0;
                let handle = setInterval(() => {
                    Log.d(TAG, "onPostDialWait setInterval: ", remainIdx);
                    if (remainIdx < remaining.length) {
                        let ch = remaining.charAt(remainIdx);
                        Log.v(TAG, "onPostDialWait senddtmf: ", remainIdx, remaining.charAt(remainIdx));
                        if (ch === ";") {
                            clearInterval(handle);
                            handle = undefined;
                            remaining = remaining.substr(remainIdx + 1);
                            remainIdx = 0;
                            dialog.message = remaining;
                            dialog.show();
                        }
                        else {
                            this.callService.sendDtmf(remaining.charAt(remainIdx));
                            remainIdx++;
                        }
                    }
                    else {
                        Log.d(TAG, "onPostDialWait stop interval: ", handle);
                        if (handle !== undefined) {
                            clearInterval(handle);
                            handle = undefined;
                        }
                    }
                }, 1000);
            }
        });
        this.dialog = dialog;
        this.dialog.show();
    }
    sendLink2MainPage() {
        Log.d(TAG, "send link to calldisplayer main page");
        let pageLink = new PageLink("page://calldisplayer.yunos.com/calldisplayer");
        pageLink.eventName = "fullscreen";
        Page.getInstance().sendLink(pageLink, (err) => {
            if (err) {
                Log.e(TAG, "sendLink2MainPage, sendlink error = " + err);
            }
        });
    }
    sendLink2ChartCarPage() {
        Log.d(TAG, "send link to vmsgcall page");
        let pageLink = new PageLink("page://vmsg.ivi.com/vmsgcall");
        pageLink.eventName = "showChartFullScreen";
        Page.getInstance().sendLink(pageLink, (err) => {
            if (err) {
                Log.e(TAG, "sendLink2MainPage, sendlink error = " + err);
            }
        });
    }
    setAppVisible() {
        Log.d(TAG, "setAppVisible, set this.appVisible=" + this.appVisible + " to true");
        this.appVisible = true;
        if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
            this.disableXiaoyun(); // delete this for can stop tts,but motion dialog use it disable xiaoyun
        }
        if (!this.screenonHandle && !this.screenoffHandle) {
            const screenCb = (on) => {
                Log.d(TAG, "receiveBroadcast screen_on/off.power.yunos.com callback, screen on is: ", on);
                this.callService.setScreenOn(on);
            };
            this.screenonHandle = this.mainPage.receiveBroadcast("screen_on.power.yunos.com", screenCb.bind(this, true));
            this.screenoffHandle = this.mainPage.receiveBroadcast("screen_off.power.yunos.com", screenCb.bind(this, false));
        }
    }
    setAppInVisible() {
        Log.d(TAG, "setAppInVisible, set this.appVisible=" + this.appVisible + " to false");
        this.appVisible = false;
        this.checkEnableXiaoyun();
        if (this.screenonHandle && this.screenoffHandle) {
            this.mainPage.unreceiveBroadcasts(this.screenonHandle);
            this.mainPage.unreceiveBroadcasts(this.screenoffHandle);
        }
    }
    trimMemory() {
        if (!this.appVisible) {
            if (!this.trimMemoryTimer) {
                this.trimMemoryTimer = setTimeout(() => {
                    this.trimMemoryTimer = null;
                    if (!this.appVisible) {
                        Log.d(TAG, "trimMemory, exit.");
                        Page.getInstance().stopPage();
                    }
                }, 1000);
            }
        }
    }
    resetUIContent() {
        Log.d(TAG, "resetUIContent");
        this.isFullScreen = false;
        if (this.mIncallingFullWindow) {
            this.mIncallingFullWindow.resetUIContent();
        }
        if (this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.resetUIContent();
            this.mIncallingFloatWindow.resetPosition();
        }
        if (this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow.resetUIContent();
        }
        if (this.mIncomingBubble) {
            this.mIncomingBubble.resetPosition();
        }
    }
    showKeyboardPanel() {
        if (this.mIncallingFullWindow) {
            this.mIncallingFullWindow.showKeyboard();
        }
    }
    closeNotificationCenter() {
        if (!DialsConstants.Customization.ENABLE_CLOSE_NOTIFICATION) {
            return;
        }
        Log.d(TAG, "closeNotificationCenter enter");
        let SystemService = require("core/system_service");
        let StatusbarService = SystemService.getService(this.mainPage, "statusbar").Proxy;
        let statusbarServiceProxy = new StatusbarService(this.mainPage);
        statusbarServiceProxy.closeSystemDialog();
        Log.d(TAG, "closeNotificationCenter end");
        return;
    }
    /**
     * lock is true: system will fix adj to zero which will keep page aliving
     * lock is false: system will reset adj which will kill page by low memory killler
     * NOTICE: To set true and false must make a pair, system will count setting times, if not match
     * the result will be incorrect
     */
    updateAdjConfig(lock) {
        Log.d(TAG, "updateAdjConfig, lock = ", lock); // TODO: remove
        let self = this;
        let clearAdjTimer = function () {
            if (self.adjTimer) {
                clearTimeout(self.adjTimer);
                self.adjTimer = null;
            }
        };
        let updateAdiImpl = function () {
            Log.d(TAG, "updateAdjConfig to", lock, process.pid);
            clearAdjTimer();
            self.mainPage.updateServiceSwitch(lock, 5 /* Page.ServiceSwitchType.Telecom */, process.pid);
            self.fixedAdj = lock;
        };
        if (this.fixedAdj !== lock) {
            if (lock === true) {
                updateAdiImpl();
            }
            else {
                if (!this.adjTimer) {
                    Log.d(TAG, "updateAdjConfig, start timer to unlock");
                    this.adjTimer = setTimeout(updateAdiImpl.bind(this), TOAST_DURATION * 2);
                }
            }
        }
        else {
            if (this.adjTimer) {
                Log.d(TAG, "updateAdjConfig, already set and just clear the timer");
                clearAdjTimer();
            }
        }
    }
    isAnyCallExist() {
        return this.callService && this.callService.isAnyCallExist();
    }
    callStatusToString(callCell, timecount) {
        // let call: IMyCallCell = callCell.callinfo || callCell;
        let call = callCell.hasOwnProperty("callinfo") && callCell.callinfo ? callCell.callinfo : callCell;
        let status = call.status;
        let startCount = callCell.startCount;
        Log.v(TAG, "callStatusToString: status=", status, " timecount=", timecount, " startCount=", startCount);
        if (callCell.hangStatus) {
            Log.v(TAG, "callStatusToString, callCell.hangStatus = " + callCell.hangStatus);
            if (callCell.hangStatus === CallService.HANGSTATUS.DISCONNECTING) {
                return Res.getString("TEXT_HANGING_UP");
            }
            else if (callCell.hangStatus === CallService.HANGSTATUS.DISCONNECTED) {
                return Res.getString("TEXT_END_CALL");
            }
            else {
                Log.e(TAG, "callStatusToString, wrong hangStatus value");
            }
        }
        if (status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && timecount && startCount) {
            return Utils.getTimeLength(timecount, startCount);
        }
        if (!this.callStatusString) {
            this.callStatusString = [];
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE] = Res.getString("TEXT_CALLING");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_HOLDING] = Res.getString("TEXT_HOLD_ON");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_DIALING] = Res.getString("TEXT_DIALING");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_ALERTING] = Res.getString("TEXT_RINGING");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING] = Res.getString("TEXT_INCOMING_CALL");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_WAITING] = Res.getString("TEXT_INCOMING_CALL");
            this.callStatusString[CallService.CALLSTATE.TELE_CALL_STATUS_DISCONNECTED] = Res.getString("TEXT_END_CALL");
        }
        return this.callStatusString[status];
    }
    showIncomingTTS(callInfo) {
        if (!callInfo) {
            Log.e(TAG, "showIncomingTTS, wrong parameter");
            return;
        }
        Log.d(TAG, "showIncomingTTS called");
        let number = this.callService.getCallCellNumber(callInfo);
        ContactDao.getDao().getContactInfo(number, (err, name, photo) => {
            let ttsName = number;
            if (!err && name) {
                ttsName = name;
            }
            let ttsContent = Res.getString("VOICE_INCOMING_CALL", { name: ttsName });
            let data = { spokenText: ttsContent, writtenText: ttsContent, tips: ttsContent, type: null, streamType: TTSPlayer.StreamType.VoiceCall };
            this.getXiaoyunService();
            this.xiaoyunService.say(data, VoiceInteractor.MicState.TurnOff, null);
        });
    }
    showIncomingOverTTS(isHangup) {
        if (isHangup) {
            this.getXiaoyunService();
            let data = { spokenText: Res.getString("VOICE_HANGUP_COMPLETE"), writtenText: null, tips: null, type: null, streamType: TTSPlayer.StreamType.VoiceCall };
            this.xiaoyunService.say(data, VoiceInteractor.MicState.TurnOff, null);
        }
    }
    stopTTS() {
        Log.d(TAG, "stopTTS called");
        let XiaoyunVoiceModality = require("yunos/ui/mmi/XiaoyunVoiceModality");
        let voiceModality = XiaoyunVoiceModality.getInstance(this.mainPage);
        voiceModality.stopTTS();
    }
    getXiaoyunService() {
        if (!this.xiaoyunService) {
            this.xiaoyunService = VoiceInteractor.getInstance(this.mainPage);
        }
        return this.xiaoyunService;
    }
    disableXiaoyun() {
        // disable xiaoyun
        Utils.getXiaoyunEnable(this.mainPage, (enable) => {
            Log.d(TAG, "disableXiaoyun, is xiaoyun enable: " + enable);
            if (enable === true) {
                /* this.getXiaoyunService();
                if (this.xiaoyunService.isShow) {
                    Log.d(TAG, "disableXiaoyun, xiaoyun is show");
                    this.xiaoyunService.hide(true);
                } else {
                    Log.d(TAG, "disableXiaoyun, xiaoyun is not show");
                    // this.xiaoyunService.startSpeech(true);
                } */
                Utils.setXiaoyunEnableSync(this.mainPage, false);
                Log.d(TAG, "disableXiaoyun, xiaoyun is disabled");
                this.mNeedRestoreXiaoyun = true;
            }
        });
    }
    checkEnableXiaoyun() {
        if (this.mNeedRestoreXiaoyun) {
            this.mNeedRestoreXiaoyun = false;
            Utils.setXiaoyunEnable(this.mainPage, true, (err) => {
                Log.d(TAG, "checkEnableXiaoyun, enable xiaoyun result = " + err);
            });
        }
    }
    controlView(param = {}) {
        Log.i(TAG, "controlView called, this.appVisible=", this.appVisible, param.isLeaveIncoming, param.isEnterOutgoing);
        if (this.appVisible === false) {
            Log.w(TAG, "controlView called, but appVisible is false, return!!");
            return;
        }
        let callList = this.callService.getExistCallList();
        if (callList.length === 0) {
            // 把前面曾经显示的窗口，全部隐藏
            Log.d(TAG, "no call exists");
            this.callService.stopCalculateCallTime();
            this.callService.status = CallService.CALL_SERVICE_STATUS.IDLE_CALL;
            this.callService.operatorInfo = [];
            this.hideAllWindow();
        }
        else if (callList[0] && callList[0].localstatus ===
            CallService.CALL_SERVICE_STATUS.INCOMING_CALL) {
            // 来电浮窗
            Log.d(TAG, "call list 0 is incoming call");
            if (!param.audioStateChanged) {
                this.callService.status = callList[0].localstatus;
                this.showIncomingWindow(callList);
            }
            else {
                // 如果是由 audio state change 触发的界面变化，在这里什么也不做
                // 以防止来电浮窗被多次显示
                Log.d(TAG, "controlView called by audio state changed, do nothing, return directly.");
                return;
            }
        }
        else {
            // 电话接通，显示全屏或浮窗
            Log.d(TAG, "in calling, need to show incalling view");
            if (param.isLeaveIncoming === true) {
                if (this.mIncomingCallFloatWindow) {
                    this.mIncomingCallFloatWindow.hide();
                }
                if (this.mIncomingBubble) {
                    this.mIncomingBubble.hide();
                }
            }
            let callCell = callList[0];
            let call = callCell.callinfo || callCell;
            if (call.aiHold) {
                this.showAilFloatWindow(callList, this.callService.timecount);
                return;
            }
            else if (call.aiTransfer) {
                this.showIncomingWindow(callList);
                return;
            }
            if (callList.length === 1 && param.isEnterOutgoing === true && call.callType !== DialsConstants.Customization.CALL_TYPE_BT) {
                //非蓝牙电话主叫
                this.isThirdPartOutgoingCall = true;
            }
            /*if(param.isEnterOutgoing && callList.length === 1){
                //首次主叫，显示全屏窗口，其他只更新当前窗口状态
                this.showIncallingFullWindow(callList, this.callService.timecount,
                    param.isLeaveIncoming, param.isEnterOutgoing, this.callService.audioState);
                return ;
            }*/
            this.callService.status = callList[0].localstatus;
            Log.d(TAG, "this.callService.previousCallStatus=", this.callService.previousCallStatus, "call.status=", callList[0].status);
            //if ((this.callService.previousCallStatus === Call.STATE_INCOMING &&
            //    callList[0].status === Call.STATE_ACTIVE) || param.needShowFloatWindow === true) {
            // 来电接通后，不显示全屏窗口，显示浮窗
            //    Log.d(TAG, "controlView will show float window");
            if (this.isFullScreen || this.isThirdPartOutgoingCall) {
                Log.d(TAG, "controlView will show full window");
                this.showIncallingFullWindow(callList, this.callService.timecount, param.isLeaveIncoming, param.isEnterOutgoing, this.callService.audioState);
            }
            else { //默认显示悬浮窗
                Log.d(TAG, "controlView will show float window");
                this.showIncallingFloatWindow(callList, this.callService.timecount);
            }
            /*} else {
                Log.d(TAG, "controlView will show full window,",
                    " leaveIncoming=", param.isLeaveIncoming, " enterOutgoing=", param.isEnterOutgoing);
                this.showIncallingFullWindow(callList, this.callService.timecount,
                    param.isLeaveIncoming, param.isEnterOutgoing, this.callService.audioState);
            }*/
        }
    }
    setThirdPartOutgoingCall(isThirdPartOutgoingCall) {
        this.isThirdPartOutgoingCall = isThirdPartOutgoingCall;
    }
    toggleFloatFullWindow(windowType) {
        Log.d(TAG, "toggleFloatFullWindow called, will show window type: ", windowType);
        if (windowType === ViewController.FULL_WINDOW) {
            Log.d(TAG, "switch to full window");
            if (this.mIncallingFloatWindow) {
                this.mIncallingFloatWindow.hide();
            }
            if (this.mIncomingCallFloatWindow) {
                this.mIncomingCallFloatWindow.hide();
            }
            if (this.mIncomingBubble) {
                this.mIncomingBubble.hide();
            }
            Log.d(TAG, "toggleFloatFullWindow,always update");
            if (!this.mIncallingFullWindow) {
                this.mIncallingFullWindow = new IncallingFullWindow(this, this.callService);
            }
            this.mIncallingFullWindow.update(this.callService.getExistCallList(), this.callService.timecount, this.callService.audioState);
            this.toggleFullWindowFirst = false;
        }
        else if (windowType === ViewController.FLOAT_WINDOW) {
            Log.d(TAG, "switch to float window");
            if (DialsConstants.Customization.ENABLE_FLOAT_WINDOW) {
                // 隐藏全屏，显示浮窗
                Log.d(TAG, "hide full window, show float window");
                if (this.mIncallingFullWindow) {
                    this.mIncallingFullWindow.hide();
                }
                let callList = this.callService.getExistCallList();
                if (callList[0].status === CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING
                    || callList[0].status === CallService.CALLSTATE.TELE_CALL_STATUS_WAITING) {
                    if (!this.mIncomingBubble) {
                        this.mIncomingBubble = new IncomingBubble(this, this.callService);
                    }
                    this.mIncomingBubble.show();
                    return;
                }
                if (!this.mIncallingFloatWindow) {
                    this.mIncallingFloatWindow = new IncallingFloatWindow(this, this.callService);
                }
                if (this.toggleFloatWindowFirst) {
                    Log.d(TAG, "toggleFloatFullWindow, this is first time to toggle to float window, so use update");
                    this.mIncallingFloatWindow.update(this.callService.getExistCallList(), this.callService.timecount);
                    this.toggleFloatWindowFirst = false;
                }
                else {
                    Log.d(TAG, "toggleFloatFullWindow, this is first time to toggle to float window, so just show is ok");
                    this.mIncallingFloatWindow.show();
                }
            }
        }
    }
    toggleIncommingFullWindow(windowType) {
        Log.d(TAG, "toggleIncommingFullWindow called, will show window type: ", windowType);
        if (!this.mIncomingBubble) {
            this.mIncomingBubble = new IncomingBubble(this, this.callService);
        }
        if (!this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow = new IncomingCallFloatWindow(this, this.callService);
        }
        if (windowType === 0) {
            this.showIncomingWindow(this.callService.getExistCallList());
            this.mIncomingBubble.hide();
        }
        else {
            this.mIncomingCallFloatWindow.hide();
            if (windowType === 1) {
                this.mIncomingBubble.show();
            }
            else {
                this.mAiHoldFloatWindow.show();
            }
        }
    }
    ABSwitch() {
        Log.d(TAG, "ABSwitch resetViewPosition");
        if (this.mIncomingCallFloatWindow) {
            this.mIncomingCallFloatWindow.resetPosition();
        }
        if (this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.resetPosition();
        }
        if (this.mIncomingBubble) {
            this.mIncomingBubble.resetPosition();
        }
    }
    clearFloatTemporaryVariable(isClear) {
        if (isClear && this.mIncallingFloatWindow) {
            this.mIncallingFloatWindow.clearPhoneNameAndNumber();
        }
    }
    fetchCallName(callNumber, userData, cb) {
        ContactDao.getDao().getContactInfo(callNumber, (err, name, photo) => {
            Log.d(TAG, err, name);
            cb && cb(err, name, photo, userData);
        });
    }
}
ViewController.FLOAT_WINDOW = 0;
ViewController.FULL_WINDOW = 1;
module.exports = ViewController;
