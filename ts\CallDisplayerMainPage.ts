"use strict";

process.env.CAFUI2 = "true";
process.env.AGIL_NG_MODE = "ng";

import Page = require("extend/hdt/page/BasePage");
import PageLink = require("yunos/page/PageLink");
import WindowCAF = require("yunos/ui/view/Window");
import ViewController = require("./ui/ViewController");
import TrackerUtil = require("./utils/TrackerUtil");
const trackerUtil = TrackerUtil.getInstance();
import DialsConstants = require("./utils/DialsConstants");

import Resource = require("yunos/content/resource/Resource");
Resource.getInstance().addExtendPackages(["extend/hdt/control"]);

import Log = require("./utils/Logs");
const TAG = "CallDisplayerMainPage";

const CALL_HANGUP_CMD = "direction_control_hangup";
const CALL_ANSWER_CMD = "direction_control_answer";

interface IWindow {
    layoutFlags: number;
}

interface IHidable {
    hidePage: () => void;
}

interface IMyProcess {
    startCPUProfile: (param: string) => void;
    stopCPUProfile: (param: string, profile: string) => void;
    foregroundNotification: () => void;
    backgroundNotification: () => void;
}

const CPU_PROFILE = !true;
const ENABLE_SPLASH = false;
let PROFILE_FILE: string = "";
if (CPU_PROFILE) {
    var fsContext = require("yunos/cloudfs/Context").getInstance();
    PROFILE_FILE = fsContext.getLocalDir() + "/profile.cpuprofile";

    (<IMyProcess><object>process).startCPUProfile("input");
}

const FR_STATE_IDLE = 0;
const FR_STATE_STARTED = 1;
const FR_STATE_RECORDING = 2;
const FR_STATE_FINISHED = 3;

class CallDisplayerMainPage extends Page {
    private fastRenderState: number;
    private viewController: ViewController;
    private timer: NodeJS.Timer = null;

    constructor() {
        super();
        Log.d(TAG, "constructor called");
        this.fastRenderState = FR_STATE_IDLE;
    }

    // destructor() {
    //     Log.d(TAG, "destructor called");
    // }

    getWindowConfig() {
        Log.d(TAG, "getWindowConfig called");
        // this.openEnterAnimation = Page.OpenEnterAnimation.FadeIn;
        // this.closeExitAnimation = Page.CloseExitAnimation.FadeOut;
        // if (ENABLE_SPLASH) {
        //    return {extraSwitches: ["--fast-render-mode=true"]};
        // }
        // 用一个 1*1 大小的透明窗口，作为page的主窗口，用于接收onHide()回调
        return {
            left: 0,
            top: 0,
            width: 1,
            height: 1,
            opaque: false,
            type: 5,
            layoutFlags: 0x00010000
        };
    }

    /*
    get globalThemeReference(): string {
        return "persist.sys.aui.extend.setting";
    }
    */
    

    onCreate() {
        Log.d(TAG, "onCreate called");
        // log.writeBootLog("calldisplayer.yunos.com is ready");
    }

    onStart() {
        Log.d(TAG, "onStart called");

        let event = this.sourceLink.getEventName();
        if (event === "outgoing" || event === "incoming") {
            if (ENABLE_SPLASH) {
                this.fastRenderState = FR_STATE_STARTED;
            }
        } else {
            Log.w(TAG, "onStart, may app is unusually started");
        }

        this.window.showWhenLocked = true; // show beyond keyguard, it will set FLAG_SHOW_WHEN_LOCKED flag
        this.window.background = "transparent";

        this.viewController = new ViewController(this);
        // Page.getInstance().on("backKey", this.viewController.hideFullWindow.bind(this.viewController));
    }

    onLink(link: PageLink) {
        Log.d(TAG, "onLink called");
        if (DialsConstants.Customization.ENABLE_SECURITY_CHECK) {
            let referer = link.referer;
            if (referer) {
                let callingPageUri = referer.uri;
                if (!callingPageUri ||
                        !callingPageUri.startsWith("page://systemservice.alios.cn/") &&
                        !callingPageUri.startsWith("page://btcallservice.yunos.com/") &&
                        !callingPageUri.startsWith("page://calldisplayer.yunos.com/")) {
                    Log.w(TAG, "onLink ignored for comming from unknown module: ", callingPageUri);
                    //this.stopPage();
                    return;
                }
            }
        }

        let event = link.getEventName();
        let data = link.getData();
        Log.i(TAG, "come new link event =", event, ", needActive =", link.needActive, ", data =", data);

        if (DialsConstants.Customization.ENABLE_BLUR) {
            // blur background use wallpaper
            (<IWindow><object> this.window).layoutFlags |= 0x00100000; // WindowCAF.Flags.NeedBackground;
        }

        if (event === "fullscreen") {
            if (this.viewController && !this.viewController.isAnyCallExist()) {
                Log.w(TAG, "received fullscreen, but there are not any call, HIDE page!!");
                this.myHidePage();
            }
        } else if (event === CALL_HANGUP_CMD) {
            //sendlink page://calldisplayer.yunos.com/calldisplayer -e direction_control_hangup
            try{
                Log.d(TAG,"from dirction control hangup call");
                this.viewController.callService.isHangupOrAnswer = true;
                trackerUtil.commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "boardcontrol", pageType: this.viewController && this.viewController.callService &&
                    this.viewController.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", time: 0, result: "handup"
                });
                return;
            } catch (e) {
                Log.d(TAG, "hangup  catch json exception", e);
            }

        } else if (event === CALL_ANSWER_CMD){
            try {
                Log.d(TAG,"from dirction control anwer call zdsxxx");
                //sendlink page://calldisplayer.yunos.com/calldisplayer -e direction_control_answer
                this.viewController.callService.isHangupOrAnswer = true;
                trackerUtil.commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "boardcontrol", pageType: this.viewController && this.viewController.callService &&
                    this.viewController.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", time: 0, result: "pickup"
                });
                return;
            } catch (e) {
                Log.d(TAG, "answer  catch json exception", e);
            }
        } else if (event === "showFloatScreen") {
            if (this.viewController && this.viewController.isAnyCallExist()) {
                this.viewController.setThirdPartOutgoingCall(false);
                this.viewController.toggleFloatFullWindow(0);
            }
        }

        this.viewController.onLink(link);
    }

    onShow() {
        Log.i(TAG, "onShow called");
        (<IMyProcess><object>process).foregroundNotification();
        if (ENABLE_SPLASH) {
            // this.stopFastRender();
            Log.d(TAG, "onShow, this.fastRenderState = " + this.fastRenderState);
            if (this.fastRenderState === FR_STATE_RECORDING) {
                this.fastRenderState = FR_STATE_FINISHED;
            }
        }
    }

    onFirstFrameCompleted() {
        Log.d(TAG, "onFirstFrameCompleted called");
    }

    
    // this means fast render recorder is running
    isFastRenderRunning() {
        return false;
        // return this.fastRenderState === FR_STATE_STARTED || this.fastRenderState === FR_STATE_RECORDING;
    }

    myHidePage() {
        Log.d(TAG, "hidePage called");
        (<IHidable><object>Page.getInstance()).hidePage();
        trackerUtil.leavePage(TrackerUtil.TrackerPages.InCallMainPage, {});
    }

    onHide() {
        Log.d(TAG, "onHide called");
        (<IMyProcess><object>process).backgroundNotification();
        this.viewController.onHide();
    }

    onActive() {
        Log.d(TAG, "onActive called");
    }

    onInactive() {
        Log.d(TAG, "onInactive called");
    }

    onStop() {
        Log.d(TAG, "onStop called");
        this.viewController.onStop();
        if (this.timer) {
            clearTimeout(this.timer);
            this.timer = null;
        }
    }

    onRestart() {
        Log.d(TAG, "onRestart called");
    }

    // onTrimMemory(level: number) {
    //     Log.d(TAG, "onTrimMemory called");
    //     if (level >= 3) {
    //         this.viewController.trimMemory();
    //     }
    // }

    // _getOverrideWindowConfig() {
    //     return {
    //         opaque: false
    //     };
    // }

    onDestroy() {
        Log.d(TAG, "onDestroy called");
    }
}

export = CallDisplayerMainPage;
