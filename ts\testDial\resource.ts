"use strict";

import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();

var resource = {
    drawable: function (name: string) {
        // return DIR_DRAWABLE + "/" + name;
        let path = Res.getImageSrc("images/" + name);
        return path;
    },

    string: function() {
        let str = Res.getString.apply(Res, arguments);
        return str;
    },

    getPixelByDp: function(dp: number) {
        // return screenIns.getPixelByDp(dp);
        return 1.5 * dp;
    }
};

export = resource;
