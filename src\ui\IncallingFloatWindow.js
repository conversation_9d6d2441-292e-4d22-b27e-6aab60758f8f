/* 通话中，小的悬浮窗 */
"use strict";
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const LayoutManager = require("yunos/ui/markup/LayoutManager");
const WindowCAF = require("yunos/ui/view/Window");
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const Utils = require("../utils/Utils");
const BaseLayout = require("./BaseLayout");
const CallService = require("../services/CallService");
const Log = require("../utils/Logs");
const TrackerUtil = require("../utils/TrackerUtil");
const TAG = "IncallingFloatWindow";
class IncallingFloatWindow extends BaseLayout {
    constructor(controller, callService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();
        return;
    }
    createLayout() {
        this.mainView = LayoutManager.loadSync("MOFloatLayout.xml");
        this._view = this.mainView;
        this.mainView.left = 0;
        this.mainView.top = 0;
        this.mainView.width = 0;
        this.mainView.height = Res.getConfig("mo_float_window_height");
        this.mainView.clipBound = false;
        this.winParam = {};
        this.winParam.height = Res.getConfig("mo_float_window_height");
        // must know content's length, and then re-calculate width
        this.winParam.width = 0;
        this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        this.winParam.y = screenInstance.getPixelBySDp(Res.getConfig("mo_float_window_margin_top"));
        this.window = WindowCAF.create(this.controller.mainPage, {
            left: this.winParam.x,
            top: Utils.isLandscape() ? this.winParam.y : this.winParam.y + Utils.getStatusBarHeight(),
            width: this.winParam.width,
            height: this.winParam.height,
            type: 2006,
            layoutFlags: 0x00000008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        this.window.specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";
        this.root = this._view.findViewById("root");
        this.icon = this._view.findViewById("icon");
        this.callname = this._view.findViewById("callname");
        this.callState = this._view.findViewById("callState");
        Log.v(TAG, "IncallingFloatWindow createLayout", this.window.height, this.window.width);
        this.window.addChild(this.mainView);
        this.window.addEventListener("touchstart", this.onWindowTouchDown.bind(this));
        this.window.addEventListener("touchend", this.onWindowTouchUp.bind(this));
        this.window.addEventListener("touchcancel", this.onWindowTouchUp.bind(this));
        return;
    }
    update(param, timecount) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callname || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }
        let callCell = param[0];
        let call = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        }
        else {
            let info = callCell.callerInfo;
            this.callname.text = this._callnameText || info.name;
        }
        this.callState.text = this.controller.callStatusToString(callCell, timecount);
        this.resizeWindow();
        // if (!this.visible) {
        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;
        // } else {
        //     Log.d(TAG, "has shown already");
        // }
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    this.callname.text = name;
                }
                else {
                    this._callnameText = call.lineid;
                    this.callname.text = call.lineid;
                }
                this.resizeWindow();
            });
        }
    }
    updateCallState(callList, curtime) {
        let callCell = callList[0];
        let call = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);
        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    this.callname.text = name;
                }
                else {
                    this._callnameText = call.lineid;
                    this.callname.text = call.lineid;
                }
            });
            return;
        }
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        }
        else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
            this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
        }
        else {
            this.callState.text = this.controller.callStatusToString(callCell, curtime);
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount, " this.callname.text=", this.callname.text, " this.callState.text=", this.callState.text);
        this.resizeWindow();
    }
    hide() {
        //if (this.visible) {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
        TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CallPage, { from: "bluetooth" });
        Log.d(TAG, "set this.visible to false");
        // }
    }
    show() {
        // if (!this.visible) {
        this.window.show();
        this.visible = true;
        TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CallPage, null, {});
        Log.d(TAG, "show called, set this.visible to true");
        // }
    }
    switchToFullScreen() {
        Log.d(TAG, "switchToFullScreen called");
        this.controller.closeNotificationCenter();
        if (this.controller.mainPage && this.controller.mainPage.destroyed === false
            && this.controller.mainPage.pageapi) {
            // main page is still alive
            Log.d(TAG, "calldisplayer main page is still alive");
            this.controller.toggleFloatFullWindow(1); // ViewController.FULL_WINDOW);
            this.controller.sendLink2MainPage();
        }
        else {
            // should not come here
            Log.e(TAG, "why call displayer main page not alive?");
        }
    }
    onWindowTouchDown() {
        if (this.root) {
            this.root.background = Res.getImageSrc("images/qms_tip_bg_dialing_pressed.png");
        }
    }
    onWindowTouchUp() {
        if (this.root) {
            this.root.background = Res.getImageSrc("images/qms_tip_bg_dialing_normal.png");
        }
        this.switchToFullScreen();
    }
    resetPosition() {
        this.mainView.top = this.mainView.left = 0;
        this.window.left = this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        this.window.top = this.winParam.y = Utils.isLandscape() ? screenInstance.getPixelBySDp(Res.getConfig("mo_float_window_margin_top")) : screenInstance.getPixelBySDp(Res.getConfig("mo_float_window_margin_top")) + Utils.getStatusBarHeight();
    }
    resizeWindow() {
        let nameWidth = this.callname.width;
        let nameWidthMax = (Utils.isLandscape() ? Utils.getScreenWidth() : Utils.getScreenHeight()) / 3;
        if (nameWidth >= nameWidthMax) {
            nameWidth = nameWidthMax;
            this.callname.width = nameWidthMax;
        }
        else {
            this.callname.width = nameWidth;
        }
        let stateWidth = this.callState.width;
        // window width = margin + spacing + icon width + name width + state width
        this.winParam.width = (Res.getConfig("mo_float_window_margin_left") * 2 +
            Res.getConfig("mo_float_window_spacing") * 2 +
            Res.getConfig("mo_float_windoe_icon_width") +
            (nameWidth) + (stateWidth));
        Log.v(TAG, "resizeWindow: ", (nameWidth), (stateWidth), this.winParam.width);
        this.root.width = this.winParam.width;
        this.mainView.width = this.winParam.width;
        this.window.width = this.winParam.width;
        this.winParam.x = (Utils.getScreenWidth() - this.winParam.width) / 2;
        if (Utils.isLandscape()) {
            this.winParam.x += Utils.getStatusBarHeight();
        }
        this.window.left = this.winParam.x;
    }
    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        this.callname.text = "";
        this.callState.text = "";
        this.resizeWindow();
    }
    clearPhoneNameAndNumber() {
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }
}
module.exports = IncallingFloatWindow;
