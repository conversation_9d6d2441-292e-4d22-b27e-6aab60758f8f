/* 来电悬浮窗 */
"use strict";
process.env.CAFUI2 = "true";
const Window = require("yunos/ui/view/Window");
const KeyframeAnimation = require("yunos/ui/animation/KeyframeAnimation");
const PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
const LayoutManager = require("yunos/ui/markup/LayoutManager");
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
const View = require("yunos/ui/view/View");
const VoiceCommand = require("yunos/ui/voice/VoiceCommand");
const Log = require("../utils/Logs");
const BaseLayout = require("./BaseLayout");
const TrackerUtil = require("../utils/TrackerUtil");
const Utils = require("../utils/Utils");
const DialsConstants = require("../utils/DialsConstants");
const TAG = "IncomingCallFloatWindow";
class IncomingCallFloatWindow extends BaseLayout {
    ;
    constructor(controller, callService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.standWindowHeight = Res.getConfig("mt_float_window_height") +
            (DialsConstants.Customization.ENABLE_HAND_MOTION ? Res.getConfig("mt_float_guide_height") : 0);
        this.standWindowHeight = screenInstance.getPixelBySDp(this.standWindowHeight);
        let screenSize = this.getScreenSize();
        let win = Window.create(this.controller.mainPage, {
            top: Utils.isLandscape() ? 0 : Utils.getStatusBarHeight(),
            left: 0, // Utils.isLandscape() ? Utils.getStatusBarHeight() : 0,
            width: screenSize.width,
            height: this.standWindowHeight,
            type: 2006,
            layoutFlags: 0x00000008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: Window.Orientation.FollowUnderWindow
            // autoOrientation: true
        });
        Log.v(TAG, "constructor 1");
        win.title = "IncomingCallFloatWindow";
        win.showWhenLocked = true; // show beyond keyguard
        win.specifiedMode = 1; // exclusive Mode for voice and motion event
        win.background = "transparent";
        win.on("orientation", function (orientation) {
            Log.d(TAG, "orientation changed.", orientation, this.window.width);
            this.orientation = orientation;
            this.setWindowAndRootView();
        }.bind(this));
        this.window = win;
        this.orientation = this.window.getSystemOrientation();
        Log.d(TAG, "constructor orientation = ", this.window.getSystemOrientation());
        Log.v(TAG, "constructor 2");
        this.controller = controller;
        this.callService = callService;
        this._view = LayoutManager.loadSync("MTFloatLayout.xml");
        Log.v(TAG, "constructor 3");
        if (DialsConstants.Customization.ENABLE_BLUR) {
            this._view.enableBehindWindowsBlur = true;
            this._view.background = Res.getConfig("color_blur_cover");
        }
        this._view.top = -this.standWindowHeight;
        this._view.left = 0;
        this.window.addChild(this._view);
        Log.v(TAG, "constructor end");
        this.visible = false;
        this.root = this._view.findViewById("root");
        this.mtFloat = this._view.findViewById("mtFloat");
        this.avatar = this._view.findViewById("avatar");
        this.userInfo = this._view.findViewById("userInfo");
        this.callname = this._view.findViewById("callname");
        this.number = this._view.findViewById("number");
        this.hangup = this._view.findViewById("hangUp");
        this.answer = this._view.findViewById("answer");
        this.mtGestureGuide = this._view.findViewById("mtGestureGuide");
        this.twoFingered = this._view.findViewById("twoFingered");
        this.guideText = this._view.findViewById("guideText");
        this.initVoiceControl();
        this.setWindowAndRootView();
        this.addListener();
    }
    setWindowAndRootView() {
        let screenSize = this.getScreenSize();
        Log.d(TAG, "setWindowAndRootView window width: ", this.window.width, screenSize.width);
        this.root.width = screenSize.width;
        // this.root.height = Res.getConfig("mt_float_window_height");
        this.window.width = screenSize.width;
        this.window.height = this.standWindowHeight;
        Log.d(TAG, "setWindowAndRootView window width 1: ", this.window.width, screenSize.width);
    }
    shockAnimStart() {
        Log.d(TAG, "shockAnimStart");
        if (!this.shockAnim) {
            let shockAnim = new KeyframeAnimation(this.root);
            shockAnim.duration = 1000;
            shockAnim.iterationCount = "infinite";
            shockAnim.keyframes = {
                from: {
                    value: { top: 0 }
                },
                "50%": {
                    value: { top: this.standWindowHeight * 0.05 } // ,
                    // timingFunction: KeyframeAnimation.TimingFunction.EaseIn
                },
                "100%": {
                    value: { top: 0 } // ,
                    // timingFunction: KeyframeAnimation.TimingFunction.EaseOut
                }
            };
            this.shockAnim = shockAnim;
        }
        Log.d(TAG, "shockAnimStart start");
        this.shockAnim.start();
    }
    dropDownAnimInit() {
        if (!this.dropDownAnim) {
            let dropDownAnim = new PropertyAnimation(this._view);
            dropDownAnim.duration = 400;
            dropDownAnim.timingFunction = "easeIn";
            dropDownAnim.iterationCount = 1;
            dropDownAnim.from = { top: -this.standWindowHeight };
            dropDownAnim.to = { top: 0 };
            this.dropDownAnim = dropDownAnim;
            this.dropDownAnim.addEventListener("complete", () => {
                this.window.background = Res.getImageSrc("images/qms_call_bg_toast_answer_shadow.png");
            });
        }
        this.dropDownAnim.start();
    }
    moveUpAnimInit() {
        if (!this.moveUpAnim) {
            let moveUpAnim = new PropertyAnimation(this._view);
            moveUpAnim.duration = 400;
            moveUpAnim.timingFunction = "easeOut";
            moveUpAnim.iterationCount = 1;
            moveUpAnim.from = { top: 0 };
            moveUpAnim.to = { top: -this.standWindowHeight };
            this.moveUpAnim = moveUpAnim;
            this.moveUpAnim.addEventListener("complete", function () {
                this.hide();
            }.bind(this));
        }
        this.moveUpAnim.start();
    }
    initVoiceControl() {
        this.hangup._voiceControlEnabled = true;
        this.hangup.voiceSelectMode = View.VoiceSelectMode.Custom;
        let hangupCommand = new VoiceCommand();
        hangupCommand.customCommands = Res.getConfig("hangup_commands");
        hangupCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        hangupCommand.activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        this.hangup.addVoiceCommand(hangupCommand);
        this.hangup.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice hangup");
            var hangupType = { hupScene: TrackerUtil.HANGUP_SCENE.PAD,
                hupWay: TrackerUtil.HANGUP_WAY.VOICE };
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                type: "voice", pageType: this.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", result: "handup"
            });
            //Log.d(TAG,"TrackerUtil.TrackerEvents.CardShow on screen off");
        });
        this.answer._voiceControlEnabled = true;
        this.answer.voiceSelectMode = View.VoiceSelectMode.Custom;
        let answerCommand = new VoiceCommand();
        answerCommand.customCommands = Res.getConfig("answer_commands");
        answerCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        answerCommand.activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        this.answer.addVoiceCommand(answerCommand);
        this.answer.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice answer");
            this.callService.answer(0, null, 0); // no need to set answer type as it not used in tracker
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                type: "voice", pageType: this.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", result: "pickup"
            });
        });
    }
    getScreenSize() {
        return {
            width: Utils.getTotalScreenWidth(), // 获取全屏幕的宽度，横屏时也包含状态栏的宽度
            height: Utils.getScreenHeight()
        };
    }
    addListener() {
        this.hangup.addEventListener("touchend", this.onHangup.bind(this));
        this.answer.addEventListener("touchend", this.onAnswer.bind(this));
        this.root.addEventListener("touchstart", this.onTouchDown.bind(this));
        this.root.addEventListener("touchend", this.onTouchUp.bind(this));
    }
    update(param) {
        Log.d(TAG, "update orientation =", this.orientation);
        if (param) {
            var callCell = param[0];
            var call = callCell.callinfo || callCell;
            if (!callCell.callerInfo) {
                this.callname.text = call.lineid;
                this.number.visibility = View.Visibility.None;
            }
            this.number.text = call.lineid;
            if (callCell.callerInfo) {
                Log.v(TAG, "MO update 2: ", callCell.callerInfo);
                let info = callCell.callerInfo;
                this.callname.text = this._callnameText || info.name;
                this.avatar.height = this.avatar.width;
                // this.avatar.radius = this.avatar.height / 2;
                if (info.photoBuffer) {
                    this.avatar.src = info.photoBuffer;
                }
                else if (info.photoUri && info.photoUri.startsWith("page://")) {
                    this.avatar.src = info.photoUri;
                }
                else if (info.photoUri && !callCell.isLocalContact) {
                    this.avatar.src = "file://" + info.photoUri;
                }
                else {
                    this.avatar.src = Res.getImageSrc("images/qms_call_icon_portrait_toast.png");
                }
                Log.v(TAG, "update avatar.src=", this.avatar.src);
                if (callCell.isStrangeNumber) {
                    this.number.visibility = View.Visibility.None;
                }
                else {
                    this.number.visibility = View.Visibility.Visible;
                }
            }
        }
        else {
            Log.w(TAG, "param is null");
        }
        // if (this._nameUpdatedNumber !== call.lineid) {
        //     this._nameUpdatedNumber = call.lineid;
        this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
            if (!err && name) {
                this._callnameText = name;
                this.callname.text = name;
                this.number.text = call.lineid;
                this.number.visibility = View.Visibility.Visible;
            }
        });
        // }
        if (!this.visible) {
            Log.d(TAG, "show");
            this.window.show();
            this.visible = true;
            if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
                this.mtGestureGuide.visibility = View.Visibility.Visible;
            }
            this.dropDownAnimInit();
            TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.IncomingLeavePage, null, {});
        }
        else {
            Log.d(TAG, "has shown already");
        }
        this.root.height = this.standWindowHeight;
        this.window.height = this.standWindowHeight;
    }
    hide() {
        Log.d(TAG, "hide called, this.visible=", this.visible);
        if (this.visible) {
            this.window.hide();
            this.visible = false;
            if (this._view) {
                this._view.top = -this.standWindowHeight;
            }
            TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.IncomingLeavePage, { type: TrackerUtil.INTERFACE_TYPE.BLUETOOTH_INTERFACE, from: TrackerUtil.FROM_TYPE.INCOMINT_WINDOW });
        }
    }
    onHangup(e) {
        Log.d(TAG, "onHangup called");
        this.controller.closeNotificationCenter();
        this.moveUpAnimInit();
        var hangupType = { hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
            hupWay: TrackerUtil.HANGUP_WAY.CLICK };
        this.callService.hangup(hangupType, "");
        TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
            type: "byhand", pageType: this.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", result: "handup"
        });
        //Log.d(TAG,"TrackerUtil.TrackerEvents.CardShow on screen off");
        if (Utils.isScrrenOff()) {
            TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickCallCard, {
                type: "hangup"
            });
        }
        e.stopPropagation();
    }
    onAnswer(e) {
        Log.d(TAG, "onAnswer called");
        this.controller.closeNotificationCenter();
        this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
        TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
            type: "byhand", pageType: this.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", result: "pickup"
        });
        if (Utils.isScrrenOff()) {
            TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickCallCard, {
                type: "answer"
            });
        }
        e.stopPropagation();
        this.hide();
    }
    onTouchDown(ev) {
        this.controller.closeNotificationCenter();
        // Log.d(TAG, "onTouchDown called", ev);
        this.startX = ev.touches[0].screenX;
        this.startY = ev.touches[0].screenY;
        this.diffX = Infinity;
        this.diffY = 5 * 2;
    }
    onTouchMove(ev) {
        let x = ev.touches[0].screenX;
        let y = ev.touches[0].screenY;
        // Log.d(TAG, "onTouchMove called", this.Dragging, this.startY, y);
        if (!this.Dragging && (Math.abs(this.startX - x) > this.diffX ||
            Math.abs(this.startY - y) > this.diffY)) {
            Log.v(TAG, "onTouchMove enter drag");
            this.Dragging = true;
            this.window.width = Utils.getScreenWidth();
            this.window.height = this.standWindowHeight;
        }
        else if (this.Dragging) {
            // this.window.height = this.standWindowHeight + y - this.startY;
            // this.window.height = this.standWindowHeight;
            this.root.height = this.standWindowHeight + y - this.startY;
            // this.window.windowHeight = this.standWindowHeight + ev.y() - this.startY;
        }
    }
    onTouchUp() {
        if (!this.Dragging) {
            this.Dragging = false;
            return;
        }
        this.Dragging = false;
        if (this.root.height < this.standWindowHeight / 2) {
            this.window.height = 0;
            this.root.height = 0;
            let hangupType = { hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
                hupWay: TrackerUtil.HANGUP_WAY.DRAG };
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ClickIncomingHanup, {
                type: "byhand", pageType: this.callService.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow"
            });
        }
        else if (this.root.height > screenInstance.getPixelByDp(Res.getConfig("mt_float_window_height")) / 2) {
            this.root.height = 0;
            this.window.height = 0;
            this.callService.answer(TrackerUtil.ANSWER_TYPE.DRAG, null, 0);
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.CallResult, {
                type: "byhand"
            });
        }
        else {
            this.root.height = this.standWindowHeight;
            this.window.height = this.standWindowHeight;
        }
    }
    onMotionDialogVisible(visible) {
        Log.d(TAG, "onMotionDialogVisible, visible = " + visible);
        this.mtGestureGuide.visibility = visible ? View.Visibility.None : View.Visibility.Visible;
    }
    resetUIContent() {
        if (this.avatar) {
            this.avatar.src = Res.getImageSrc("images/qms_call_icon_portrait_toast.png");
        }
        if (this.callname) {
            this.callname.text = "";
        }
        if (this.number) {
            this.number.text = "";
        }
        this._callnameText = null;
    }
}
module.exports = IncomingCallFloatWindow;
