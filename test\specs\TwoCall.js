"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");
const TouchEvent = require("yunos/ui/event/TouchEvent");
var View = require("yunos/ui/view/View");

const addCall = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 1
};

// huangup call
const huangupcall = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const ringcall = {
    _subId: 0,
    _callId: 1,
    _state: 4,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const activecall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const holdCall = {
    _subId: 0,
    _callId: 1,
    _state: 2,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const secondAddCall = {
    _subId: 0,
    _callId: 2,
    _state: 3,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

// const secondAddIncomingCall = {
//     _subId: 0,
//     _callId: 2,
//     _state: 6,
//     _displayName: "",
//     _callNumber: "13911727181",
//     _multiParty: 0,
//     _emergencyCall: 0,
//     _disconnectCause: -1,
//     _capability: 34,
//    _phoneType: 1
// };

const secondRingingCall = {
    _subId: 0,
    _callId: 2,
    _state: 4,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondActiveCall = {
    _subId: 0,
    _callId: 2,
    _state: 1,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondHoldCall = {
    _subId: 0,
    _callId: 2,
    _state: 2,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const secondHuangupCall = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondConfCall = {
    _subId: 0,
    _callId: 2,
    _state: 1,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

const ConfCall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 40,
    _phoneType: 1
};

const secondHuangupConfCall = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

// const secondConfHoldCall = {
//     _subId: 0,
//     _callId: 2,
//     _state: 2,
//     _displayName: "",
//     _callNumber: "13911727181",
//     _multiParty: 1,
//     _emergencyCall: 0,
//     _disconnectCause: -1,
//     _capability: 1734955885,
//     _phoneType: 1
// };

// const confHoldCall = {
//     _subId: 0,
//     _callId: 1,
//     _state: 2,
//     _displayName: "",
//     _callNumber: "15800000000",
//     _multiParty: 1,
//     _emergencyCall: 0,
//     _disconnectCause: -1,
//     _capability: 1734955885,
//     _phoneType: 1
// };

const touchup = new TouchEvent({
    type: "touchend",
    changedTouches: [{
        clientX: 100,
        clientY: 10,
        identifier: 0
    }],
    eventPhase: TouchEvent.EventPhase.Target
});

function twoCallCheck() {
    utTools.log("TwoCall twoCallCheck");
    const controler = uiInf.getControler();
    const moLayout = uiInf.getMOLayout();
    utTools.equal(controler.appVisible, true);

    const service = uiInf.getService();
    utTools.equal(service.callItemSize(), 2);
    // visible views
    utTools.equal(moLayout.convertCall.visibility, View.Visibility.Visible);
    utTools.equal(moLayout.mergeCall.visibility, View.Visibility.Visible);
    // utTools.equal(moLayout.holdInfo.visibility, View.Visibility.Visible);

    // hiden views
    utTools.equal(moLayout.holdCall.visibility, View.Visibility.None);
    utTools.equal(moLayout.addCall.visibility, View.Visibility.None);

    utTools.equal(moLayout.call1Number.text, addCall._callNumber);
}

function oneCallCheck() {
    utTools.log("TwoCall oneCallCheck");
    const controler = uiInf.getControler();
    const moLayout = uiInf.getMOLayout();
    utTools.equal(controler.appVisible, true);
    utTools.viewEqual(uiInf.getTopLayout(), moLayout);
    utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
    const service = uiInf.getService();
    utTools.equal(service.callItemSize(), 1);
    // hiden views
    utTools.equal(moLayout.convertCall.visibility, View.Visibility.None);
    utTools.equal(moLayout.mergeCall.visibility, View.Visibility.None);
    // utTools.equal(moLayout.holdInfo.visibility, View.Visibility.None);

    // visible views
    utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
    utTools.equal(moLayout.addCall.visibility, View.Visibility.Visible);
}

describe("TwoCall", function() {
    utTools.log("TwoCall start");

    let originalTimeout;
    let huangupFirstCall = true;
    let huangupSecondCall = true;
    let callAfterEach = true;

    beforeAll(() => {
        utTools.log("TwoCall beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("TwoCall afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("TwoCall beforeEach");

        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall beforeEach run");
            utTools.MockInCallService.emit("calladded", addCall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", ringcall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            yield 3000;
            utTools.MockInCallService.emit("calladded", secondAddCall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", secondRingingCall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", secondActiveCall);
            yield 3000;
            twoCallCheck();
        }());
    });

    afterEach((done) => {
        utTools.log("TwoCall afterEach");

        if (!callAfterEach) {
            done();
            callAfterEach = true;
            return;
        }

        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall afterEach run");
            if (huangupFirstCall) {
                utTools.MockInCallService.emit("callstatechanged", huangupcall);
            }
            if (huangupSecondCall) {
                utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            }
            yield 3000;

            const controler = uiInf.getControler();
            utTools.viewEqual(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
            huangupFirstCall = true;
            huangupSecondCall = true;
        }());

    });

    it("convert -> convert", function(done) {
        utTools.log("TwoCall \"convert -> convert\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall \"convert -> convert\" run");
            const moLayout = uiInf.getMOLayout();

            // switch
            moLayout.convertCall.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            utTools.MockInCallService.emit("callstatechanged", secondHoldCall);
            yield 3000;
            utTools.equal(moLayout.callname.text, activecall._callNumber);
            utTools.equal(moLayout.call1Number.text, secondHoldCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);

            // switch again
            moLayout.convertCall.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("callstatechanged", secondActiveCall);
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            yield 3000;
            utTools.equal(moLayout.callname.text, secondActiveCall._callNumber);
            utTools.equal(moLayout.call1Number.text, holdCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
        }());
    });

    it("huangup front", function(done) {
        utTools.log("TwoCall \"huangup front\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall \"huangup front\" run");
            const moLayout = uiInf.getMOLayout();

            // hangup
            moLayout.hangUp.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.discParams, [secondHuangupCall._callId]);
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            utTools.equal(moLayout.callname.text, activecall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            huangupSecondCall = false;
        }());
    });

    it("huangup background", function(done) {
        utTools.log("TwoCall \"huangup background\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall \"huangup background\" run");
            const moLayout = uiInf.getMOLayout();

            // hangup
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            utTools.equal(moLayout.callname.text, secondActiveCall._callNumber);
            oneCallCheck();
            huangupFirstCall = false;
        }());
    });

    it("conference -> hangup second", function(done) {
        utTools.log("TwoCall \"conference -> hangup second\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("TwoCall \"conference -> hangup second\" run");
            const moLayout = uiInf.getMOLayout();

            // conference
            moLayout.mergeCall.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.conferenceParams, [secondHuangupCall._callId]);
            utTools.MockInCallService.emit("callstatechanged", ConfCall);
            utTools.MockInCallService.emit("callstatechanged", secondConfCall);
            yield 3000;
            utTools.equal(moLayout.number.text, "");
            utTools.strEqual(moLayout.callname.text, "TEXT_CONFERENCE_CALL");
            const controler = uiInf.getControler();
            utTools.equal(controler.appVisible, true);
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            // hiden views
            utTools.equal(moLayout.convertCall.visibility, View.Visibility.None);
            utTools.equal(moLayout.mergeCall.visibility, View.Visibility.None);
            // utTools.equal(moLayout.holdInfo.visibility, View.Visibility.None);

            // visible views
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.addCall.visibility, View.Visibility.Visible);

            // hangup
            utTools.MockInCallService.emit("callstatechanged", secondHuangupConfCall);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.None);
            utTools.equal(moLayout.callname.text, activecall._callNumber);
            oneCallCheck();

            huangupSecondCall = false;
        }());
    });

    // it("conference -> hold -> unhold", function(done) {
    //     utTools.asyncRun(done, function *() {
    //         const moLayout = uiInf.getMOLayout();
    //
    //         // conference
    //         moLayout.mergeCall.dispatchEvent("touchend", touchup);
    //         utTools.ObjectEqual(utTools.MockInCallService.conferenceParams, [secondHuangupCall._callId]);
    //         utTools.MockInCallService.emit("callstatechanged", ConfCall);
    //         utTools.MockInCallService.emit("callstatechanged", secondConfCall);
    //         yield 3000;
    //         utTools.equal(moLayout.number.text, "");
    //         utTools.strEqual(moLayout.callname.text, "TEXT_CONFERENCE_CALL");
    //         const controler = uiInf.getControler();
    //         utTools.equal(controler.appVisible, true);
    //         utTools.viewEqual(uiInf.getTopLayout(), moLayout);
    //         utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
    //         // hiden views
    //         utTools.equal(moLayout.convertCall.visibility, View.Visibility.None);
    //         utTools.equal(moLayout.mergeCall.visibility, View.Visibility.None);
    //         utTools.equal(moLayout.holdInfo.visibility, View.Visibility.None);
    //
    //         // visible views
    //         utTools.equal(moLayout.meeting.visibility, View.Visibility.Visible);
    //         utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
    //         utTools.equal(moLayout.addCall.visibility, View.Visibility.Visible);
    //
    //         // hold
    //         moLayout.hold.dispatchEvent("touchend", touchup);
    //         utTools.MockInCallService.emit("callstatechanged", secondConfHoldCall);
    //         utTools.MockInCallService.emit("callstatechanged", confHoldCall);
    //         yield 3000;
    //         utTools.equal(moLayout.meeting.visibility, View.Visibility.None);
    //         utTools.viewEqual(uiInf.getTopLayout(), moLayout);
    //
    //         huangupSecondCall = false;
    //     }());
    // });
});
