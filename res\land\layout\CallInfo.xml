<CompositeView id="callInfoRoot" layout="{layout.callInfoLayout}" width="{sdp(875)}" height="{sdp(632)}" >
    <CompositeView id="callInfoOne" layout="{layout.callInfoOneItemLayout}" width="{sdp(875)}" height="{sdp(632)}">
        <ImageView id="avatar" src="{img(images/qms_call_icon_portrait.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(144)}" width="{sdp(144)}"/>
        <ImageView id="avatarDing" src="{img(images/qms_call_btn_dingding_large.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(46)}" width="{sdp(46)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callname" align="{enum.TextView.Align.Left}" height="{sdp(40)}" width="{sdp(256)}" propertySetName="extend/hdt/FontTitle2" color="{theme.color.White_1}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callState" align="{enum.TextView.Align.Left}" height="{sdp(36)}" width="{sdp(224)}" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_3}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
    </CompositeView>
    <CompositeView id="callInfoTwo" layout="{layout.callInfoTwoItemLayout}"  width="{sdp(875)}" height="{sdp(632)}" visibility="{enum.View.Visibility.None}">
        <ImageView id="avatarOne" src="{img(images/qms_call_icon_portrait.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(144)}" width="{sdp(144)}"/>
        <ImageView id="avatarOneDing" src="{img(images/qms_call_btn_dingding_large.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(46)}" width="{sdp(46)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callnameOne" align="{enum.TextView.Align.Center}" height="{sdp(40)}" width="{sdp(224)}" propertySetName="extend/hdt/FontBody1" color="{theme.color.White_1}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callStateOne" align="{enum.TextView.Align.Center}" height="{sdp(36)}" width="{sdp(256)}" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_3}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <ImageView id="avatarTwo" src="{img(images/qms_call_icon_portrait.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(144)}" width="{sdp(144)}"/>
        <ImageView id="avatarTwoDing" src="{img(images/qms_call_btn_dingding_large.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
            height="{sdp(46)}" width="{sdp(46)}" visibility="{enum.View.Visibility.None}"/>
        <TextView id="callnameTwo" align="{enum.TextView.Align.Center}" height="{sdp(40)}" width="{sdp(224)}" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_4}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        <TextView id="callStateTwo" align="{enum.TextView.Align.Center}" height="{sdp(36)}" width="{sdp(256)}" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_4}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
    </CompositeView>
</CompositeView>
