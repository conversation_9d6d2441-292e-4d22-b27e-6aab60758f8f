"use strict";

import DataAgent = require("yunos/datadriver/DataAgent");
import Util = require("yunos/contextagent/Util");

import Utils = require("./Utils");
import Log = require("./Logs");
const TAG = "TrackerUtil";
const MAIN_PAGE_NAME = "page://calldisplayer.yunos.com/calldisplayer";
const MAIN_PAGE_TITLE = "inCall";
const CARD_CLICK_TITLE = "callCardShow";
const INCOMING_HANDUP_CLICK_TILE = "incomingCall";
const APP_KEY = "4f850fa149af8967";
const TRACK_VERSION = "1.0.2";
const APP_NAME = "calldisplayer.yunos.com";
var sInstance: TrackerUtil = null;

class TrackerPage {
    private _name: string;
    private _title: string;

    constructor(name: string, title: string) {
        this._name = name;
        this._title = title;
    }

    get name() {
        return this._name;
    }

    get title() {
        return this._title;
    }

    toString() {
        return "{Page}" + this._name + this._title;
    }
}

const TrackerPages = {
    InCallMainPage: new TrackerPage(MAIN_PAGE_NAME, MAIN_PAGE_TITLE),
    CallCardClickPage: new TrackerPage(MAIN_PAGE_NAME, CARD_CLICK_TITLE),
    IncomingHanupClickPage: new TrackerPage(MAIN_PAGE_NAME, INCOMING_HANDUP_CLICK_TILE),
    IncomingLeavePage: new TrackerPage(MAIN_PAGE_NAME, "incomingCallPage"),
    IncomingCallResultPage: new TrackerPage(MAIN_PAGE_NAME, "incomingCallResult"),
    TalkingCallPage: new TrackerPage(MAIN_PAGE_NAME, "talkingPage"),
    CallPage: new TrackerPage(MAIN_PAGE_NAME, "callPage"),
    CommunicationsAssistantPage: new TrackerPage(MAIN_PAGE_NAME, "communicationsAssistantPage"),
    CommunicationsAssistantCallback: new TrackerPage(MAIN_PAGE_NAME, "communicationsAssistantCallback"),
    CommunicationsAssistantSmallCardPage: new TrackerPage(MAIN_PAGE_NAME, "communicationsAssistantSmallCardPage")
};

class TrackerCtrl {
    private _page: TrackerPage;
    private _name: string;

    constructor(trackerPage: TrackerPage, clickName: string) {
        this._page = trackerPage;
        this._name = clickName;
    }

    get page() {
        return this._page;
    }

    get name() {
        return this._name;
    }

    toString() {
        return this._page.toString() + " -> {Ctrl}" + this._name;
    }
}

const TrackerCtrls = {
    ClickContact: new TrackerCtrl(TrackerPages.InCallMainPage, "widget_click"),
    ClickNote: new TrackerCtrl(TrackerPages.InCallMainPage, "widget_click"),
    ClickCallCard: new TrackerCtrl(TrackerPages.CallCardClickPage,"callQuickClick"),
    ClickShrink: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_shrink"),
    ClickBigWindow: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_bigWindow"),
    ClickDial: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_dial"),
    ClickAudioSwitch: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_audioSwitch"),
    ClickMute: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_mute"),
    ClickReject: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_reject"),
    ClickMergeTalk: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_mergeTalk"),
    ClickSwitchTalk: new TrackerCtrl(TrackerPages.TalkingCallPage,"talkingPage_switchTalk"),
    ClickExtend: new TrackerCtrl(TrackerPages.IncomingLeavePage,"incomingCallPage_extend"),
    ClickIcomingShrik: new TrackerCtrl(TrackerPages.IncomingLeavePage,"incomingCallPage_shrink"),
    ClickComShrink: new TrackerCtrl(TrackerPages.CommunicationsAssistantPage,"communicationsAssistantCallback_shrink"),
    ClickComExtend: new TrackerCtrl(TrackerPages.CommunicationsAssistantSmallCardPage,"communicationsAssistantSmallCardPage_extend")
    //ClickIncomingHanup: new TrackerCtrl(TrackerPages.IncomingHanupClickPage,"hangUpClick")
};

class TrackerEvent {
    private _page: TrackerPage;
    private _name: string;

    constructor(trackerPage: TrackerPage, eventName: string) {
        this._page = trackerPage;
        this._name = eventName;
    }

    get page() {
        return this._page;
    }

    get name() {
        return this._name;
    }

    toString() {
        return this._page.toString() + " -> {Event}" + this._name;
    }
}

const TrackerEvents = {
    CallEnd: new TrackerEvent(TrackerPages.InCallMainPage, "callingEnd"),
    CardShow: new TrackerEvent(TrackerPages.InCallMainPage, "phoneCardShow"),
    CallResult: new TrackerEvent(TrackerPages.InCallMainPage, "incomingCallResult"),
    ClickIncomingHanup: new TrackerEvent(TrackerPages.InCallMainPage,"hangUpClick"),
    InCall: new TrackerEvent(TrackerPages.InCallMainPage, "inCall"),
    IncomingResult: new TrackerEvent(TrackerPages.IncomingCallResultPage, "answer"),
    Call: new TrackerEvent(TrackerPages.CallPage, "call"),
    NoEvent: new TrackerEvent(TrackerPages.TalkingCallPage, ""),
    RESULT: new TrackerEvent(TrackerPages.TalkingCallPage, "result"),
    ComIncomingResult: new TrackerEvent(TrackerPages.CommunicationsAssistantCallback, "communicationsAssistantCallbackResult"),
    ComSmallIncomingResult: new TrackerEvent(TrackerPages.CommunicationsAssistantSmallCardPage, "result"),
    ComAssIncomingResult: new TrackerEvent(TrackerPages.CommunicationsAssistantPage, "communicationsAssistantPageResult"),
    InCallResult: new TrackerEvent(TrackerPages.CallPage, "callPageResult"),
    IncomingBubbleResult: new TrackerEvent(TrackerPages.IncomingCallResultPage, "incomingCallResultAnswer")
};

class TrackerUtil {
    private pageSet: Set<string> = null;

    static TrackerPages = TrackerPages;
    static TrackerEvents = TrackerEvents;
    static TrackerCtrls = TrackerCtrls;

    static readonly PAGENAME_INCALL = "InCall";
    static readonly PAGENAME_IDLE = "Idle";
    static readonly HANGUP_TYPE = {
        INVALID: -1,
        IN_PAD_CLICK: 0,
        IN_FLOAT_CLICK: 1,
        IN_FLOAT_DRAG: 2,
        IN_SMS_CUSTOM: 3,
        IN_SMS_NORMAL: 4,
        OUT_PAD_CLICK: 5,
        OUT_FLOAT_CLICK: 6,
        CALL_PAD_CLICK: 7,
        CALL_FLOAT_CLICK: 8,
        CALL_CONF_SPETIAL: 9,
        PAGELINK: 10,
        PASSIVE: 11
    };

    static readonly CALL_TYPE = {
        VOICE: "voice",
        VOLTE: "volte",
        VILTE: "vilte"
    };

    static readonly INCOMING_TYPE = {
        VOICE: "voice",
        VIDEO: "video"
    };

    static readonly WAY_TYPE = {
        BANNER: "banner",
        GLIDE: "glide",
        WINDOW: "window"
    };

    static readonly ANSWER_TYPE = {
        INVALID: -1,
        PAD: 0,
        FLOAT: 1,
        DRAG: 2
    };

    static readonly HANGUP_SCENE = {
        INVALID: -1,
        PAD: 0,
        FLOAT: 1,
        ALL: 10
    };

    static readonly HANGUP_WAY = {
        INVALID: -1,
        CLICK: 0,
        DRAG: 1,
        SMS_NORMAL: 2,
        SMS_CUSTOM: 3,
        CONF_SPETIAL: 4,
        PAGELINK: 5,
        VOICE: 6
    };

    static readonly PAGE_NAME = {
        INCOMING_PAGE: "incomingCallPage",
        TWOWAY_PAGE: "twoWayCallPage"
    };

    static readonly INTERFACE_TYPE = {
        BLUETOOTH_INTERFACE: "bluetooth",
        CARCHAT_INTERFACE: "carchat"
    };

    static readonly FROM_TYPE = {
        INCOMINT_WINDOW: "incomingWindow",
        TWOWAY_WINDOW: "twoWayCallPage"
    };

    static readonly SCENE_SIGNAL_NAME = {
        NOT_ANSWER_CALL: "NotAnswerCall",
        END_AFTER_ANSWER: "EndAfterAnswer",
        WAIT_BEFORE_ANSWERING: "WaitBeforeAnswering"
    };

    static getInstance() {
        if (!sInstance) {
            Log.v(TAG, "getInstance, create new TrackerUtil");
            sInstance = new TrackerUtil();
        }

        return sInstance;
    }

    constructor() {
        this.pageSet = new Set();
    }

    getDataAgent(pageName: string): DataAgent {
        if (typeof pageName !== "string" || pageName.length === 0) {
            pageName = MAIN_PAGE_TITLE;
        }

        let dataAgent = DataAgent.getInstance(pageName);
        dataAgent.appName = APP_NAME;
        dataAgent.configure(APP_KEY, { TrackVersion: TRACK_VERSION });
        return dataAgent;
    }

    commitEvent(event: TrackerEvent, jsonParam: object) {
        if (Utils.isSupportCta()) {
            return;
        }
        jsonParam = jsonParam || {};
        Log.d(TAG, "commitEvent, event = ", JSON.stringify(event),
            ", jsonParam = ", JSON.stringify(jsonParam));
        this.getDataAgent(event.page.title).sendEvent(event.name, jsonParam);
    }

    clickCtrl(ctrl: TrackerEvent | TrackerCtrl, jsonParam: object) {
        if (Utils.isSupportCta()) {
            return;
        }
        jsonParam = jsonParam || {};
        Log.v(TAG, "clickCtrl, ctrl = ", ctrl,
            ", jsonParam = ", JSON.stringify(jsonParam));
        this.getDataAgent(ctrl.page.title).clickButton(ctrl.name, jsonParam);
    }

    enterPage(trackerPage: TrackerPage, trackerFromPage: TrackerPage,
              jsonParam: {FromPage?: string}) {
        if (this.pageSet.has(trackerPage.toString())) {
            Log.v(TAG, trackerPage.title, "has entered!!");
            return;
        }
        this.pageSet.add(trackerPage.toString());
        jsonParam = jsonParam || {};
        jsonParam.FromPage = trackerFromPage ? trackerFromPage.title : "";
        Log.d(TAG, "enterPage, trackerPage = ", JSON.stringify(trackerPage),
            ", trackerFromPage = ", trackerFromPage,
            ", jsonParam = ", JSON.stringify(jsonParam));
        let ret = this.getDataAgent(trackerPage.title).enterPage(jsonParam);
        if (ret && ret.err) {
            Log.v(TAG, "enterPage Error occurs: " + ret.msg);
        }
    }

    leavePage(trackerPage: TrackerPage, jsonParam: object) {
        if (!this.pageSet.has(trackerPage.toString())) {
            Log.v(TAG, trackerPage.title, "has leaved!!");
            return;
        }
        jsonParam = jsonParam || {};
        Log.d(TAG, "leavePage, trackerPage = ", JSON.stringify(trackerPage),
            ", jsonParam = ", JSON.stringify(jsonParam));
        let ret = this.getDataAgent(trackerPage.title).leavePage(jsonParam);
        if (ret && ret.err) {
            Log.v(TAG, "leavePage Error occurs: " + ret.msg);
        }
        this.pageSet.delete(trackerPage.toString());
    }

    sendIncomingCallSignal(signalName: string, data: object) {
        data = data || {};
        let jsonParam = {"serviceName": APP_NAME, "signalName": signalName, "data": data};
        Log.d(TAG, "incoming call signal send data = ", JSON.stringify(data),
            ", jsonParam = ", JSON.stringify(jsonParam));
        // Util.sendSignal(jsonParam);
    }
}

export = TrackerUtil;
