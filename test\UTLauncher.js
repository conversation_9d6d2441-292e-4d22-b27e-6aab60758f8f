
// Run command: node --harmony /opt/app/calldisplayer.yunos.com/test/UTLauncher.js

"use strict";

// Start up UTLauncher at first.
let UBus = require("ubus");
let Page = require("yunos/page/Page");
let PageLink = require("yunos/page/PageLink");
let pageLink = new PageLink("page://calldisplayer.yunos.com/UT");

const SEND_LINK_INTERVAL = 20000;
const TAG = "CallDisplayer UTLauncher";

console.log(TAG, "start.");
let timerId = null;
let exitProcess = false;

let stopLauncher = function() {
    console.log(TAG, "stopLauncher");

    if (timerId !== null) {
        clearInterval(timerId);
    }
    process.exit();
};

function UTAdapter() {
    var busName = "com.yunos.calldisplayerutadapter";
    var busPath = "/com/yunos/calldisplayerutadapter";
    var busInterface = "com.yunos.calldisplayerutadapter.interface";
    var ubus = new UBus("dbus");
    this._service = ubus.createService(busName);
    this._iface = this._service.createServiceInterface(busPath, busInterface);
    this._iface.onBirth = function() {
        console.log(TAG, "UTAdapter onBirth");
    };
    this._iface.onDeath = function() {
        console.log(TAG, "UTAdapter onDeath");
        stopLauncher();
    };
}

UTAdapter.prototype.addMethods = function() {
    var self = this;

    this._iface.addMethod("sendUTResult",
            function(msg, sendResponse, reply) {
                if (msg.hasInvalidRead()) {
                    var msgE = self._iface.createErrorMessageWithType(msg, UBus.ErrorType.BUS_ERROR_INVALID_ARGS, "invalid arguments");
                    sendResponse(msgE);
                    return;
                }

                var utCompleted = msg.readBool();
                var result = msg.readString();
                console.log(TAG, "sendUTResult, utCompleted = " + utCompleted);
                if (utCompleted === true) {
                    let reports = result.split("\n");
                    for (let i = 0; i < reports.length; i++) {
                        console.log(TAG, reports[i] + "\n");
                    }
                }
                sendResponse(reply);
                exitProcess = true;
            }
    );
};

let utAdapter = new UTAdapter();
utAdapter.addMethods();

Page.getInstance().sendLink(pageLink, function(error) {
    if (error) {
        console.log(TAG, "start pagelink error: ", error);
    } else {
        timerId = setInterval(function() {
            if (exitProcess === true) {
                console.log(TAG, "end.");
                stopLauncher();
            } else {
                console.log(TAG, "heartbeat");
            }
        }, SEND_LINK_INTERVAL);
    }
});
