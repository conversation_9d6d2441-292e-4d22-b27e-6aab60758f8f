
<CompositeView id="root" layout="{layout.callLayout}" background="linear-gradient(to bottom, #1A2027, #262E37)"
        left="{config.fullview_margin_left}" top="{config.fullview_margin_top}"  width="{config.fullview_width}" height="{config.fullview_height}" >
    <include markup="CallInfo"/>
    <CompositeView id="dialPanel" layout="{layout.dialPanelLayout}" width="{sdp(872)}" height="{sdp(620)}" >
        <CompositeView id="mtGestureGuide" width="{dp(340)}" height="{dp(72)}" borderRadius="{dp(8)}"
                background="#1C242EFF" layout="{layout.mtGestureGuideLayout}" visibility="{enum.View.Visibility.None}">
            <ImageView id="twoFingered" src="{img(images/ic_gestures_hand1.png)}"/>
            <TextView id="guideText" fontSize="20sp" color="#FFFFFFFF" width="{dp(230)}" text="{string.TEXT_GUIDE_SMS_REJECT}"
                    multiLine="false" maxLineCount="1" opacity="0.6" elideMode= "{enum.TextView.ElideMode.ElideLeft}"/>
        </CompositeView>
        <CompositeView id="callHandler" height="{sdp(112)}" layout="{layout.callHandlerLayout}">
            <ImageView id="handsfree"  width="{sdp(112)}" height="{sdp(112)}" src="{img(images/qms_call_bt_speaker_car.png)}"
                multiState="{config.button_handsfreeMultiState}"/>
            <ImageView id="phoneSpeaker"  width="{sdp(112)}" height="{sdp(112)}" src="{img(images/qms_call_bt_speaker_phone_normal.png)}"
                multiState="{config.button_phoneSpeakerMultiState}" visibility="{enum.View.Visibility.None}"/>
            <ImageView id="keyboard"  width="{sdp(112)}" height="{sdp(112)}" src="{img(images/qms_call_btn_dialer_normal.png)}"
                multiState="{config.button_keyboardMultiState}"/>
            <ImageView id="mute"  width="{sdp(112)}" height="{sdp(112)}" src="{img(images/qms_call_bt_mute_normal.png)}"
                />
        </CompositeView>
        <CompositeView id="info" height="{sdp(28)}" layout="{layout.callHandlerLayout}">
                <TextView id="handsfreeText" propertySetName="extend/hdt/FontBody4"  align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(112)}" text="{string.TEXT_AUDIO_CHANGE}"/>
                <TextView id="phoneSpeakerText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(112)}" text="{string.TEXT_AUDIO_PHONE}" visibility="{enum.View.Visibility.None}"/>
                <TextView id="keyboardText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(112)}" text="{string.TEXT_DIALPAD}"/>
                <TextView id="muteText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(112)}" text="{string.BTN_MUTE}"/>
        </CompositeView>
    </CompositeView>
    <ImageView id="back" src="{img(images/qms_call_ic_down_normal.png)}"
        height="{sdp(56)}" width="{sdp(56)}"/>
    <CompositeView id="callButtonPanel" layout="{layout.callButtonPanelLayout}" width="{sdp(872)}" height="{sdp(150)}" >
        <CompositeView id="callButton" height="{sdp(80)}" width="{sdp(872)}" layout="{layout.callButtonLayout}">
            <ButtonBM id="answer" iconSrc="{img(images/qms_call_bt_answer_short.png)}"
                height="{sdp(80)}" width="{sdp(196)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}"  contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="holdAnswer" iconSrc="{img(images/qms_call_btn_answer_reservation_normal.png)}"
                height="{sdp(80)}" width="{sdp(196)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="merge" iconSrc="{img(images/qms_call_btn_merge_normal.png)}"
                height="{sdp(80)}" width="{sdp(196)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="switch" iconSrc="{img(images/qms_call_btn_switch_normal.png)}"
                height="{sdp(80)}" width="{sdp(196)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="hangUp" iconSrc="{img(images/qms_call_bt_hang_up_long.png)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                height="{sdp(80)}" width="{sdp(196)}"/>
        </CompositeView>
        <CompositeView id="callButtonInfo" height="{sdp(28)}" width="{sdp(872)}" layout="{layout.callButtonLayout}" visibility="{enum.View.Visibility.None}">
                <TextView id="answerText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(196)}" text="{string.TEXT_ANSWER}"/>
                <TextView id="holdText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(196)}" text="{string.TEXT_ANSWER_HOLD}"/>
                <TextView id="hangUpText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(196)}" text="{string.BTN_HANG_UP}"/>
        </CompositeView>
    </CompositeView>

</CompositeView>
