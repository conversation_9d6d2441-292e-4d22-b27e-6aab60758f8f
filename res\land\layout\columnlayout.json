{"moFloatLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(10)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"icon": {"margin": {"left": "{dp(24)}"}, "align": "{enum.RowLayout.Align.Middle}"}, "callState": {"margin": {"right": "{dp(24)}"}, "align": "{enum.RowLayout.Align.Middle}"}}}, "moFloatLayoutTwoInfo": {"type": "RelativeLayout", "params": {"callState": {"align": {"left": "parent", "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(0)}", "top": "{sdp(2)}"}}, "callname": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(0)}", "top": "{sdp(5)}"}}}}, "moFloatLayoutTwo": {"type": "RelativeLayout", "params": {"smallHangUp": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(25)}", "top": "{sdp(15)}"}}, "icon": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(6)}", "top": "{sdp(36)}"}}, "iconCar": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(6)}", "top": "{sdp(30)}"}}, "info": {"align": {"left": {"target": "smallHangUp", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(24)}", "top": "{sdp(15)}"}}}}, "floatCallInfoLayout": {"type": "ColumnLayout", "attrs": {"wrapContent": true}, "default": {"align": "{enum.ColumnLayout.Align.Left}", "margin": {"top": "{dp(16)}"}}}, "floatCallInfoLayoutTwoavatarInfo": {"type": "RelativeLayout", "params": {"avatar": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "avatarChart": {"align": {"right": "parent", "bottom": "parent"}, "margin": {"right": "{sdp(0)}"}}}}, "floatCallInfoLayoutTwo": {"type": "RelativeLayout", "params": {"avatarInfo": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "callname": {"align": {"left": {"target": "avatarInfo", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(24)}", "top": "{sdp(8)}"}}, "number": {"align": {"left": {"target": "avatarInfo", "side": "right"}, "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(24)}", "top": "{sdp(16)}"}}, "aiTransfer": {"align": {"left": {"target": "number", "side": "right"}, "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(16)}", "top": "{sdp(16)}"}}}}, "floatCallInfoLayoutTwoDingding": {"type": "RelativeLayout", "params": {"avatarIncoming": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "callnameIncoming": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(6)}"}}, "numberIncoming": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}, "top": {"target": "callnameIncoming", "side": "bottom"}}, "margin": {"left": "{sdp(16)}", "top": "{sdp(4)}"}}, "individer": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(192)}"}}, "avatarTwo": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}}, "margin": {"left": "{sdp(253)}"}}, "callnameTwo": {"align": {"left": {"target": "avatarT<PERSON>", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(6)}"}}, "numberTwo": {"align": {"left": {"target": "avatarT<PERSON>", "side": "right"}, "top": {"target": "callnameTwo", "side": "bottom"}}, "margin": {"left": "{sdp(16)}", "top": "{sdp(4)}"}}}}, "mtGuestureFloatLayout": {"type": "RelativeLayout", "params": {"mtFloat": {"align": {"left": "parent", "right": "parent", "top": "parent"}}, "mtGestureGuide": {"align": {"left": "parent", "right": "parent", "top": {"target": "mtFloat", "side": "bottom"}}}}}, "mtFloatLayout": {"type": "RelativeLayout", "params": {"avatar": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(68)}"}}, "userInfo": {"align": {"left": {"target": "avatar", "side": "right"}, "right": {"target": "answer", "side": "left"}, "middle": "parent"}, "margin": {"left": "{sdp(36)}", "top": "{sdp(59)}"}}, "answer": {"align": {"right": {"target": "hangUp", "side": "left"}, "middle": "parent"}, "margin": {"right": "{sdp(28)}"}}, "hangUp": {"align": {"right": "parent", "middle": "parent"}, "margin": {"right": "{sdp(60)}"}}}}, "mtFloatLayoutTwo": {"type": "RelativeLayout", "params": {"mtFloatOneDingding": {"align": {"top": "parent", "center": "parent"}}, "mtFloatTwoDingding": {"align": {"top": "parent", "center": "parent"}}}}, "mtFloatLayoutOneDingding": {"type": "RelativeLayout", "params": {"userInfo": {"align": {"middle": "parent", "left": "parent"}, "margin": {"left": "{sdp(47)}"}}, "anwerhanup": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(719)}"}}, "minimize": {"align": {"right": "parent", "top": "parent"}, "margin": {"right": "{sdp(16)}", "top": "{sdp(16)}"}}}}, "anwerhanupLayout": {"type": "RelativeLayout", "params": {"answer": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "hangUp": {"align": {"right": "parent"}, "margin": {"right": "{sdp(0)}"}}}}, "mtFloatLayoutTwoDingding": {"type": "RelativeLayout", "params": {"userInfoTwo": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(58)}"}}, "anwerhanupTwoPanel": {"align": {"right": "parent", "top": "parent"}, "margin": {"right": "{sdp(104)}", "top": "{sdp(36)}"}}, "minimizeTwo": {"align": {"right": "parent", "top": "parent"}, "margin": {"right": "{sdp(17)}", "top": "{sdp(16)}"}}}}, "anwerhanupLayoutTwo": {"type": "RowLayout", "attrs": {"spacing": "{sdp(40)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "anwerhanupTwoInfo": {"type": "RowLayout", "attrs": {"spacing": "{sdp(50)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "anwerhanupLayoutTwoPanel": {"type": "RelativeLayout", "params": {"anwerhanupTwo": {"align": {"right": "parent", "top": "parent"}}, "anwerhanupTwoInfo": {"align": {"right": "parent", "top": {"target": "anwerhanupTwo", "side": "bottom"}}, "margin": {"right": "{sdp(9)}", "top": "{sdp(8)}"}}}}, "imcommingBubbleLayoutInfo": {"type": "RelativeLayout", "params": {"imcommingText": {"align": {"left": "parent", "top": {"target": "imcommingName", "side": "bottom"}}, "margin": {"top": "{sdp(2)}", "left": "{sdp(0)}"}}, "imcommingName": {"align": {"left": "parent", "top": "parent"}, "margin": {"top": "{sdp(5)}", "left": "{sdp(0)}"}}}}, "imcommingBubbleLayout": {"type": "RelativeLayout", "params": {"smallAnswer": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(25)}", "top": "{sdp(15)}"}}, "smallHangUp": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(18)}"}}, "info": {"align": {"left": {"target": "smallAnswer", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(15)}"}}}}, "mtGuestureFloatLayoutTwo": {"type": "RelativeLayout", "params": {"mtFloat": {"align": {"top": "parent"}, "margin": {"top": "{sdp(456)}"}}, "mtGestureGuide": {"align": {"top": {"target": "mtFloat", "side": "bottom"}}}}}, "mtGestureGuideFloatLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(8)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"twoFingered": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"left": "{dp(449)}"}}, "guideText": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"right": "{dp(22)}"}}}}, "scrollLayout": {"type": "RelativeLayout", "params": {"bigNumber": {"align": {"middle": "parent", "left": "parent"}}}}, "keyboardPanelLayout": {"type": "RelativeLayout", "params": {"hide": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(24)}", "left": "{sdp(56)}"}}, "scroll": {"align": {"top": "parent", "left": "parent", "right": "parent"}, "margin": {"top": "{sdp(40)}", "left": "{sdp(232)}", "right": "{sdp(229)}"}}, "keyboardBackground": {"align": {"top": "parent", "center": "parent"}, "margin": {"top": "{sdp(108)}"}}, "keyboardLayout": {"align": {"top": {"target": "keyboardBackground", "side": "bottom"}, "left": "parent", "right": "parent"}, "margin": {"top": "{sdp(8)}", "left": "{sdp(40)}", "right": "{sdp(10)}"}}, "keyboardHangUp": {"align": {"bottom": "parent", "center": "parent"}, "margin": {"bottom": "{sdp(36)}"}}}}, "keyboardGridLayout": {"type": "GridLayout", "default": {}, "attrs": {"rows": 4, "columns": 3, "rowSpacing": "{sdp(10)}", "columnSpacing": "{sdp(177)}"}}, "keyLayout": {"type": "RelativeLayout", "default": {"align": {"center": "parent", "middle": "parent"}}}, "callInfoOneItemLayout": {"type": "RelativeLayout", "params": {"avatar": {"align": {"middle": "parent", "left": "parent"}, "margin": {"left": "{sdp(167)}"}}, "avatarDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(267)}", "top": "{sdp(338)}"}}, "callname": {"align": {"left": {"target": "avatar", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(32)}", "top": "{sdp(270)}"}}, "callState": {"align": {"left": {"target": "avatar", "side": "right"}, "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(32)}", "top": "{sdp(16)}"}}}}, "callInfoTwoItemLayout": {"type": "RelativeLayout", "params": {"avatarOne": {"align": {"top": "parent", "left": "parent"}, "margin": {"left": "{sdp(201)}", "top": "{sdp(190)}"}}, "avatarOneDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(300)}", "top": "{sdp(290)}"}}, "callnameOne": {"align": {"left": "parent", "top": {"target": "avatarOne", "side": "bottom"}}, "margin": {"left": "{sdp(161)}", "top": "{sdp(24)}"}}, "callStateOne": {"align": {"left": "parent", "top": {"target": "callnameOne", "side": "bottom"}}, "margin": {"left": "{sdp(145)}", "top": "{sdp(8)}"}}, "avatarTwo": {"align": {"top": "parent", "left": "parent"}, "margin": {"left": "{sdp(529)}", "top": "{sdp(190)}"}}, "avatarTwoDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(628)}", "top": "{sdp(290)}"}}, "callnameTwo": {"align": {"left": "parent", "top": {"target": "avatarT<PERSON>", "side": "bottom"}}, "margin": {"left": "{sdp(489)}", "top": "{sdp(24)}"}}, "callStateTwo": {"align": {"left": "parent", "top": {"target": "callnameTwo", "side": "bottom"}}, "margin": {"left": "{sdp(473)}", "top": "{sdp(8)}"}}}}, "callInfoLayout": {"type": "RowLayout", "default": {"align": "{enum.RowLayout.Align.Top}"}}, "callInfoItemLayout": {"type": "ColumnLayout", "attrs": {"wrapContent": true}, "default": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(0)}"}}, "params": {"_avatar": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(0)}"}}, "_callname": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(24)}"}}, "_callState": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(6)}"}}}}, "mtGestureGuideLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(8)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"twoFingered": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"left": "{dp(32)}"}}, "guideText": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"right": "{dp(22)}"}}}}, "callHandlerLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{sdp(160)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "callButtonLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{sdp(16)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "dialPanelLayout": {"type": "RelativeLayout", "params": {"callHandler": {"align": {"center": "parent", "top": "parent"}, "margin": {"top": "{sdp(0)}"}}, "info": {"align": {"top": {"target": "call<PERSON><PERSON><PERSON>", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(16)}"}}}}, "callButtonPanelLayout": {"type": "RelativeLayout", "params": {"callButton": {"align": {"top": "parent", "center": "parent"}, "margin": {"top": "{sdp(0)}"}}, "callButtonInfo": {"align": {"top": {"target": "callButton", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(16)}"}}}}, "callLayout": {"type": "RelativeLayout", "params": {"back": {"align": {"top": "parent", "left": "parent"}, "margin": {"left": "{sdp(32)}", "top": "{sdp(20)}"}}, "dialPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(152)}", "left": "{sdp(0)}"}}, "keyboardPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(0)}", "left": "{sdp(120)}"}}, "callButtonPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(382)}", "left": "{sdp(0)}"}}, "callInfoRoot": {"align": {"left": "parent"}, "margin": {"left": "{sdp(876)}"}}}}}