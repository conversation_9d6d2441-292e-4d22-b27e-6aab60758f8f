/* 来电悬浮窗 */
"use strict";

process.env.CAFUI2 = "true";

import Window = require("yunos/ui/view/Window");
import KeyframeAnimation = require("yunos/ui/animation/KeyframeAnimation");
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
import View = require("yunos/ui/view/View");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import TouchEvent = require("yunos/ui/event/TouchEvent");

import Log = require("../utils/Logs");
import BaseLayout = require("./BaseLayout");
import TrackerUtil = require("../utils/TrackerUtil");
import Utils = require("../utils/Utils");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import DialsConstants = require("../utils/DialsConstants");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer");
import RowLayout = require("yunos/ui/layout/RowLayout");
import MotionAgent = require("extend/hdt/platform/MotionAgent");
import {MotionEvent, MotionReferrer, MotionEventType, TipDuration, ArrowPosition} from "extend/hdt/control/Types";
import MotionFeedbackBM = require("extend/hdt/control/MotionFeedbackBM");
import MotionGestureEvent = require("yunos/ui/event/MotionGestureEvent");

const TAG = "IncomingCallFloatWindowTwo";
const CALL_SCENEID = "35d088c37a10c9ff903cb4a691893aa1";
interface IWindow {
    specifiedMode?: number;
    getSystemOrientation?: () => number;
}

interface IWindowOptions {
    top?: number;
    left?: number;
    width?: number;
    height?: number;
    type?: number;
    layoutFlags?: number;
    pageToken?: string;
    orientation?: number;
}

interface IView {
    enableBehindWindowsBlur: boolean;
}

interface IImageView {
    voiceEnabled: boolean;
}

interface IVoiceCommand {
    activeMode: number;
    sceneId: string;
    keepFullTimeActive: boolean;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
    isLocalContact?: boolean;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class IncomingCallFloatWindowTwo extends BaseLayout {
    private standWindowHeight: number;
    private window: Window;
    private orientation: number;
    public visible: boolean;
    private root: CompositeView;
    private mtFloat: CompositeView;
    private avatar: ImageView;
    private userInfo: CompositeView;
    private callname: TextView;
    private number: TextView;
    private hangup: ImageView;
    private answer:ImageView;
    private silenceRinger: ImageView;
    private mtGestureGuide: CompositeView;
    private twoFingered: ImageView;
    private guideText: TextView;
    private shockAnim: KeyframeAnimation;
    private dropDownAnim: PropertyAnimation;
    private moveUpAnim: PropertyAnimation;
    private Dragging: boolean;
    private startX: number;
    private startY: number;;
    private diffX: number;
    private diffY: number;
    private _callnameText: string;
    private _nameUpdatedNumber: string;
    private screenHeight: number;
    private minimize: ImageView;
    private mtFloatOneDingding: CompositeView;
    private mtFloatTwoDingding: CompositeView;
    private callnameIncoming: TextView;
    private callnameTwo: TextView;
    private numberTwo: TextView;
    private anwerhanupTwo: CompositeView;
    private hangUpTwo: ImageView;
    private answerHangup:ImageView;
    private answerHold:ImageView;
    private minimizeTwo: ImageView;
    private _callnameTwoText: string;
    private avatarInfo: CompositeView;
    private avatarChart: ImageView;
    private anwerhanupTwoInfo: CompositeView;
    private answerHoldInfo: TextView;
    private aiTransfer: TextView;
    private isAiTransfer: boolean;
    private callType: string;
    private isAiHold: boolean;
    private motionAgent: MotionAgent;
    private isMultiLine: boolean;
    private isMinimize: boolean;
    private motionFeedbackBM: MotionFeedbackBM;
    private _quitTimeout: NodeJS.Timer;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");

        super(controller, callService);
        let screenSize = this.getScreenSize();
        this.screenHeight = screenSize.height;
        let win: Window = <Window> Window.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.getStatusBarHeight(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeightWithMenu(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: Window.Orientation.FollowUnderWindow
            // autoOrientation: true
        });

        win.title = "IncomingCallFloatWindow";
        win.showWhenLocked = true; // show beyond keyguard
        (<IWindow><object>win).specifiedMode = 1; // exclusive Mode for voice and motion event
        win.background = "transparent";
        this.window = win;

        this.controller = controller;
        this.callService = callService;

        this._view = <CompositeView>LayoutManager.loadSync("MTFloatLayouttwo.xml");
        if (DialsConstants.Customization.ENABLE_BLUR) {
            (<IView><object> this._view).enableBehindWindowsBlur = true;
            this._view.background = <string>Res.getConfig("color_blur_cover");
        }
        this.visible = false;

        this.root = <CompositeView> this._view.findViewById("root");
        this.mtFloat = <CompositeView> this._view.findViewById("mtFloat");
        this.avatar = <ImageView> this._view.findViewById("avatar");
        this.userInfo = <CompositeView> this._view.findViewById("userInfo");
        this.callname = <TextView> this._view.findViewById("callname");
        this.number = <TextView> this._view.findViewById("number");
        this.hangup = <ImageView> this._view.findViewById("hangUp");
        this.answer = <ImageView> this._view.findViewById("answer");
        this.mtGestureGuide = <CompositeView> this._view.findViewById("mtGestureGuide");
        this.twoFingered = <ImageView> this._view.findViewById("twoFingered");
        this.guideText = <TextView> this._view.findViewById("guideText");
        this.minimize = <ImageView> this._view.findViewById("minimize");
        this.mtFloatOneDingding =  <CompositeView> this._view.findViewById("mtFloatOneDingding");
        this.mtFloatTwoDingding =  <CompositeView> this._view.findViewById("mtFloatTwoDingding");
        this.callnameIncoming = <TextView> this._view.findViewById("callnameIncoming");
        this.callnameTwo = <TextView> this._view.findViewById("callnameTwo");
        this.numberTwo = <TextView> this._view.findViewById("numberTwo");
        this.anwerhanupTwo = <CompositeView> this._view.findViewById("anwerhanupTwo");
        this.hangUpTwo = <ImageView> this._view.findViewById("hangUpTwo");
        this.answerHangup = <ImageView> this._view.findViewById("answerHangup");
        this.answerHold = <ImageView> this._view.findViewById("answerHold");
        this.minimizeTwo = <ImageView> this._view.findViewById("minimizeTwo");
        this.avatarChart = <ImageView> this._view.findViewById("avatarChart");
        this.anwerhanupTwoInfo = <CompositeView> this._view.findViewById("anwerhanupTwoInfo");
        this.answerHoldInfo = <TextView> this._view.findViewById("answerHoldInfo");
        this.aiTransfer = <TextView> this._view.findViewById("aiTransfer");

        this.setViewTop();
        this.window.addChild(this._view);

        this.initVoiceControl();
        this.addListener();
    }

    setViewTop() {
        let homeUri = Utils.getCurrentHomeshell();
        if (this._view && this._view.layout) {
            if (homeUri && -1 !== homeUri.indexOf("map")) {  //A homeshell
                this._view.layout.setLayoutParam(0, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("mt_float_view_top"))});
            } else {
                this._view.layout.setLayoutParam(0, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("mt_float_view_top_B"))});
            }
        }
    }

    initVoiceControl() {
        (<IImageView><object> this.hangup).voiceEnabled = true;
        this.hangup.voiceSelectMode = View.VoiceSelectMode.Custom;
        let hangupCommand = new VoiceCommand();
        hangupCommand.customCommands = <Array<string>> Res.getConfig("hangup_commands");
        hangupCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>hangupCommand).sceneId = CALL_SCENEID;
        (<IVoiceCommand><object>hangupCommand).activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        (<IVoiceCommand><object>hangupCommand).keepFullTimeActive = true;
        this.hangup.addVoiceCommand(hangupCommand);
        this.hangup.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice hangup");
            var hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.PAD,
                hupWay: TrackerUtil.HANGUP_WAY.VOICE};
            this.callService.hangup(hangupType, "");
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ComIncomingResult, {type: "hangUp"});
            } else {
                let isMultiLines = this.callService.getCallLines() >= 2;
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "voice", pageType: isMultiLines ? "twoWayCallPage" : "incomingWindow", result: isMultiLines ? "reject&pickup" :"hangUp"
                });
            }
            //Log.d(TAG,"TrackerUtil.TrackerEvents.CardShow on screen off");
        });

        (<IImageView><object> this.answer).voiceEnabled = true;
        this.answer.voiceSelectMode = View.VoiceSelectMode.Custom;
        let answerCommand = new VoiceCommand();
        answerCommand.customCommands = <Array<string>> Res.getConfig("answer_commands");
        answerCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>answerCommand).sceneId = CALL_SCENEID;
        (<IVoiceCommand><object>answerCommand).activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        (<IVoiceCommand><object>answerCommand).keepFullTimeActive = true;
        this.answer.addVoiceCommand(answerCommand);
        this.answer.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice answer");
            this.callService.answer(0, null, 0); // no need to set answer type as it not used in tracker
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ComIncomingResult, {type: "pickup"});
            } else {
                let isMultiLines = this.callService.getCallLines() >= 2;
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "voice", pageType: isMultiLines ? "twoWayCallPage" : "incomingWindow", result: isMultiLines ? "hold&pickup" : "pickup"
                });
            }
        });

        (<IImageView><object> this.minimize).voiceEnabled = true;
        this.minimize.voiceSelectMode = View.VoiceSelectMode.Custom;
        let minimizeCommand = new VoiceCommand();
        minimizeCommand.customCommands = <Array<string>> Res.getConfig("mute_commands");
        minimizeCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>minimizeCommand).sceneId = CALL_SCENEID;
        (<IVoiceCommand><object>minimizeCommand).activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        (<IVoiceCommand><object>minimizeCommand).keepFullTimeActive = true;
        minimizeCommand.interactorState = VoiceCommand.InteractorState.Idle;
        this.minimize.addVoiceCommand(minimizeCommand);
        this.minimize.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice silenceRinger");
            this.onMinimize();
        });

    }

    getScreenSize() {
        return {
            width: Utils.getTotalScreenWidth(), // 获取全屏幕的宽度，横屏时也包含状态栏的宽度
            height: Utils.getScreenHeightWithMenu()
        };
    }

    onHangup() {
        Log.d(TAG, "onHangup called");
        this.controller.closeNotificationCenter();
        var hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
            hupWay: TrackerUtil.HANGUP_WAY.CLICK};
        this.callService.hangup(hangupType, "");
        if (this.isAiTransfer) {
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ComIncomingResult, {type: "hangUp"});
        } else {
            let isMultiLines = this.callService.getCallLines() >= 2;
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
              type: "byhand", pageType: isMultiLines ? "twoWayCallPage" : "incomingWindow", result: "hangUp"
            });
        }
    }

    addListener() {
        this.hangup.addGestureRecognizer(new TapRecognizer());
        this.answer.addGestureRecognizer(new TapRecognizer());
        this.hangUpTwo.addGestureRecognizer(new TapRecognizer());
        this.answerHangup.addGestureRecognizer(new TapRecognizer());
        this.answerHold.addGestureRecognizer(new TapRecognizer());
        this.minimize.addGestureRecognizer(new TapRecognizer());
        this.minimizeTwo.addGestureRecognizer(new TapRecognizer());
        //this.silenceRinger.addGestureRecognizer(new TapRecognizer());
        this.hangup.on("tap",() => {
            this.onHangup();
        });
        this.answer.on("tap",() => {
            Log.d(TAG, "onAnswer called");
            this.controller.closeNotificationCenter();
            this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ComIncomingResult, {type: "pickup"});
            } else {
                let isMultiLines = this.callService.getCallLines() >= 2;
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "byhand", pageType: isMultiLines ? "twoWayCallPage" : "incomingWindow", result: isMultiLines ? "hold&pickup" : "pickup"
                });
            }

            this.hide();
        });
        this.hangUpTwo.on("tap",() => {
            this.onHangup();
        });
        this.answerHangup.on("tap",() => {
            Log.d(TAG, "onanswerHangup called");
            this.controller.closeNotificationCenter();
            let callList = this.callService.getExistCallList();
            if (callList.length > 1) {
                let hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
                    hupWay: TrackerUtil.HANGUP_WAY.CLICK};
                this.callService.hangupSpecial(callList[1],hangupType);
            }
            this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);

            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
              type: "byhand", pageType: "twoWayCallPage", result: "reject&pickup"
            });
        });
        this.answerHold.on("tap",() => {
            Log.d(TAG, "answerHold called");
            this.controller.closeNotificationCenter();
            this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                type: "byhand", pageType: "twoWayCallPage", result: "hold&pickup"
            });
        });
        this.minimize.on("tap", () => {
            this.onMinimize();
        });
        this.minimizeTwo.on("tap", () => {
            this.onMinimize();
        });
    }

    update(param: Array<IMyCallCell>) {
        if (param) {
            let incomingCell = param[0];
            let incomingCall: IMyCallCell = incomingCell.callinfo || incomingCell;
            this.isAiTransfer = incomingCall.aiTransfer;
            this.isMultiLine = param.length > 1;
            if (param.length === 1) {
                this.mtFloatOneDingding.visibility = View.Visibility.Visible;
                this.mtFloatTwoDingding.visibility = View.Visibility.None;
                if (!incomingCell.callerInfo) {
                    this.callname.text = incomingCall.lineid;
                } else {
                    this.callname.text = this._callnameText || incomingCell.callerInfo.name;
                }
                if (incomingCall.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
                    this.mtFloatOneDingding.background = Res.getImageSrc("images/qms_call_bg_dingding_right.png");
                    this.avatarChart.visibility = View.Visibility.Visible;
                } else {
                    this.mtFloatOneDingding.background = Res.getImageSrc("images/qms_call_bg_incomingcall_popup.png");
                    this.avatarChart.visibility = View.Visibility.None;
                }
                this.isAiHold = incomingCall.aiHold;
                this.callType = incomingCall.callType;
                if (incomingCall.aiHold) {  //显示AI助理接听中
                    this.aiTransfer.visibility = View.Visibility.Visible;
                    this.number.text = this.controller.callStatusToString(incomingCell, this.callService.timecount);
                } else {
                    this.aiTransfer.visibility = View.Visibility.None;
                    this.number.text = Res.getString("TEXT_INCOMING_CALL");
                }
            } else {
                this.destoryMotionAgent();
                this.mtFloatOneDingding.visibility = View.Visibility.None;
                this.mtFloatTwoDingding.visibility = View.Visibility.Visible;
                if (!incomingCell.callerInfo) {
                    this.callnameIncoming.text = incomingCall.lineid;
                } else {
                    this.callnameIncoming.text = this._callnameText || incomingCell.callerInfo.name;
                }

                let activeCell = param[1];
                let activeCall: IMyCallCell = activeCell.callinfo || activeCell;
                if (!activeCell.callerInfo) {
                    this.callnameTwo.text = activeCall.lineid;
                } else {
                    this.callnameTwo.text = this._callnameTwoText || activeCell.callerInfo.name;
                }
                let twoButtom = false;
                if (activeCall.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
                    this.mtFloatTwoDingding.background = Res.getImageSrc("images/qms_call_bg_dingding_right.png");
                    twoButtom = true;
                } else if (incomingCall.callType === DialsConstants.Customization.CALL_TYPE_CHART){
                    this.mtFloatTwoDingding.background = Res.getImageSrc("images/qms_call_bg_dingding_left.png");
                    twoButtom = true;
                } else {
                    this.mtFloatTwoDingding.background = Res.getImageSrc("images/qms_call_bg_incomingcall_popup.png");
                }
                if (twoButtom) {
                    this.answerHold.visibility = View.Visibility.None;
                    this.anwerhanupTwo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton_width_2"));
                    this.answerHangup.width = this.hangUpTwo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_oneCallbutton_width_2"));
                    this.answerHangup.height = this.hangUpTwo.height = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_oneCallbutton_height_2"));
                    (<RowLayout>this.anwerhanupTwo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton2_spacing"));
                    if (this.hangUpTwo.width === this.hangUpTwo.height) {
                        this.answerHoldInfo.visibility = View.Visibility.None;
                        this.anwerhanupTwoInfo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton_width_2"));
                        (<RowLayout>this.anwerhanupTwoInfo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbuttoninfo2_spacing"));
                    }
                } else {
                    this.answerHold.visibility = View.Visibility.Visible;
                    this.anwerhanupTwo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton_width_3"));
                    this.answerHangup.width = this.hangUpTwo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_oneCallbutton_width_3"));
                    this.answerHangup.height = this.hangUpTwo.height = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_oneCallbutton_height_3"));
                    (<RowLayout>this.anwerhanupTwo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton3_spacing"));
                    if (this.hangUpTwo.width === this.hangUpTwo.height) {
                        this.answerHoldInfo.visibility = View.Visibility.Visible;
                        this.anwerhanupTwoInfo.width = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbutton_width_3"));
                        (<RowLayout>this.anwerhanupTwoInfo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("MtView_callbuttoninfo3_spacing"));
                    }
                }
                this.controller.fetchCallName(activeCall.lineid, null, (err, name, photo, userData) => {
                    if (!err && name) {
                        this._callnameTwoText = name;
                        if (this.callnameTwo) {
                            this.callnameTwo.text = name;
                        }
                    }
                });
                this.numberTwo.text = this.controller.callStatusToString(activeCell, this.callService.timecount);
            }
            this.controller.fetchCallName(incomingCall.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    if (this.mtFloatOneDingding.visibility === View.Visibility.Visible && this.callname) {
                        this.callname.text = this._callnameText;
                    } else {
                        if (this.callnameIncoming) {
                            this.callnameIncoming.text = this._callnameText;
                        }
                    }
                }
            });
        } else {
            Log.w(TAG, "param is null");
            return;
        }

        if (!this.visible) {
            Log.d(TAG, "show");
            this.resetPosition();
            this.window.show();
            this.visible = true;
            // if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
            //     this.mtGestureGuide.visibility = View.Visibility.Visible;
            // }
            if (Utils.supportGesture() && param.length === 1) {
                this.initGesutreGuide();
            }
            if (this.isAiTransfer) {
                this.aiTransfer && this.aiTransfer.visibility === View.Visibility.Visible ?
                TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CommunicationsAssistantCallback, null, {}) :
                TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CommunicationsAssistantPage, null, {}) ;
            } else {
                TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.IncomingLeavePage, null, {});
            }
        } else {
            Log.d(TAG, "has shown already");
        }
    }

    updateCallState(callList: Array<IMyCallCell>, curtime: number) {
        if (this.isAiHold) {
            this.number.text = this.controller.callStatusToString(callList[0], curtime);
        } else {
            if (callList && callList.length > 1) {
                this.numberTwo.text = this.controller.callStatusToString(callList[1], curtime);
            }
        }
    }

    hide() {
        Log.d(TAG, "hide called, this.visible=", this.visible);
        if (this.visible) {
            this.window.hide();
            this.visible = false;
            if (!this.isMinimize) {
                this.destoryMotionAgent();
            } else {
                this.isMinimize = false;
            }
            if (this._view) {
                // this._view.top = -this.standWindowHeight;
            }
            if (this.isAiTransfer) {
                this.aiTransfer && this.aiTransfer.visibility === View.Visibility.Visible ?
                TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CommunicationsAssistantCallback, {}) :
                TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CommunicationsAssistantPage, {});
            } else {
                TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.IncomingLeavePage,
                    {type: this.callType === "bt" ? TrackerUtil.INTERFACE_TYPE.BLUETOOTH_INTERFACE : TrackerUtil.INTERFACE_TYPE.CARCHAT_INTERFACE,
                    from: this.mtFloatTwoDingding.visibility === View.Visibility.Visible ? TrackerUtil.FROM_TYPE.TWOWAY_WINDOW : TrackerUtil.FROM_TYPE.INCOMINT_WINDOW});
            }
        }
        this.mtFloatOneDingding.visibility = View.Visibility.None;
        this.mtFloatTwoDingding.visibility = View.Visibility.None;
    }

    initGesutreGuide() {
        if (!this.motionAgent) {
            let nodStr = Res.getString("gesture_nod");
            let shakeStr = Res.getString("gesture_shake");
            this.motionAgent = new MotionAgent({
                anchorView: this.mtFloat,
                motionList: [
                    {
                        type:MotionEvent.Nod, // Nod,Shake,HoldLeft,HoldRight
                        text:nodStr
                    },
                    {
                        type:MotionEvent.Shake,
                        text:shakeStr
                    }],
                referrer: MotionReferrer.CallNotification // Notification,CallNotification,AiPush,AirCondition
            });
            this.motionAgent.tipDuration = TipDuration.Temporary; //TipDuration,Temporary
            this.motionAgent.arrowPosition = ArrowPosition.Bottom; // Top,Right,Bottom,Left
            this.motionAgent.tipOffsetY = <number> Res.getConfig("motionagent_tipoffset_Y");
            this.motionAgent.tipOffsetX = <number> Res.getConfig("motionagent_tipoffset_X");
            this.motionAgent.excute();
            this.motionAgent.on(MotionEventType.Nod,(ev:MotionGestureEvent) => {
                if (this.isMultiLine) {
                    this.startGestureEffect(true);
                }
            });
            this.motionAgent.on(MotionEventType.Shake,(ev:MotionGestureEvent) => {
                if (this.isMultiLine) {
                    this.startGestureEffect(false);
                }
            });
        }
    }

    destoryMotionAgent() {
        if (this.motionAgent) {
            this.motionAgent.destroy();
            this.motionAgent = null;
        }
    }

    destoryMotionFeedbackBM() {
        if (this.motionFeedbackBM) {
            this.motionFeedbackBM.destroy();
            this.motionFeedbackBM = null;
        }
    }

    startGestureEffect(isNod: boolean) {
        this.destoryMotionFeedbackBM();
        this.hangup.enabled = false;
        this.answer.enabled = false;
        let focusView = null;
        let targetView = this.hangup;
        let type = MotionEvent.Shake;
        if (isNod) {
            type  = MotionEvent.Nod;
            targetView = this.answer;
        }
        this.motionFeedbackBM = new MotionFeedbackBM(type);
        this.motionFeedbackBM.attachTo(targetView);
        this._quitTimeout = setTimeout(() => {
            clearTimeout(this._quitTimeout);
            this._quitTimeout = null;
            if (isNod) {
                Log.d(TAG, "onAnswer called");
                this.controller.closeNotificationCenter();
                this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0)
                this.hide();
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "byhand", pageType: "incomingWindow", result: "pickup"
                });
            } else {
                this.onHangup();
            }
            this.hangup.enabled = true;
            this.answer.enabled = true;
            this.destoryMotionFeedbackBM();
            this.destoryMotionAgent();
        }, 1000);
    }

    onMinimize() {
        this.callService.silenceRinger();
        if (this.isAiTransfer) {
            TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickComShrink, null);
        } else {
            if (this.callService.status !== CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE) {
                TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickIcomingShrik, null);
            } else {
                TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickShrink, null);
            }
        }
        this.isMinimize = true;
        if (this.isAiHold) {
            this.controller.toggleIncommingFullWindow(2);
        } else {
            this.controller.toggleIncommingFullWindow(1);
        }
    }

    onAnswer(e: TouchEvent) {
        Log.d(TAG, "onAnswer called");
        this.controller.closeNotificationCenter();
        this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
        e.stopPropagation();
        this.hide();
    }

    onMotionDialogVisible(visible: boolean) {
        Log.d(TAG, "onMotionDialogVisible, visible = " + visible);
        this.mtGestureGuide.visibility = visible ? View.Visibility.None : View.Visibility.Visible;
    }

    resetUIContent() {
        if (this.avatar) {
            this.avatar.src = Res.getImageSrc("images/qms_call_ic_portrait_medium.png");
        }
        if (this.callname) {
            this.callname.text = "";
        }

        this._callnameText = null;
    }

    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        this.setViewTop();
    }
}

export = IncomingCallFloatWindowTwo;
