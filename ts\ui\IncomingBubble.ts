/* 来电悬浮窗 */
"use strict";

process.env.CAFUI2 = "true";

import Window = require("yunos/ui/view/Window");
import KeyframeAnimation = require("yunos/ui/animation/KeyframeAnimation");
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
import View = require("yunos/ui/view/View");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import TouchEvent = require("yunos/ui/event/TouchEvent");

import Log = require("../utils/Logs");
import BaseLayout = require("./BaseLayout");
import TrackerUtil = require("../utils/TrackerUtil");
import Utils = require("../utils/Utils");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import DialsConstants = require("../utils/DialsConstants");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer");

const TAG = "IncomingBubble";
const TEXT_MIN_LENTH = 108;
const TEXT_MAX_LENTH = 180;

interface IWindow {
    specifiedMode?: number;
    getSystemOrientation?: () => number;
}

interface IWindowOptions {
    top?: number;
    left?: number;
    width?: number;
    height?: number;
    type?: number;
    layoutFlags?: number;
    pageToken?: string;
    orientation?: number;
}

interface IView {
    enableBehindWindowsBlur: boolean;
}

interface IImageView {
    _voiceControlEnabled: boolean;
}

interface IVoiceCommand {
    activeMode: number;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
    isLocalContact?: boolean;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class IncomingBubble extends BaseLayout {
    private standWindowHeight: number;
    private window: Window;
    private orientation: number;
    public visible: boolean;
    private root: CompositeView;
    private mtFloat: CompositeView;
    private avatar: ImageView;
    private userInfo: CompositeView;
    private callname: TextView;
    private number: TextView;
    private hangup: ImageView;
    private answer:ImageView;
    private mtGestureGuide: CompositeView;
    private twoFingered: ImageView;
    private guideText: TextView;
    private shockAnim: KeyframeAnimation;
    private dropDownAnim: PropertyAnimation;
    private moveUpAnim: PropertyAnimation;
    private Dragging: boolean;
    private startX: number;
    private startY: number;
    private diffX: number;
    private diffY: number;
    private _callnameText: string;
    private _nameUpdatedNumber: string;
    private screenHeight: number;
    private minimize: ImageView;
    private bubbleText: TextView;
    private bubbleHangup: ImageView;
    private bubbleAnswer:ImageView;
    private bubble:CompositeView;
    private imcommingBubbleRoot:CompositeView;
    private originX: number;
    private originY: number;
    private info:CompositeView;
    private bubbleName: TextView;
    private isAiTransfer: boolean;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");

        super(controller, callService);
        this.standWindowHeight = <number>Res.getConfig("mt_float_window_height") +
            (DialsConstants.Customization.ENABLE_HAND_MOTION ? <number>Res.getConfig("mt_float_guide_height") : 0);
        this.standWindowHeight = screenInstance.getPixelBySDp(this.standWindowHeight);
        let win: Window = <Window> Window.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ?  Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: Window.Orientation.FollowUnderWindow
            // autoOrientation: true
        });

        this.window = win;
        win.title = "IncomingCallFloatWindow";
        win.showWhenLocked = true; // show beyond keyguard
        (<IWindow><object>win).specifiedMode = 1; // exclusive Mode for voice and motion event
        win.background = "transparent";
        this.controller = controller;
        this.callService = callService;

        this.bubble = <CompositeView>LayoutManager.loadSync("IncomingBubbleLayout.xml");
        this.bubble.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
        this.bubble.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        this.bubble.capInsets = [<number>Res.getConfig("bubble_capinsets_dh1"), <number>Res.getConfig("bubble_capinsets_dv1"),
                                <number>Res.getConfig("bubble_capinsets_dh2"), <number>Res.getConfig("bubble_capinsets_dv2")];

        this.imcommingBubbleRoot = <CompositeView> this.bubble.findViewById("imcommingBubbleRoot");
        this.bubbleText = <TextView> this.bubble.findViewById("imcommingText");
        this.bubbleHangup = <ImageView> this.bubble.findViewById("smallHangUp");
        this.bubbleAnswer = <ImageView> this.bubble.findViewById("smallAnswer");
        this.info = <CompositeView> this.bubble.findViewById("info");
        this.bubbleName = <TextView> this.bubble.findViewById("imcommingName");
        this.window.addChild(this.bubble);
        this.window.setInputRegion(this.bubble.left, this.bubble.top, this.bubble.width, this.bubble.height);

        this.visible = false;
        this.initVoiceControl();
        this.addListener();
        this.bubble.on("touchstart", (e: TouchEvent) => {
            this.startX = e.changedTouches[0].screenX;
            this.startY = e.changedTouches[0].screenY;
            this.originX = this.bubble.left;
            this.originY = this.bubble.top;
        });
        this.bubble.on("touchmove", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this.bubble.on("touchend", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
    }

    _updateViewPosition(touchPositionX: number, touchPositionY: number) {
        let left = touchPositionX - this.startX + this.originX;
        let top = touchPositionY - this.startY + this.originY;
        top = Math.min(Math.max(top, 0), this.window.height - this.bubble.height);
        left = Math.min(Math.max(left, 0), this.window.width - this.bubble.width);
        this.bubble.left = left;
        this.bubble.top = top;
        this.window.setInputRegion(this.bubble.left, this.bubble.top, this.bubble.width, this.bubble.height);
    }

    initVoiceControl() {
        (<IImageView><object> this.bubbleHangup)._voiceControlEnabled = true;
        this.bubbleHangup.voiceSelectMode = View.VoiceSelectMode.Custom;
        let hangupCommand = new VoiceCommand();
        hangupCommand.customCommands = <Array<string>> Res.getConfig("hangup_commands");
        hangupCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>hangupCommand).activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        this.bubbleHangup.addVoiceCommand(hangupCommand);
        this.bubbleHangup.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice hangup");
            var hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.PAD,
                hupWay: TrackerUtil.HANGUP_WAY.VOICE};
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingBubbleResult, {
                type: "voice", result: "hangup"
            });
            //Log.d(TAG,"TrackerUtil.TrackerEvents.CardShow on screen off");
        });

        (<IImageView><object> this.bubbleAnswer)._voiceControlEnabled = true;
        this.bubbleAnswer.voiceSelectMode = View.VoiceSelectMode.Custom;
        let answerCommand = new VoiceCommand();
        answerCommand.customCommands = <Array<string>> Res.getConfig("answer_commands");
        answerCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>answerCommand).activeMode = 2; // VoiceCommand.ActiveMode.Inactive; // receive voice command for inactive window
        this.bubbleAnswer.addVoiceCommand(answerCommand);
        this.bubbleAnswer.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice answer");
            this.callService.answer(0, null, 0); // no need to set answer type as it not used in tracker
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingBubbleResult, {
                type: "voice", result: "pickup"
            });
        });
    }

    getScreenSize() {
        return {
            width: Utils.getTotalScreenWidth(), // 获取全屏幕的宽度，横屏时也包含状态栏的宽度
            height: Utils.getScreenHeight()
        };
    }

    addListener() {
        this.bubbleHangup.addGestureRecognizer(new TapRecognizer());
        this.bubbleAnswer.addGestureRecognizer(new TapRecognizer());
        this.info.addGestureRecognizer(new TapRecognizer());
        this.bubbleHangup.on("tap",()=> {
            Log.d(TAG, "onHangup called");
            this.controller.closeNotificationCenter();
            // this.moveUpAnimInit();
            var hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
                hupWay: TrackerUtil.HANGUP_WAY.CLICK};
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingBubbleResult, {type: "bulb", result: "hangup"});
            // e.stopPropagation();
        });
        this.bubbleAnswer.on("tap",() => {
            Log.d(TAG, "onAnswer called");
            this.controller.closeNotificationCenter();
            this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingBubbleResult, {
                type: "bulb", result: "pickup"
            });
            // e.stopPropagation();
            this.hide();
        });
        this.info.on("tap", () => {
            this.controller.toggleIncommingFullWindow(0);
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickComExtend, null);
            } else {
                if (this.callService.status !== CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE) {
                    TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickExtend, null);
                } else {
                    TrackerUtil.getInstance().clickCtrl(TrackerUtil.TrackerCtrls.ClickBigWindow, null);
                }
            }
        });

        this.bubbleName.on("textchange", ()=> {
            Log.d(TAG, "bubbleName textchange");
            this.resizeView();
        });
    }

    update(param: Array<IMyCallCell>) {
        Log.d(TAG, "update orientation =", this.orientation);
        if (param) {
            var callCell = param[0];
            var call: IMyCallCell = callCell.callinfo || callCell;
            if (!callCell.callerInfo) {
                this.bubbleName.text = this._callnameText || call.lineid;
            } else {
                this.bubbleName.text = this._callnameText || callCell.callerInfo.name;
            }
            this.isAiTransfer = call.aiTransfer;
            if (this._nameUpdatedNumber !== call.lineid) {
                this._nameUpdatedNumber = call.lineid;
                this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                    if (!err && name) {
                        this._callnameText = name;
                        this.bubbleName.text = name;
                    } else {
                        this._callnameText = call.lineid;
                    }
                });
            }
        } else {
            Log.w(TAG, "param is null");
        }

    }

    hide() {
        Log.d(TAG, "hide called, this.visible=", this.visible);
        if (this.visible) {
            this.window.hide();
            this.visible = false;
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CommunicationsAssistantSmallCardPage, {});
            }
        }
    }

    show() {
        if (!this.visible) {
            Log.d(TAG, "show");
            this.update(this.callService.getExistCallList());
            this.window.show();
            this.visible = true;
            if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
                // this.mtGestureGuide.visibility = View.Visibility.Visible;
            }
            if (this.isAiTransfer) {
                TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CommunicationsAssistantSmallCardPage, null, {});
            }
            // this.dropDownAnimInit();
        } else {
            Log.d(TAG, "has shown already");
        }
    }

    onShowMaxCard(e: TouchEvent) {
        this.controller.toggleIncommingFullWindow(0);
    }

    onHangup(e: TouchEvent) {
        Log.d(TAG, "onHangup called");
        this.controller.closeNotificationCenter();
        // this.moveUpAnimInit();
        var hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
            hupWay: TrackerUtil.HANGUP_WAY.CLICK};
        this.callService.hangup(hangupType, "");
        e.stopPropagation();
    }

    onAnswer(e: TouchEvent) {
        Log.d(TAG, "onAnswer called");
        this.controller.closeNotificationCenter();
        this.callService.answer(TrackerUtil.ANSWER_TYPE.FLOAT, null, 0);
        e.stopPropagation();
        this.hide();
    }

    onMotionDialogVisible(visible: boolean) {
        Log.d(TAG, "onMotionDialogVisible, visible = " + visible);
    }

    resetWidth() {
        Log.d(TAG, "resetWidth this.visible = " + this.visible);
        this.imcommingBubbleRoot.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_window_width_two"));
        this.info.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mt_float_text_width_two_sdp"));
        this.bubbleName.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mt_float_text_width_two_sdp"));
        this.bubbleText.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mt_float_text_width_two_sdp"));
    }

    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        this._nameUpdatedNumber = "";
        if (this.bubble) {
            this.bubble.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
            this.bubble.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        }
        if (this.window.destroyed === false) {
            this.window.top = Utils.isLandscape() ?  0 : Utils.getPortscapeTopPos(),
            this.window.setInputRegion(this.bubble.left, this.bubble.top, this.bubble.width, this.bubble.height);
        }
    }

    resizeView() {
        let zoomWidth = 0;
        this.bubbleName.elideMode = TextView.ElideMode.ElideNone;
        if (this.bubbleName.contentWidth > TEXT_MAX_LENTH) {
            zoomWidth = TEXT_MAX_LENTH - TEXT_MIN_LENTH;
            this.bubbleName.elideMode = TextView.ElideMode.ElideRight;
        } else if (this.bubbleName.contentWidth > TEXT_MIN_LENTH) {
            zoomWidth = this.bubbleName.contentWidth - TEXT_MIN_LENTH;
        } else {
            Log.d(TAG, "resizeView 0");
        }
        this.resetWidth();
        if (zoomWidth > 0) {
            this.imcommingBubbleRoot.width += screenInstance.getPixelBySDp(zoomWidth);
            this.info.width += screenInstance.getPixelBySDp(zoomWidth);
            this.bubbleName.width += screenInstance.getPixelBySDp(zoomWidth);
            this.bubbleText.width += screenInstance.getPixelBySDp(zoomWidth);
        }
        this.window.setInputRegion(this.bubble.left, this.bubble.top, this.bubble.width, this.bubble.height);
    }
}

export = IncomingBubble;
