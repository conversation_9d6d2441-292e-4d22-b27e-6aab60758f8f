"use strict";
import Feature = require("yunos/util/Feature");

interface IFeature {
    has: (feature: string) => boolean;
}

class DialsConstants {
    static readonly Customization = {
        ENABLE_FLOAT_WINDOW: true,
        ENABLE_BLUR: !true,
        ENABLE_NAV_BAR: !true,
        ENA<PERSON>E_FIT_SYSTEM_WINDOW: !true,
        ENABLE_SCREEN_POWER_CTRL: !true,
        ENABLE_KEYGUARD: !true,
        ENABLE_SECURITY_CHECK: true,
        ENABLE_CLOSE_NOTIFICATION: !true,
        ENABLE_EXPAND_FULL_SCREEN: !true,
        ENABLE_HAND_MOTION: (<IFeature><object>Feature).has("YUNOS_SYSCAP_VISION.HAND_MOTION"),
        CALL_TYPE_CHART: "carchart",
        CALL_TYPE_BT: "bt"
    }
}

export = DialsConstants;
