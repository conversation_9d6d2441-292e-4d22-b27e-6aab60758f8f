{"screen_width": 960, "screen_height": 1280, "status_bar_height": 48, "app_left": 44, "app_top": 32, "app_width": "{sdp(960)}", "app_height": "{sdp(1232)}", "call_info_width": 768, "root_menu_heiht": 120, "mt_float_window_height_sdp_two": "{sdp(288)}", "mt_float_window_width_sdp_two": "{sdp(936)}", "mt_float_window_height_two": 152, "mt_float_view_top": 896, "mt_float_view_left": 12, "mt_float_view_top_B": 814, "mt_float_view_capInsets": 43, "mt_float_text_width_two": "{sdp(108)}", "mt_float_text_height_two": "{sdp(36)}", "mt_float_state_height_two": "{sdp(24)}", "mt_float_text_width_two_sdp": 108, "mo_float_window_height_sdp_two": "{sdp(124)}", "mo_float_window_width_sdp_two": "{sdp(334)}", "mo_float_window_height_two": 124, "mo_float_window_width_two": 334, "mo_float_window_height_two_margin_left": 8, "mo_float_window_height_two_margin_top": 820, "mo_float_text_width_two": "{sdp(130)}", "mo_float_text_height_two": "{sdp(36)}", "mo_float_state_height_two": "{sdp(24)}", "mo_float_text_width_two_sdp": 130, "bubble_margin_top": 846, "bubble_margin_left": 28, "bubble_capinsets_dh1": 61, "bubble_capinsets_dv1": 54, "bubble_capinsets_dh2": 61, "bubble_capinsets_dv2": 70, "mt_float_window_height_sdp": "{sdp(152)}", "mt_float_window_height": 152, "mt_float_guide_height": 72, "mt_float_window_background_color": "#294976", "mo_float_window_top": 4, "mo_float_window_height": 40, "mo_float_window_margin_top": 10, "mo_float_window_text_size": "20sp", "mo_float_window_margin_left": 24, "mo_float_window_spacing": 10, "mo_float_window_bg_capinset": 24, "mo_float_windoe_icon_width": 24, "keyboard_scroll_width": "{sdp(400)}", "keyboard_scroll_height": "{sdp(64)}", "keyboard_text_width": "{sdp(100)}", "keyboard_text_height": "{sdp(64)}", "keyboard_text_size": "44sdp", "fullview_borderRadius": 30, "fullview_onecallbutton_width_1": 744, "fullview_onecallbutton_width_2": 240, "fullview_onecallbutton_width_3": 196, "fullview_onecallbutton_height_1": 80, "fullview_onecallbutton_height_2": 80, "fullview_onecallbutton_height_3": 80, "fullview_callbutton_width1": 744, "fullview_callbutton_width2": 576, "fullview_callbutton_width3": 872, "fullview_callButtonPanel_width1": 872, "fullview_callButtonPanel_width2": 872, "fullview_callButtonPanel_width3": 872, "fullview_margin_top": "{sdp(32)}", "fullview_margin_left": "{sdp(44)}", "fullview_width": "{sdp(872)}", "fullview_height": "{sdp(1048)}", "dialPanel_margin_top_one": 464, "dialPanel_margin_top_two": 464, "dialPanel_margin_top_three": 464, "callButton_margin_top_one": 830, "callButton_margin_top_two": 820, "callButton_margin_top_three": 846, "callButton_two_spacing": 96, "callButton_three_spacing": 16, "callHandler_one_sapcing": 104, "callHandler_two_sapcing": 104, "callHandler_three_sapcing": 104, "fullview_callHandler_width1": 544, "fullview_callHandler_width2": 544, "fullview_callHandler_width3": 544, "fullview_dialPanel_width1": 872, "fullview_dialPanel_width2": 872, "fullview_dialPanel_width3": 872, "MtView_callbutton_width_2": 580, "MtView_callbutton_width_3": 710, "MtView_oneCallbutton_width_2": 240, "MtView_oneCallbutton_width_3": 210, "MtView_oneCallbutton_height_2": 64, "MtView_oneCallbutton_height_3": 64, "MtView_callbutton2_spacing": 100, "MtView_callbutton3_spacing": 40, "MtView_callbuttoninfo2_spacing": 100, "MtView_callbuttoninfo3_spacing": 40, "callinfo_name_text_size": 28, "callinfo_state_text_size": 24, "callinfo_avatar_width": 200, "callinfo_avatar_margin_top": 160, "callinfo_callname_margin_top": 34, "callinfo_callstatus_margin_top": 6, "callinfo_callname_margin_top_small": 26, "callinfo_avatar_width_small": 144, "callinfo_callname_height": 40, "callinfo_callState_height": 32, "callinfo_avatar_margin_top_small": 28, "callinfo_avatarDingding_width_large": 58, "callinfo_avatarDingding_width_med": 42, "callinfo_avatarDingding_width_large_margin_left": 142, "callinfo_avatarDingding_width_large_margin_top": -58, "callinfo_avatarDingding_width_med_margin_left": 102, "callinfo_avatarDingding_width_med_margin_top": -42, "TEST_FORCE_LANDSCAPE": false, "color_blur_cover": "#00000099", "show_miss_call_indication_for_reject_mt": false, "answer_commands": ["接听"], "hangup_commands": ["挂断"], "mute_commands": ["来电静音"], "button_handsfreeMultiState": {"normal": {"src": "{img(images/qms_call_bt_speaker_car.png)}"}, "pressed": {"src": "{img(images/qms_call_bt_speaker_car_pressed.png)}"}, "disabled": {"src": "{img(images/qms_call_bt_speaker_car_disable.png)}"}}, "button_keyboardMultiState": {"normal": {"src": "{img(images/qms_call_btn_dialer_normal.png)}"}, "pressed": {"src": "{img(images/qms_call_btn_dialer_pressed.png)}"}, "disabled": {"src": "{img(images/qms_call_btn_dialer_disable.png)}"}}, "button_muteMultiState": {"normal": {"src": "{img(images/qms_call_bt_mute_normal.png)}"}, "pressed": {"src": "{img(images/qms_call_bt_mute_activated.png)}"}, "disabled": {"src": "{img(images/qms_call_bt_mute_disable.png)}"}}, "button_phoneSpeakerMultiState": {"normal": {"src": "{img(images/qms_call_bt_speaker_phone_normal.png)}"}, "pressed": {"src": "{img(images/qms_call_bt_speaker_phone_pressed.png)}"}, "disabled": {"src": "{img(images/qms_call_bt_speaker_phone_disable.png)}"}}, "dailer_key_multiState": {"normal": {"background": "transparent"}, "pressed": {"background": "{img(images/qms_call_bg_key_pressed.png)}"}}, "button_icDownMultiState": {"normal": {"src": "{img(images/qms_call_ic_down_normal.png)}"}, "pressed": {"src": "{img(images/qms_call_ic_down_pressed.png)}"}}}