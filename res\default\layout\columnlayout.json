{"moFloatLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(10)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"icon": {"margin": {"left": "{dp(24)}"}, "align": "{enum.RowLayout.Align.Middle}"}, "callState": {"margin": {"right": "{dp(24)}"}, "align": "{enum.RowLayout.Align.Middle}"}}}, "moFloatLayoutTwoInfo": {"type": "RelativeLayout", "params": {"callState": {"align": {"left": "parent", "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(0)}", "top": "{sdp(2)}"}}, "callname": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(0)}", "top": "{sdp(5)}"}}}}, "moFloatLayoutTwo": {"type": "RelativeLayout", "params": {"smallHangUp": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(25)}", "top": "{sdp(18)}"}}, "icon": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(6)}", "top": "{sdp(36)}"}}, "iconCar": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(6)}", "top": "{sdp(30)}"}}, "info": {"align": {"left": {"target": "smallHangUp", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(24)}", "top": "{sdp(18)}"}}}}, "floatCallInfoLayout": {"type": "ColumnLayout", "attrs": {"wrapContent": true}, "default": {"align": "{enum.ColumnLayout.Align.Left}", "margin": {"top": "{dp(16)}"}}}, "floatCallInfoLayoutTwoavatarInfo": {"type": "RelativeLayout", "params": {"avatar": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "avatarChart": {"align": {"right": "parent", "bottom": "parent"}, "margin": {"right": "{sdp(0)}"}}}}, "floatCallInfoLayoutTwo": {"type": "RelativeLayout", "params": {"avatarInfo": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "callname": {"align": {"left": {"target": "avatarInfo", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(32)}", "top": "{sdp(10)}"}}, "number": {"align": {"left": {"target": "avatarInfo", "side": "right"}, "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(32)}", "top": "{sdp(8)}"}}, "aiTransfer": {"align": {"left": {"target": "number", "side": "right"}, "top": {"target": "callname", "side": "bottom"}}, "margin": {"left": "{sdp(16)}", "top": "{sdp(12)}"}}}}, "floatCallInfoLayoutTwoDingding": {"type": "RelativeLayout", "params": {"avatarIncoming": {"align": {"left": "parent"}, "margin": {"left": "{sdp(124)}"}}, "callnameIncoming": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(24)}"}}, "numberIncoming": {"align": {"left": {"target": "callnameIncoming", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(16)}"}}, "individer": {"align": {"center": "parent"}}, "avatarTwo": {"align": {"left": {"target": "avatar<PERSON><PERSON><PERSON>", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(341)}"}}, "callnameTwo": {"align": {"left": {"target": "avatarT<PERSON>", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(24)}"}}, "numberTwo": {"align": {"left": {"target": "callnameTwo", "side": "right"}, "middle": "parent"}, "margin": {"left": "{sdp(16)}"}}}}, "mtGuestureFloatLayout": {"type": "RelativeLayout", "params": {"mtFloat": {"align": {"left": "parent", "right": "parent", "top": "parent"}}, "mtGestureGuide": {"align": {"left": "parent", "right": "parent", "top": {"target": "mtFloat", "side": "bottom"}}}}}, "mtFloatLayout": {"type": "RelativeLayout", "params": {"avatar": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(68)}"}}, "userInfo": {"align": {"left": {"target": "avatar", "side": "right"}, "right": {"target": "answer", "side": "left"}, "middle": "parent"}, "margin": {"left": "{sdp(36)}", "top": "{sdp(59)}"}}, "answer": {"align": {"right": {"target": "hangUp", "side": "left"}, "middle": "parent"}, "margin": {"right": "{sdp(28)}"}}, "hangUp": {"align": {"right": "parent", "middle": "parent"}, "margin": {"right": "{sdp(60)}"}}}}, "mtFloatLayoutTwo": {"type": "RelativeLayout", "params": {"mtFloatOneDingding": {"align": {"top": "parent", "center": "parent"}}, "mtFloatTwoDingding": {"align": {"top": "parent", "center": "parent"}}}}, "mtFloatLayoutOneDingding": {"type": "RelativeLayout", "params": {"userInfo": {"align": {"middle": "parent", "left": "parent"}, "margin": {"left": "{sdp(80)}"}}, "anwerhanup": {"align": {"left": "parent", "middle": "parent"}, "margin": {"left": "{sdp(572)}"}}, "minimize": {"align": {"right": "parent", "top": "parent"}, "margin": {"right": "{sdp(44)}", "top": "{sdp(48)}"}}}}, "anwerhanupLayout": {"type": "RelativeLayout", "params": {"answer": {"align": {"left": "parent"}, "margin": {"left": "{sdp(0)}"}}, "hangUp": {"align": {"right": "parent"}, "margin": {"right": "{sdp(0)}"}}}}, "mtFloatLayoutTwoDingding": {"type": "RelativeLayout", "params": {"userInfoTwo": {"align": {"top": "parent", "center": "parent"}, "margin": {"top": "{sdp(72)}"}}, "anwerhanupTwo": {"align": {"bottom": "parent", "center": "parent"}, "margin": {"bottom": "{sdp(60)}"}}, "minimizeTwo": {"align": {"right": "parent", "top": "parent"}, "margin": {"right": "{sdp(56)}", "top": "{sdp(52)}"}}}}, "anwerhanupLayoutTwo": {"type": "RowLayout", "attrs": {"spacing": "{sdp(40)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "imcommingBubbleLayoutInfo": {"type": "RelativeLayout", "params": {"imcommingText": {"align": {"left": "parent", "top": {"target": "imcommingName", "side": "bottom"}}, "margin": {"top": "{sdp(2)}", "left": "{sdp(0)}"}}, "imcommingName": {"align": {"left": "parent", "top": "parent"}, "margin": {"top": "{sdp(5)}", "left": "{sdp(0)}"}}}}, "imcommingBubbleLayout": {"type": "RelativeLayout", "params": {"smallAnswer": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(25)}", "top": "{sdp(18)}"}}, "smallHangUp": {"align": {"left": {"target": "info", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(18)}"}}, "info": {"align": {"left": {"target": "smallAnswer", "side": "right"}, "top": "parent"}, "margin": {"left": "{sdp(16)}", "top": "{sdp(18)}"}}}}, "mtGuestureFloatLayoutTwo": {"type": "RelativeLayout", "params": {"mtFloat": {"align": {"top": "parent", "center": "parent"}}, "mtGestureGuide": {"align": {"top": {"target": "mtFloat", "side": "bottom"}}}}}, "mtGestureGuideFloatLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(8)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"twoFingered": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"left": "{dp(449)}"}}, "guideText": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"right": "{dp(22)}"}}}}, "scrollLayout": {"type": "RelativeLayout", "params": {"bigNumber": {"align": {"middle": "parent", "left": "parent"}}}}, "keyboardPanelLayout": {"type": "RelativeLayout", "params": {"hide": {"align": {"top": "parent", "left": "parent", "right": "parent"}, "margin": {"top": "{sdp(0)}", "left": "{sdp(118)}", "right": "{sdp(674)}"}}, "scroll": {"align": {"top": "parent", "left": "parent", "right": "parent"}, "margin": {"top": "{sdp(12)}", "left": "{sdp(243)}", "right": "{sdp(229)}"}}, "keyboardBackground": {"align": {"top": {"target": "scroll", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(41.5)}"}}, "keyboardLayout": {"align": {"top": {"target": "keyboardBackground", "side": "bottom"}, "left": "parent", "right": "parent"}, "margin": {"top": "{sdp(79.5)}", "left": "{sdp(123)}", "right": "{sdp(123)}"}}, "keyboardHangUp": {"align": {"bottom": "parent", "center": "parent"}, "margin": {"bottom": "{sdp(138)}"}}}}, "keyboardGridLayout": {"type": "GridLayout", "default": {}, "attrs": {"rows": 4, "columns": 3, "rowSpacing": "{sdp(18)}", "columnSpacing": "{sdp(178)}"}}, "keyLayout": {"type": "RelativeLayout", "default": {"align": {"center": "parent", "middle": "parent"}}}, "callInfoOneItemLayout": {"type": "RelativeLayout", "params": {"avatar": {"align": {"center": "parent", "top": "parent"}, "margin": {"top": "{sdp(0)}"}}, "avatarDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(479)}", "top": "{sdp(143)}"}}, "callname": {"align": {"center": "parent", "top": {"target": "avatar", "side": "bottom"}}, "margin": {"top": "{sdp(24)}"}}, "callState": {"align": {"center": "parent", "top": {"target": "callname", "side": "bottom"}}, "margin": {"top": "{sdp(8)}"}}}}, "callInfoTwoItemLayout": {"type": "RelativeLayout", "params": {"avatarOne": {"align": {"top": "parent", "left": "parent"}, "margin": {"left": "{sdp(154)}", "top": "{sdp(0)}"}}, "avatarOneDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(298)}", "top": "{sdp(144)}"}}, "callnameOne": {"align": {"left": "parent", "top": {"target": "avatarOne", "side": "bottom"}}, "margin": {"left": "{sdp(154)}", "top": "{sdp(24)}"}}, "callStateOne": {"align": {"left": "parent", "top": {"target": "callnameOne", "side": "bottom"}}, "margin": {"left": "{sdp(154)}", "top": "{sdp(8)}"}}, "avatarTwo": {"align": {"top": "parent", "left": "parent"}, "margin": {"left": "{sdp(560)}", "top": "{sdp(28)}"}}, "avatarTwoDing": {"align": {"left": "parent", "top": "parent"}, "margin": {"left": "{sdp(662)}", "top": "{sdp(130)}"}}, "callnameTwo": {"align": {"left": "parent", "top": {"target": "avatarT<PERSON>", "side": "bottom"}}, "margin": {"left": "{sdp(532)}", "top": "{sdp(24)}"}}, "callStateTwo": {"align": {"left": "parent", "top": {"target": "callnameTwo", "side": "bottom"}}, "margin": {"left": "{sdp(532)}", "top": "{sdp(8)}"}}}}, "callInfoLayout": {"type": "RowLayout", "default": {"align": "{enum.RowLayout.Align.Top}"}}, "callInfoItemLayout": {"type": "ColumnLayout", "attrs": {"wrapContent": true}, "default": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(0)}"}}, "params": {"_avatar": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(0)}"}}, "_callname": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(34)}"}}, "_callState": {"align": "{enum.ColumnLayout.Align.Center}", "margin": {"top": "{sdp(6)}"}}}}, "mtGestureGuideLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{dp(8)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}, "params": {"twoFingered": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"left": "{dp(32)}"}}, "guideText": {"align": "{enum.RowLayout.Align.Middle}", "margin": {"right": "{dp(22)}"}}}}, "callHandlerLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{sdp(104)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "callButtonLayout": {"type": "RowLayout", "attrs": {"wrapContent": true, "spacing": "{sdp(16)}"}, "default": {"align": "{enum.RowLayout.Align.Middle}"}}, "dialPanelLayout": {"type": "RelativeLayout", "params": {"callHandler": {"align": {"top": "parent", "center": "parent"}, "margin": {"top": "{sdp(464)}"}}, "info": {"align": {"top": {"target": "call<PERSON><PERSON><PERSON>", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(12)}"}}}}, "callButtonPanelLayout": {"type": "RelativeLayout", "params": {"callButton": {"align": {"top": "parent", "center": "parent"}, "margin": {"top": "{sdp(0)}"}}, "callButtonInfo": {"align": {"top": {"target": "callButton", "side": "bottom"}, "center": "parent"}, "margin": {"top": "{sdp(16)}"}}}}, "callLayout": {"type": "RelativeLayout", "params": {"back": {"align": {"top": "parent", "right": "parent"}, "margin": {"right": "{sdp(24)}", "top": "{sdp(24)}"}}, "dialPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(154)}"}}, "keyboardPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(154)}"}}, "callButtonPanel": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(382)}", "left": "{sdp(0)}"}}, "callInfoRoot": {"align": {"top": "parent"}, "margin": {"top": "{sdp(154)}"}}}}, "aiHoldFloatLayout": {"type": "RelativeLayout", "params": {"icon": {"align": {"top": "parent", "left": "parent"}, "margin": {"top": "{sdp(37)}", "left": "{sdp(44)}"}}, "callname": {"align": {"top": "parent", "left": {"target": "icon", "side": "right"}}, "margin": {"top": "{sdp(26)}", "left": "{sdp(9)}"}}, "callState": {"align": {"top": "parent", "left": {"target": "callname", "side": "right"}}, "margin": {"top": "{sdp(28)}", "left": "{sdp(16)}"}}, "aiTransfer": {"align": {"top": {"target": "callname", "side": "bottom"}, "left": {"target": "icon", "side": "right"}}, "margin": {"top": "{sdp(8)}", "left": "{sdp(14)}"}}}}}