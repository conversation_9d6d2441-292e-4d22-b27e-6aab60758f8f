/* 通话中，小的悬浮窗 */
"use strict";

import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import WindowCAF = require("yunos/ui/view/Window");
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TextView = require("yunos/ui/view/TextView");
import TouchEvent = require("yunos/ui/event/TouchEvent");

import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();

import Utils = require("../utils/Utils");
import BaseLayout = require("./BaseLayout");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import Log = require("../utils/Logs");
import TapRecognizer = require("yunos/ui/gesture/TapRecognizer");
import TrackerUtil = require("../utils/TrackerUtil");
import View = require("yunos/ui/view/View");
import DialsConstants = require("../utils/DialsConstants");

const TAG = "IncallingFloatWindowTwo";
const TEXT_MIN_LENTH = 130;
const TEXT_MAX_LENTH = 180;

interface IPage {
    pageapi: object;
}

interface IWindowParams {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
}

interface IMyWindow {
    specifiedMode: number;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class IncallingFloatWindowTwo extends BaseLayout {
    public visible: boolean;
    private mainView: CompositeView;
    private winParam: IWindowParams;
    private window: WindowCAF;
    private root: CompositeView;
    private icon: ImageView;
    private smallHangUp: ImageView;
    private callname: TextView;
    private callState: TextView;
    private _callnameText: string;
    private _nameUpdatedNumber: string;
    private startX: number;
    private startY: number;
    private originX: number;
    private originY: number;
    private info: CompositeView;
    private iconCar: ImageView;
    private callType: string;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);
        this.visible = false;
        this.createLayout();

        return;
    }

    createLayout() {

        this.mainView = <CompositeView>LayoutManager.loadSync("MoBubbleLayout.xml");
        this._view = this.mainView;

        let screenSize = this.getScreenSize();
        this.winParam = {};
        this.winParam.height = screenSize.height;
        // must know content's length, and then re-calculate width
        this.winParam.width = screenSize.width;
        this.winParam.x = 0;
        this.winParam.y = 0;

        this.window = <WindowCAF>WindowCAF.create(this.controller.mainPage, {
            left: Utils.isLandscape() ? Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ? Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFloatWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        (<IMyWindow><object>this.window).specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";

        this._view.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
        this._view.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        this._view.capInsets = [<number>Res.getConfig("bubble_capinsets_dh1"), <number>Res.getConfig("bubble_capinsets_dv1"),
        <number>Res.getConfig("bubble_capinsets_dh2"), <number>Res.getConfig("bubble_capinsets_dv2")];

        this.root = <CompositeView>this._view.findViewById("root");
        this.icon = <ImageView>this._view.findViewById("icon");
        this.smallHangUp = <ImageView>this._view.findViewById("smallHangUp");
        this.callState = <TextView>this._view.findViewById("callState");
        this.callname = <TextView>this._view.findViewById("callname");
        this.info = <CompositeView>this._view.findViewById("info");
        this.iconCar = <ImageView>this._view.findViewById("iconCar");

        Log.v(TAG, "IncallingFloatWindow createLayout", this.window.height, this.window.width);
        this.window.addChild(this.mainView);
        this.info.addGestureRecognizer(new TapRecognizer());
        this.smallHangUp.addGestureRecognizer(new TapRecognizer());
        this.info.on("tap", () => {
            this.switchToFullScreen();
        });

        this.callname.on("textchange", () => {
            Log.d(TAG, "callname textchange");
            this.resizeView();
        });

        this.smallHangUp.on("tap", () => {
            this.controller.closeNotificationCenter();
            var hangupType = {
                hupScene: TrackerUtil.HANGUP_SCENE.FLOAT,
                hupWay: TrackerUtil.HANGUP_WAY.CLICK
            };
            this.callService.hangup(hangupType, "");
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.CallResult, {
                type: "byhand", result: "hangUp"
            });
        });

        this._view.on("touchstart", (e: TouchEvent) => {
            this.startX = e.changedTouches[0].screenX;
            this.startY = e.changedTouches[0].screenY;
            this.originX = this._view.left;
            this.originY = this._view.top;
        });
        this._view.on("touchmove", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });
        this._view.on("touchend", (e: TouchEvent) => {
            this._updateViewPosition(e.changedTouches[0].screenX, e.changedTouches[0].screenY);
        });

        return;
    }

    _updateViewPosition(touchPositionX: number, touchPositionY: number) {
        let left = touchPositionX - this.startX + this.originX;
        let top = touchPositionY - this.startY + this.originY;
        top = Math.min(Math.max(top, 0), this.window.height - this._view.height);
        left = Math.min(Math.max(left, 0), this.window.width - this._view.width);
        this._view.left = left;
        this._view.top = top;
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }

    getScreenSize() {
        return {
            width: Utils.getTotalScreenWidth(), // 获取全屏幕的宽度，横屏时也包含状态栏的宽度
            height: Utils.getScreenHeight()
        };
    }
    update(param: Array<IMyCallCell>, timecount: number) {
        if (!param || !this.mainView || !this.window ||
            !this.root || !this.callState || param.length === 0) {
            Log.w(TAG, "some unnormal thing happened, we can't update window, return.");
            return;
        }

        let callCell = param[0];
        let call: IMyCallCell = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status);
        this.callType = call.callType;
        if (!callCell.callerInfo) {
            this.callname.text = this._callnameText || call.lineid;
        } else {
            this.callname.text = this._callnameText || callCell.callerInfo.name;
        }
        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && timecount) {
                this.callState.text = Utils.getTimeLength(timecount, callCell.startCount);
            } else {
                this.callState.text = this.controller.callStatusToString(callCell, timecount);
            }
        }

        if (call.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
            this.iconCar.visibility = View.Visibility.Visible;
            this.icon.visibility = View.Visibility.None;
        } else {
            this.iconCar.visibility = View.Visibility.None;
            this.icon.visibility = View.Visibility.Visible;
        }

        Log.d(TAG, "show float window in update()");
        this.window.show();
        this.visible = true;

        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                    if (this.callname) {
                        this.callname.text = name;
                    }
                } else {
                    this._callnameText = call.lineid;
                }
            });
        }
    }

    updateCallState(callList: Array<IMyCallCell>, curtime: number) {
        let callCell = callList[0];
        let call: IMyCallCell = callCell.hasOwnProperty('callinfo') && callCell.callinfo ? callCell.callinfo : callCell;
        Log.d(TAG, "updateCallState call.callid = ", call.callid, ", state = ", call.status);

        if (this._nameUpdatedNumber !== call.lineid) {
            this._nameUpdatedNumber = call.lineid;
            Log.d(TAG, "updateCallState call.lineid  changed, need to fetch again ", call.lineid);
            this.controller.fetchCallName(call.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText = name;
                } else {
                    this._callnameText = call.lineid;
                }
            });
            return;
        }

        if (this.callState) {
            if (callCell.status === CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE && curtime) {
                this.callState.text = Utils.getTimeLength(curtime, callCell.startCount);
            } else {
                this.callState.text = this.controller.callStatusToString(callCell, curtime);
            }
        }
        Log.d(TAG, "updateCallState, curtime=", curtime, " starttime=", callCell.startCount,
            " this.callname.text=  this.callState.text=", this.callState.text);
    }

    hide() {
        //if (this.visible) {
        Log.d(TAG, "IncallingFloatWindow hide called");
        this.window.hide();
        this.visible = false;
        TrackerUtil.getInstance().leavePage(TrackerUtil.TrackerPages.CallPage, { from: this.callType === "bt" ? "bluetooth" : "carchat" });
        Log.d(TAG, "set this.visible to false");
        // }
    }

    show() {
        // if (!this.visible) {
        this.window.show();
        this.visible = true;
        Log.d(TAG, "show called, set this.visible to true");
        TrackerUtil.getInstance().enterPage(TrackerUtil.TrackerPages.CallPage, null, {});
        // }
    }

    switchToFullScreen() {
        Log.d(TAG, "switchToFullScreen called");
        this.controller.closeNotificationCenter();
        if (this.controller.mainPage && this.controller.mainPage.destroyed === false
            && (<IPage><object>this.controller.mainPage).pageapi) {
            // main page is still alive
            Log.d(TAG, "calldisplayer main page is still alive");
            let callList = this.callService.getExistCallList();
            let call: IMyCallCell = callList[0].callinfo || callList[0];
            if (callList.length === 1 && call.callType === DialsConstants.Customization.CALL_TYPE_CHART) {
                Log.d(TAG, "one call carchart switch fullview need to hideself and sendlink to carchart");
                this.controller.setThirdPartOutgoingCall(true);
                this.controller.sendLink2ChartCarPage();
            }
            this.controller.toggleFloatFullWindow(1); // ViewController.FULL_WINDOW);
            this.controller.sendLink2MainPage();
        } else {
            // should not come here
            Log.e(TAG, "why call displayer main page not alive?");
        }
    }

    onWindowTouchDown() {

    }

    onWindowTouchUp() {

        this.switchToFullScreen();
    }

    resetPosition() {
        Log.d(TAG, "resetPosition this.visible = " + this.visible);
        if (this._view) {
            this._view.top = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_top"));
            this._view.left = screenInstance.getPixelBySDp(<number>Res.getConfig("bubble_margin_left"));
        }
        if (this.window.destroyed === false) {
            this.window.top = Utils.isLandscape() ? 0 : Utils.getPortscapeTopPos(),
                this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
        }
    }

    resetUIContent() {
        Log.d(TAG, "resetUIContent called");
        if (this.callState) {
            this.callState.text = "";
        }
        this.callname.text = "";
        this.clearPhoneNameAndNumber();
    }

    resetWidth() {
        Log.d(TAG, "resetWidth this.visible = " + this.visible);
        this.root.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_window_width_two"));
        this.info.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_text_width_two_sdp"));
        this.callname.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_text_width_two_sdp"));
        this.callState.width = screenInstance.getPixelBySDp(<number>Res.getConfig("mo_float_text_width_two_sdp"));
    }

    clearPhoneNameAndNumber() {
        this._nameUpdatedNumber = null;
        this._callnameText = null;
    }

    resizeView() {
        let zoomWidth = 0;
        this.callname.elideMode = TextView.ElideMode.ElideNone;
        if (this.callname.contentWidth > TEXT_MAX_LENTH) {
            zoomWidth = TEXT_MAX_LENTH - TEXT_MIN_LENTH;
            this.callname.elideMode = TextView.ElideMode.ElideRight;
        } else if (this.callname.contentWidth > TEXT_MIN_LENTH) {
            zoomWidth = this.callname.contentWidth - TEXT_MIN_LENTH;
        } else {
            Log.d(TAG, "resizeView 0");
        }
        this.resetWidth();
        if (zoomWidth > 0) {
            Log.d(TAG, "zoomout");
            this.root.width += screenInstance.getPixelBySDp(zoomWidth);
            this.info.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callname.width += screenInstance.getPixelBySDp(zoomWidth);
            this.callState.width += screenInstance.getPixelBySDp(zoomWidth);
        }
        this.window.setInputRegion(this._view.left, this._view.top, this._view.width, this._view.height);
    }

}

export = IncallingFloatWindowTwo;
