"use strict";

const utTools = require("../UTTools");


class MockInCallService {
    constructor() {
        this.events = {};
    }

    static getInstance() {
        if (!MockInCallService._inst) {
            MockInCallService._inst = new MockInCallService();
            utTools.MockInCallService = MockInCallService._inst;
        }

        return MockInCallService._inst;
    }

    on(event, cb) {
        this.events[event] = cb;
    }

    emit(event, ...params) {
        this.events[event](...params);
    }

    turnOffProximitySensor(...params) {
        this.proximityOffParams = params;
    }

    turnOnProximitySensor(...params) {
        this.proximityOnParams = params;
    }

    disconnectCall(...params) {
        this.discParams = params;
    }

    disconnectConference(...params) {
        this.discConfParams = params;
    }

    rejectCall(...params) {
        this.rejectParams = params;
    }

    answerCall(...params) {
        this.answerCallParams = params;
    }

    holdCall(...params) {
        this.holdCallParams = params;
    }

    splitFromConference(...params) {
        this.splitConfParams = params;
    }

    playDtmfTone(...params) {
        this.playDtmfToneParams = params;
    }

    setMute(...params) {
        this.setMuteParams = params;
    }

    setAudioRoute(...params) {
        this.setAudioRouteParams = params;
    }

    conference(...params) {
        this.conferenceParams = params;
    }

    startVoiceRecording(...params) {
        this.recordStartParams = params;
    }

    stopVoiceRecording(...params) {
        this.recordStopParams = params;
    }

    stopInCallAdapterProxy(...params) {
        this.stopInCallParams = params;
    }
}

module.exports = MockInCallService;
