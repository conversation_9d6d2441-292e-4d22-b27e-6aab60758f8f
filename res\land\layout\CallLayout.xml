<CompositeView id="root" layout="{layout.callLayout}" background="linear-gradient(to bottom, #1A2027, #262E37)"
    left="{config.fullview_margin_left}" top="{config.fullview_margin_top}"  width="{config.fullview_width}" height="{config.fullview_height}" >
    <include markup="CallInfo"/>
    <CompositeView id="dialPanel" layout="{layout.dialPanelLayout}" width="{sdp(875)}" height="{sdp(188)}" >
        <CompositeView id="callHandler" height="{sdp(100)}" layout="{layout.callHandlerLayout}">
            <ImageView id="handsfree"  width="{sdp(100)}" height="{sdp(100)}" src="{img(images/qms_call_bt_speaker_car.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
                />
            <ImageView id="phoneSpeaker"  width="{sdp(100)}" height="{sdp(100)}" src="{img(images/qms_call_bt_speaker_phone_normal.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
                 visibility="{enum.View.Visibility.None}"/>
            <ImageView id="keyboard"  width="{sdp(100)}" height="{sdp(100)}" src="{img(images/qms_call_btn_dialer_normal.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
                />
            <ImageView id="mute"  width="{sdp(100)}" height="{sdp(100)}" src="{img(images/qms_call_bt_mute_normal.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"
                />
        </CompositeView>
        <CompositeView id="info" height="{sdp(24)}" layout="{layout.callHandlerLayout}">
                <TextView id="handsfreeText" propertySetName="extend/hdt/FontBody4"  align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.TEXT_AUDIO_CHANGE}"/>
                <TextView id="phoneSpeakerText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.TEXT_AUDIO_PHONE}" visibility="{enum.View.Visibility.None}"/>
                <TextView id="keyboardText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.TEXT_DIALPAD}"/>
                <TextView id="muteText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.BTN_MUTE}"/>
        </CompositeView>
    </CompositeView>
    <ImageView id="back" src="{img(images/qms_call_ic_down_normal.png)}"
        height="{sdp(60)}" width="{sdp(60)}"/>
    <CompositeView id="callButtonPanel" layout="{layout.callButtonPanelLayout}" width="{sdp(875)}" height="{sdp(200)}" >
        <CompositeView id="callButton" height="{sdp(64)}" width="{sdp(875)}" layout="{layout.callButtonLayout}">
            <ButtonBM id="answer" iconSrc="{img(images/qms_call_bt_answer_short.png)}"
                height="{sdp(64)}" width="{sdp(280)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}"  contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="holdAnswer" iconSrc="{img(images/qms_call_btn_answer_reservation_normal.png)}"
                height="{sdp(64)}" width="{sdp(280)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="merge" iconSrc="{img(images/qms_call_btn_merge_normal.png)}"
                height="{sdp(100)}" width="{sdp(100)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="switch" iconSrc="{img(images/qms_call_btn_switch_normal.png)}"
                height="{sdp(100)}" width="{sdp(100)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                visibility="{enum.View.Visibility.None}"/>
            <ButtonBM id="hangUp" iconSrc="{img(images/qms_call_bt_hang_up_long.png)}"
                buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
                height="{sdp(64)}" width="{sdp(280)}"/>
        </CompositeView>
        <CompositeView id="callButtonInfo" height="{sdp(30)}" width="{sdp(875)}" layout="{layout.callButtonLayout}" visibility="{enum.View.Visibility.None}">
                <TextView id="answerText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.TEXT_ANSWER}"/>
                <TextView id="holdText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.TEXT_ANSWER_HOLD}"/>
                <TextView id="hangUpText" propertySetName="extend/hdt/FontBody4" align="{enum.TextView.Align.Center}" color="{theme.color.White_3}" width="{sdp(100)}" text="{string.BTN_HANG_UP}"/>
        </CompositeView>
    </CompositeView>

</CompositeView>
