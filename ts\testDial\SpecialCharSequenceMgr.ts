"use strict";

import AlertDialog = require("yunos/ui/widget/AlertDialog");
const android = require("android");

const MMI_IMEI_DISPLAY = "*#06#";
const MMI_USB_PORT_SWITCH = "*#061#";
const MMI_ATCI_PORT_SWITCH = "*#062#";
const EXTERNAL_VERSION_DISPLAY = "*#166*#";
const INTERNAL_VERSION_DISPLAY = "*#29826633#";

const FINGER_PRINT_COLLECTION = "*#*#378665#*#*";
const PROPERTY_FINGER_PRINT_KEY = "persist.sys.eng.debug.fp.enable";
const PROPERTY_INTERNAL_VERSION_KEY = "ckt.internal.version";
const PROPERTY_EXTERNAL_VERSION_KEY = "ro.build.display.id";
const SECRET_CODE_ACTION = "android.provider.Telephony.SECRET_CODE";
const IMEI_14_DIGIT = 14;

const TAG = "SpecialCharSequenceMgr";
var DEBUG = true;

var OngoingDialog: AlertDialog = null;
const plugEnabled = false;


class SpecialCharSequenceMgr {

    static handleChars(input: string) {
        if (!input) {
            return false;
        }
        // get rid of the separators so that the string gets parsed correctly
        // var dialString = PhoneNumberUtils.stripSeparators(input);

        if (this.handleIMEIDisplay(input) ||
            this.handleSecretCode(input) ||
            this.handleUSBPortEntry(input) ||
            this.handleAtciPortEntry(input) ||
            this.handleInternalOrExternalVersion(input)) {
            return true;
        }
        
        return false;
    }

    static handleIMEIDisplay(input: string) {


        let dialogTitle: string = null;

        if (MMI_IMEI_DISPLAY === input) {
            var TelephonyManager = require("yunos/telephony/TelephonyManager").getInstance();
            if (!TelephonyManager) {
                return true;
            }
            TelephonyManager.getPhoneType(function(err: Error, type: number) {
                if (err) {
                    // log.E(TAG, "handleIMEIDisplay: getPhoneType error 1", err);
                    return;
                }
                dialogTitle = type === TelephonyManager.PHONE_TYPE_GSM ? "IMEI" : "MEID";
                TelephonyManager.getPhoneCount(function(err: Error, phoneCount: number) {
                    if (err) {
                        // log.E(TAG, "handleIMEIDisplay: getPhoneCount error ", err);
                        return;
                    }

                    let message = "";
                    let deviceIds: Array<string> = [];
                    let meidStr = "";
                    let count = 0;
                    let allCount = phoneCount + 1;
                    TelephonyManager.getPhoneTypeForPhone(0, function(err: Error, slotId: number, phoneType: number) {
                        if (err) {
                            count++;
                            // log.E(TAG, "handleIMEIDisplay: getPhoneTypeForPhone error ", err);
                            return;
                        }
                        if (phoneType === TelephonyManager.PHONE_TYPE_CDMA) {
                            TelephonyManager.getMeidForPhone(0, function(err: Error, slotId: number, meid: string) {
                                count++;

                                meidStr = meid ? meid : "";

                                if (count === allCount) {
                                    if (meidStr) {
                                        message = "MEID: " + meidStr + "\n";
                                    }
                                    let deviceIdsLength = deviceIds.length;
                                    if (deviceIdsLength === 1) {
                                        message += "IMEI: " + deviceIds[0];
                                    } else if (deviceIdsLength === 2) {
                                        message += "IMEI1: " + deviceIds[0] + "\n" + "IMEI2: " + deviceIds[1];
                                    }
                                    SpecialCharSequenceMgr.showIMEIDMessageDialog(dialogTitle, message);
                                }
                            });
                        } else {
                            count++;
                            if (count === allCount) {
                                if (meidStr) {
                                    message = "MEID: " + meidStr + "\n";
                                }
                                let deviceIdsLength = deviceIds.length;
                                if (deviceIdsLength === 1) {
                                    message += "IMEI: " + deviceIds[0];
                                } else if (deviceIdsLength === 2) {
                                    message += "IMEI1: " + deviceIds[0] + "\n" + "IMEI2: " + deviceIds[1];
                                }
                                SpecialCharSequenceMgr.showIMEIDMessageDialog(dialogTitle, message);
                            }
                        }
                    });
                    for (let slot = 0; slot < phoneCount; slot++) {
                        /* jshint loopfunc:true */
                        TelephonyManager.getImeiForPhone(slot, function(err: Error, slotId: number, imei: string) {
                            count++;

                            let enable14DigitImei = false;
                            enable14DigitImei = false;

                            if (enable14DigitImei && imei !== null && imei.length > IMEI_14_DIGIT) {
                                imei = imei.substring(0, IMEI_14_DIGIT);
                            }

                            deviceIds[slotId] = imei;

                            if (count === allCount) {
                                if (meidStr) {
                                    message = "MEID: " + meidStr + "\n";
                                }
                                let deviceIdsLength = deviceIds.length;
                                if (deviceIdsLength === 1) {
                                    message += "IMEI: " + deviceIds[0];
                                } else if (deviceIdsLength === 2) {
                                    message += "IMEI1: " + deviceIds[0] + "\n" + "IMEI2: " + deviceIds[1];
                                }
                                SpecialCharSequenceMgr.showIMEIDMessageDialog(dialogTitle, message);
                            }
                        });
                    }
                });
            });

            return true;
        }
        return false;
    }

    static showIMEIDMessageDialog(title: string, message: string) {

        let alertDialog = new AlertDialog();
        alertDialog.buttons = ["YES"];
        // alertDialog.getButton(0).color = "#02c57e";
        alertDialog.title = title;
        alertDialog.message = message;
        alertDialog.show();
        SpecialCharSequenceMgr.setOngoingDialog(alertDialog);
    }

    static closeOngoingDialog() {
        if (OngoingDialog) {
            if (OngoingDialog.isShowing()) {
                OngoingDialog.close();
            }
            OngoingDialog = null;
        }
    }

    static setOngoingDialog(dialog: AlertDialog) {
        if (OngoingDialog && OngoingDialog.isShowing()) {
            OngoingDialog.close();
        }
        OngoingDialog = dialog;
    }


    static handleSecretCode(input: string) {
        var len = input.length;
        if (len > 8 && input.substring(0, 4) === "*#*#" && input.substring(input.length - 4) === "#*#*") {

            if (input === FINGER_PRINT_COLLECTION) {
                this.handlefingerPrintSecretCode(input);
                return true;
            }

            if (plugEnabled) {
                let Context = android.content.Context;
                let Intent = android.content.Intent;
                let Uri = android.net.Uri;
                try {
                    let intent = new Intent(SECRET_CODE_ACTION);
                    intent.setData(Uri.parse("android_secret_code://" + input.substring(4, len - 4)));
                    Context.getApplicationContext().sendBroadcast(intent);

                    return true;
                } catch (e) {
                    // log.E(TAG, "handleSecretCode: error ", e);
                    return false;
                }
            }
        }
        return false;
    }

    static handlefingerPrintSecretCode(input: string) {
        let alertDialog = new AlertDialog();
        alertDialog.buttons = ["NO", "YES"];
        // alertDialog.getButton(1).color = "#02c57e";
        alertDialog.title = "Signed fingerprint collection agreement?";
        alertDialog.message = "Make sure your sign hard copy of fingerprint collection agreement before proceeding. If not, click No to exit and contact HTC staff before using this phone.";
        alertDialog.show();
        this.setOngoingDialog(alertDialog);

    }

    static handleUSBPortEntry(input: string) {

        if (input === MMI_USB_PORT_SWITCH) {

            if (plugEnabled) {
                let Context = android.content.Context;
                let Intent = android.content.Intent;
                try {
                    let intent = new Intent("android.intent.action.USB_PORT_SWITCH");
                    // intent.addCategory(Intent.CATEGORY_DEFAULT);
                    Context.getApplicationContext().sendBroadcast(intent);

                    return true;
                } catch (e) {
                    return false;
                }
            }
        }
        return false;
    }

    static handleAtciPortEntry(input: string) {

        if (input === MMI_ATCI_PORT_SWITCH) {

            if (plugEnabled) {

                return true;
            }
        }
        return false;
    }

    static handleInternalOrExternalVersion(input: string) {

        if (input === INTERNAL_VERSION_DISPLAY || input === EXTERNAL_VERSION_DISPLAY) {
            let isInternalVersion = input === INTERNAL_VERSION_DISPLAY;

            let message = "";

            let title = "EXTERNAL VERSION";
            let alertDialog = new AlertDialog();
            alertDialog.buttons = ["YES"];
            alertDialog.title = title;
            alertDialog.message = message;
            alertDialog.show();
            this.setOngoingDialog(alertDialog);
            return true;
        }
        return false;
    }

}

export = SpecialCharSequenceMgr;
