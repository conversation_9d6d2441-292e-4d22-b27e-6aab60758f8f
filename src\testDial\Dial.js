"use strict";
process.env.CAFUI2 = "true";
const Page = require("yunos/page/Page");
const Log = require("../utils/Logs");
const resource = require("./resource");
const TAG = "Dial";
class Dial extends Page {
    onCreate() {
        this.window.width = resource.getPixelByDp(800);
        this.window.height = resource.getPixelByDp(600);
    }
    onStart() {
        Log.d(TAG, "onStart");
        super.onStart();
        var DialpadView = require("./DialpadView");
        var dialpad = new DialpadView(this);
        dialpad.width = this.window.width;
        dialpad.height = this.window.height;
        this.window.addChild(dialpad);
    }
}
module.exports = Dial;
