"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");
const TouchEvent = require("yunos/ui/event/TouchEvent");
const View = require("yunos/ui/view/View");

const callANumber = "15811111111";
const callBNumber = "15800000000";

const dialingCallA = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 3
};

const alertingCallA = {
    _subId: 0,
    _callId: 1,
    _state: 4,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const activeCallA = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const holdCallA = {
    _subId: 0,
    _callId: 1,
    _state: 2,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 3
};

const hangupCallA = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 3
};

const dialingCallB = {
    _subId: 0,
    _callId: 2,
    _state: 3,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 3
};

const alertingCallB = {
    _subId: 0,
    _callId: 2,
    _state: 4,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const incomingCallB = {
    _subId: 0,
    _callId: 2,
    _state: 5,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const activeCallB = {
    _subId: 0,
    _callId: 2,
    _state: 1,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const activeMultiCallB = {
    _subId: 0,
    _callId: 2,
    _state: 1,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 3,
    _phoneType: 3
};

const hangupCallB = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 3
};

const hangupMultiCallB = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 3,
    _capability: 1734955885,
    _phoneType: 3
};

const fbHangupMultiCallB = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 49,
    _capability: 1734955885,
    _phoneType: 3
};

const activeMultiCallC = {
    _subId: 0,
    _callId: 470362292,
    _state: 1,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 0,
    _capability: 3,
    _phoneType: 3
};

const activeGsmMultiCallC = {
    _subId: 0,
    _callId: 470362292,
    _state: 1,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 0,
    _capability: 3,
    _phoneType: 1
};

const hangupGsmMultiCallC = {
    _subId: 0,
    _callId: 470362292,
    _state: 7,
    _displayName: "",
    _callNumber: callANumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 3,
    _capability: 2,
    _phoneType: 1
};

const activeMultiCallD = {
    _subId: 0,
    _callId: 1774834395,
    _state: 1,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 0,
    _capability: 3,
    _phoneType: 3
};

const activeGsmMultiCallD = {
    _subId: 0,
    _callId: 1774834395,
    _state: 1,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 0,
    _capability: 3,
    _phoneType: 1
};

const hangupGsmMultiCallD = {
    _subId: 0,
    _callId: 1774834395,
    _state: 7,
    _displayName: "",
    _callNumber: callBNumber,
    _multiParty: 1,
    _emergencyCall: 0,
    _disconnectCause: 3,
    _capability: 1734955885,
    _phoneType: 1
};

const touchup = new TouchEvent({
    type: "touchend",
    changedTouches: [{
        clientX: 100,
        clientY: 10,
        identifier: 0
    }],
    eventPhase: TouchEvent.EventPhase.Target
});

describe("VoLTECall", function() {
    utTools.log("VoLTECall start");

    var originalTimeout;

    beforeAll(() => {
        utTools.log("VoLTECall beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("VoLTECall afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("VoLTECall beforeEach");

        // dialing -> alerting -> active -> disconnect
        utTools.asyncRun(done, function *() {
            utTools.log("VoLTECall beforeEach run");
            utTools.MockInCallService.emit("calladded", dialingCallA);
            uiInf.setOperatorInfo(0, String.fromCodePoint(0x4e2d, 0x56fd, 0x79fb, 0x52a8)); // ChinaMobile
            yield 3000;
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.hd.visibility, View.Visibility.Visible);

            utTools.MockInCallService.emit("callstatechanged", alertingCallA);
            yield 3000;
            utTools.equal(moLayout.callState.text, global.res.getString("TEXT_RINGING"));
            utTools.equal(moLayout.hd.visibility, View.Visibility.Visible);

            utTools.MockInCallService.emit("callstatechanged", activeCallA);
            yield 3000;
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            utTools.equal(moLayout.hd.visibility, View.Visibility.Visible);
        }());
    });

    afterEach((done) => {
        utTools.log("VoLTECall afterEach");

        utTools.asyncRun(done, function *() {
            utTools.log("VoLTECall afterEach run");
            yield 3000;
            const controler = uiInf.getControler();
            utTools.equal(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
        }());
    });

    // call a active -> call b incoming -> call b active -> call b disconnect -> call a disconnect
    it("twocalls", function(done) {
        utTools.log("VoLTECall twocalls in");
        utTools.asyncRun(done, function *() {
            utTools.log("VoLTECall twocalls run");
            utTools.MockInCallService.emit("callstatechanged", holdCallA);
            utTools.MockInCallService.emit("calladded", incomingCallB);
            yield 3000;
            const mtLayout = uiInf.getMTLayout();
            utTools.equal(uiInf.getTopLayout(), mtLayout);
            utTools.equal(mtLayout.hd.visibility, View.Visibility.Visible);

            utTools.MockInCallService.emit("callstatechanged", activeCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", hangupCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", hangupCallA);
            yield 3000;
        }());
    });

    // call a active -> call b dialing -> call b alerting -> call b active -> merge to conference -> disconnect
    it("conferencecall", function(done) {
        utTools.log("VoLTECall conferencecall in");
        utTools.asyncRun(done, function *() {
            utTools.log("VoLTECall conferencecall run");
            utTools.MockInCallService.emit("callstatechanged", holdCallA);
            utTools.MockInCallService.emit("calladded", dialingCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", alertingCallB);
            yield 3000;

            utTools.MockInCallService.emit("callstatechanged", activeCallB);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.mergeCall.enabled, true);
            utTools.equal(moLayout.call1Number.text, holdCallA._callNumber);

            moLayout.mergeCall.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("callstatechanged", hangupCallA);
            utTools.MockInCallService.emit("callstatechanged", activeMultiCallB);
            utTools.MockInCallService.emit("conferenceparticipantsstatechanged", activeMultiCallC);
            utTools.MockInCallService.emit("conferenceparticipantsstatechanged", activeMultiCallD);
            yield 3000;
            utTools.equal(moLayout.number.text, "");
            utTools.strEqual(moLayout.callname.text, "TEXT_CONFERENCE_CALL");
            utTools.equal(moLayout.mergeCall.visibility, View.Visibility.None);
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.Visible);

            utTools.MockInCallService.emit("callstatechanged", hangupMultiCallB);
            yield 3000;
        }());
    });

    // volte conference -> fallback -> disconnect
    it("fallback", function(done) {
        utTools.log("VoLTECall fallback in");
        utTools.asyncRun(done, function *() {
            // volte conference call
            utTools.log("VoLTECall fallback run");
            utTools.MockInCallService.emit("callstatechanged", holdCallA);
            utTools.MockInCallService.emit("calladded", dialingCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", alertingCallB);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activeCallB);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            moLayout.mergeCall.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("callstatechanged", hangupCallA);
            utTools.MockInCallService.emit("callstatechanged", activeMultiCallB);
            utTools.MockInCallService.emit("conferenceparticipantsstatechanged", activeMultiCallC);
            utTools.MockInCallService.emit("callstatechanged", activeMultiCallC);
            utTools.MockInCallService.emit("conferenceparticipantsstatechanged", activeMultiCallD);
            utTools.MockInCallService.emit("callstatechanged", activeMultiCallD);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.Visible);

            // conference list
            moLayout.meetingBtn.dispatchEvent("touchend", touchup);
            yield 3000;
            const confLayout = uiInf.getConfLayout();
            utTools.equal(uiInf.getTopLayout(), confLayout);
            const volteConfItem = confLayout.memberList.obtainView(0);
            utTools.notEqual(volteConfItem, null);
            utTools.equal(volteConfItem.private.visibility, View.Visibility.None);
            confLayout.backImage.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), moLayout);

            // fallback
            utTools.MockInCallService.emit("callstatechanged", fbHangupMultiCallB);
            utTools.MockInCallService.emit("callstatechanged", activeGsmMultiCallC);
            utTools.MockInCallService.emit("callstatechanged", activeGsmMultiCallD);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.meetingBtn.visibility, View.Visibility.Visible);

            // conference list
            moLayout.meetingBtn.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), confLayout);
            const gsmConfItem = confLayout.memberList.obtainView(0);
            utTools.notEqual(gsmConfItem, null);
            utTools.equal(gsmConfItem.private.visibility, View.Visibility.Visible);
            confLayout.backImage.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(uiInf.getTopLayout(), moLayout);

            // hangup
            utTools.MockInCallService.emit("callstatechanged", hangupGsmMultiCallC);
            utTools.MockInCallService.emit("callstatechanged", hangupGsmMultiCallD);
            yield 3000;
        }());
    });
});
