<CompositeView id="root" layout="{layout.mtGuestureFloatLayoutTwo}" background="{img(images/qms_call_bg_Incomingcall.png)}" width="{config.app_width}" height="{config.app_height}">
    <CompositeView id="mtFloat" layout="{layout.mtFloatLayoutTwo}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent">
        <CompositeView id="mtFloatOneDingding" layout="{layout.mtFloatLayoutOneDingding}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent">
            <CompositeView id="userInfo" width="{sdp(390)}" height="{sdp(96)}" layout="{layout.floatCallInfoLayoutTwo}">
                <CompositeView id="avatarInfo" width="{sdp(96)}" height="{sdp(96)}" layout="{layout.floatCallInfoLayoutTwoavatarInfo}">
                    <ImageView id="avatar" width="{sdp(96)}" height="{sdp(96)}"
                        src="{img(images/qms_call_ic_portrait_small.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                    <ImageView id="avatarChart" width="{sdp(26)}" height="{sdp(26)}"
                        src="{img(images/qms_call_btn_dingding_small.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                </CompositeView>
                <TextView id="callname" propertySetName="extend/hdt/FontTitle1" color="{theme.color.White_1}" width="{sdp(216)}" height="{sdp(40)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="number" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_3}" width="{sdp(96)}" height="{sdp(28)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_INCOMING_CALL}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="aiTransfer" fontSize="{sdp(18)}" color="{theme.color.Brand_1}" width="{sdp(136)}" height="{sdp(20)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_AI_TRANSFER}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
            </CompositeView>

            <CompositeView id="anwerhanup"  clipBound="false" layout="{layout.anwerhanupLayout}" height = "{sdp(88)}" width="{sdp(236)}">
                <ButtonBM id="hangUp" width="{sdp(88)}" height="{sdp(88)}" iconSrc="{img(images/qms_call_bt_hang_up_long.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="answer" width="{sdp(88)}" height="{sdp(88)}" iconSrc="{img(images/qms_call_btn_answer_01_normal.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
            </CompositeView>
            <ImageView id="minimize" width="{sdp(56)}" height="{sdp(56)}" src="{img(images/qms_call_ic_down_normal.png)}"
                scaleType="{enum.ImageView.ScaleType.Centerinside}" multiState="{config.button_icDownMultiState}"/>
        </CompositeView>
        <CompositeView id="mtFloatTwoDingding" layout="{layout.mtFloatLayoutTwoDingding}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent" visibility="{enum.View.Visibility.None}">
            <CompositeView id="userInfoTwo" width="{sdp(872)}" height="{sdp(48)}" layout="{layout.floatCallInfoLayoutTwoDingding}">
                <ImageView id="avatarIncoming" width="{sdp(48)}" height="{sdp(48)}"
                    src="{img(images/qms_call_ic_portrait_verysmall.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                <TextView id="callnameIncoming" propertySetName="extend/hdt/FontTitle2" color="{theme.color.White_2}" width="{sdp(100)}" height="{sdp(40)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="numberIncoming" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_3}" width="{sdp(50)}" height="{sdp(28)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_INCOMING_CALL}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <View id="individer" background="{theme.color.White_5}" width="{sdp(1)}" height="{sdp(48)}"/>
                <ImageView id="avatarTwo" width="{sdp(48)}" height="{sdp(48)}"
                    src="{img(images/qms_call_ic_portrait_verysmall.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                <TextView id="callnameTwo" propertySetName="extend/hdt/FontTitle2" color="{theme.color.White_2}" width="{sdp(100)}" height="{sdp(40)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="numberTwo" propertySetName="extend/hdt/FontBody4" color="{theme.color.White_3}" width="{sdp(50)}" height="{sdp(28)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
            </CompositeView>
            <CompositeView id="anwerhanupTwo"  clipBound="false" layout="{layout.anwerhanupLayoutTwo}" height = "{sdp(64)}" width="{sdp(710)}">
                <ButtonBM id="answerHangup" width="{sdp(210)}" height="{sdp(64)}" text="{string.TEXT_ANSWER}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="answerHold" width="{sdp(210)}" height="{sdp(64)}" text="{string.TEXT_ANSWER_HOLD_1}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="hangUpTwo" width="{sdp(210)}" height="{sdp(64)}" text="{string.BTN_HANG_UP}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
            </CompositeView>
            <ImageView id="minimizeTwo" width="{sdp(56)}" height="{sdp(56)}" src="{img(images/qms_call_ic_down_normal.png)}"
                scaleType="{enum.ImageView.ScaleType.Centerinside}" multiState="{config.button_icDownMultiState}"/>
        </CompositeView>
    </CompositeView>
    <CompositeView id="mtGestureGuide" height="{config.mt_float_guide_height}" background="#1C3861FF" opacity="0.9"
            layout="{layout.mtGestureGuideFloatLayout}" visibility="{enum.View.Visibility.None}">
        <ImageView id="twoFingered" src="{img(images/ic_gestures_hand1.png)}"/>
        <TextView id="guideText" fontSize="20sp" color="#FFFFFFFF" width="{dp(230)}" text="{string.TEXT_GUIDE_SMS_REJECT}"
                multiLine="false" maxLineCount="1" opacity="0.6" elideMode= "{enum.TextView.ElideMode.ElideLeft}"/>
    </CompositeView>
</CompositeView>
