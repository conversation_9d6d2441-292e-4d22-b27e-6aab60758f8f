/* 通话中，全屏窗口 */
"use strict";

import WindowCAF = require("yunos/ui/view/Window");
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import View = require("yunos/ui/view/View");
import TextView = require("yunos/ui/view/TextView");
import VoiceCommand = require("yunos/ui/voice/VoiceCommand");
import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
import CompositeView = require("yunos/ui/view/CompositeView");
import ImageView = require("yunos/ui/view/ImageView");
import TouchEvent = require("yunos/ui/event/TouchEvent");
import LayoutManager = require("yunos/ui/markup/LayoutManager");
import ScrollableView = require("yunos/ui/view/ScrollableView");
import AnimationGroup = require("yunos/ui/animation/AnimationGroup");
import PropertyAnimation = require("yunos/ui/animation/PropertyAnimation");
import AlertDialog = require("yunos/ui/widget/AlertDialog");

import CallAudioState = require("yunos/telecom/CallAudioState");
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import BaseLayout = require("./BaseLayout");
//import CallInfoView = require("./CallInfoView");
import CallInfoView = require("./CallInfoViewTwo");
import TrackerUtil = require("../utils/TrackerUtil");
const trackerUtil = TrackerUtil.getInstance();
import Utils = require("../utils/Utils");
import DialsConstants = require("../utils/DialsConstants");
import Log = require("../utils/Logs");
import RowLayout = require("yunos/ui/layout/RowLayout");
import ButtonBM = require("extend/hdt/control/ButtonBM");

const TAG = "IncallingFullWindow";

const MAX_CALL_ITEMS = 3; // maybe one active, one hold and one incoming call
const EVENT_INTERVAL = 1000;
const CALL_SCENEID = "35d088c37a10c9ff903cb4a691893aa1";
interface ICallAudioState {
    _muted?: boolean;
    _route?: number;
    _supportedRouteMask?: number;
}

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface ICallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: ICallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<ICallCell>;
    confInfo?: ICallCell;
    isLocalContact?: boolean;
    callType?: string;
    aiHold?: boolean;
    aiTransfer?: boolean;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IImageView {
    voiceEnabled?: boolean;
}

interface IKey {
    data?: string;
}

interface ILayout {
    removeLayoutParam: (param1: string, param2: string) => void;
}

interface IVoiceCommand {
    localTaskEnd: boolean;
    sceneId: string;
}

interface IWindowParams {
    x?: number;
    y?: number;
    width?: number;
    height?: number;
}

interface IMyWindow {
    specifiedMode: number;
}

class IncallingFullWindow extends BaseLayout {
    private window: WindowCAF;
    private visible: boolean;
    private root: CompositeView;
    private dialPanel: CompositeView;
    private hideFullWinBtn: ImageView;
    private callInfoCompositeView: CompositeView;
    private mtGestureGuide: CompositeView;
    private twoFingered: ImageView;
    private guideText: TextView;
    private callHandler: CompositeView;
    private keyboard: ImageView;
    private handsfree: ImageView;
    private phoneSpeaker: ImageView;
    private mute: ImageView;
    private callButton: CompositeView;
    private hangup: ImageView;
    private answer: ImageView;
    private switch: ImageView;
    private holdAnswer: ImageView;
    private merge: ImageView;
    private callInfoViewArray: Array<CallInfoView>;
    private buttonGroup: Array<ImageView>;
    private buttonMaxWidth: number;

    private keyboardPanel: CompositeView;
    private scroll: ScrollableView;
    private bigNumber: TextView;
    private key1: TextView;
    private key2: TextView;
    private key3: TextView;
    private key4: TextView;
    private key5: TextView;
    private key6: TextView;
    private key7: TextView;
    private key8: TextView;
    private key9: TextView;
    private key0: TextView;
    private keyStar: TextView;
    private keySharp: TextView;
    private keyboardLayout: CompositeView;
    private hidePanelButton: ImageView;
    private keyboardBackground: View;
    private callButtonInfo: CompositeView;
    private answerText: TextView;
    private holdText: TextView;
    private handsfreeText: TextView;
    private phoneSpeakerText: TextView;

    private showKeyboardLater: boolean;
    private showPanelAnim: AnimationGroup;
    private hidePanelAnim: AnimationGroup;
    private convertClickDelay: number;
    private standTop: number;
    private primaryCallInfoView: CallInfoView;
    private _callnameText: Array<string>;
    private info: CompositeView;
    private callInfoOne: CompositeView;
    private avatar: ImageView;
    private avatarDing: ImageView;
    private callname: TextView;
    private callState: TextView;
    private callInfoTwo: CompositeView;
    private avatarOne: ImageView;
    private avatarOneDing: ImageView;
    private callnameOne: TextView;
    private callStateOne: TextView;
    private avatarTwo: ImageView;
    private avatarTwoDing: ImageView;
    private callnameTwo: TextView;
    private callStateTwo: TextView;
    private callButtonPanel: CompositeView;
    private oldButtonType: number;
    private newButtonType: number;
    private keyboardHangUp: ImageView;

    static readonly CALL_BUTTON_TYPE = {
        CALLBUTTON_ONE: 0,
        CALLBUTTON_TWO: 1,
        CALLBUTTON_THREE_INCOMING: 2,
        CALLBUTTON_THREE_HOLD: 3
    };

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");
        super(controller, callService);

        this.visible = false;
        this.callInfoViewArray = [];
        this.buttonGroup = [];
        this.buttonMaxWidth = 0;
        this._callnameText = [];
        this.oldButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_ONE;
        this.newButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_ONE;

        this.setFullScreenFlag(false);
        this.createLayout();
        this.initVoiceControl();

        Log.d(TAG, "constructor end");
        return;
    }

    createLayout() {
        this._view = <CompositeView> LayoutManager.loadSync("CallLayout.xml");

        //this._view.left = Utils.isLandscape() ? 0 : screenInstance.getPixelBySDp(<number> Res.getConfig("app_left"));
        //this._view.top = Utils.isLandscape() ? 0 : screenInstance.getPixelBySDp(<number> Res.getConfig("app_top"));
        //this._view.width = Utils.isLandscape() ? Utils.getScreenWidth() : Utils.getScreenWidth() - screenInstance.getPixelBySDp(<number> Res.getConfig("app_left") * 2);
        //this._view.height = Utils.isLandscape() ? Utils.getScreenHeight() : Utils.getScreenHeight() - screenInstance.getPixelBySDp(<number> Res.getConfig("app_top")* 2); // <number> Res.getConfig("mo_float_window_height");
        this._view.clipBound = false;
        this._view.borderRadius = <number> Res.getConfig("fullview_borderRadius");

        this.window = <WindowCAF> WindowCAF.create(this.controller.mainPage, {
            left: Utils.isLandscape() ?  Utils.getMenuWidth() : 0,
            top: Utils.isLandscape() ?  Utils.getStatusBarHeight() : Utils.getPortscapeTopPos(),
            width: Utils.getScreenWidth(),
            height: Utils.getScreenHeight(),
            type: 2006,
            layoutFlags: 0x00010008,
            pageToken: "SYSTEM_SERVICE_TOKEN",
            orientation: WindowCAF.Orientation.FollowUnderWindow
        });
        this.window.title = "IncallingFullWindow";
        this.window.showWhenLocked = true; // show beyond keyguard
        (<IMyWindow><object> this.window).specifiedMode = 1; // exclusive Mode for voice and motion event
        this.window.background = "transparent";
        this.window.addChild(this._view);

        this.root = <CompositeView> this._view.findViewById("root");
        this.dialPanel = <CompositeView> this._view.findViewById("dialPanel");
        this.hideFullWinBtn = <ImageView> this._view.findViewById("back");
        this.callInfoCompositeView = <CompositeView> this._view.findViewById("callInfoRoot");
        this.mtGestureGuide = <CompositeView> this._view.findViewById("mtGestureGuide");
        this.twoFingered = <ImageView> this._view.findViewById("twoFingered");
        this.guideText = <TextView> this._view.findViewById("guideText");
        this.callHandler = <CompositeView> this._view.findViewById("callHandler");
        this.keyboard = <ImageView> this._view.findViewById("keyboard");
        this.handsfree = <ImageView> this._view.findViewById("handsfree");
        this.phoneSpeaker = <ImageView> this._view.findViewById("phoneSpeaker");
        this.mute = <ImageView> this._view.findViewById("mute");
        this.callButton = <CompositeView> this._view.findViewById("callButton");
        this.hangup = <ImageView> this._view.findViewById("hangUp");
        this.answer = <ImageView> this._view.findViewById("answer");
        this.holdAnswer = <ImageView> this._view.findViewById("holdAnswer");
        this.merge = <ImageView> this._view.findViewById("merge");
        this.switch = <ImageView> this._view.findViewById("switch");
        this.callButtonInfo = <CompositeView> this._view.findViewById("callButtonInfo");
        this.answerText = <TextView> this._view.findViewById("answerText");
        this.holdText = <TextView> this._view.findViewById("holdText");
        this.handsfreeText = <TextView> this._view.findViewById("handsfreeText");
        this.phoneSpeakerText = <TextView> this._view.findViewById("phoneSpeakerText");
        this.info = <CompositeView> this._view.findViewById("info");
        this.callButtonPanel = <CompositeView> this._view.findViewById("callButtonPanel");

        this.callinfoLayout();
        this.refreshCallInfoView(null, 0);
        this.keyboardPanelInit();
        this.addListener();
        if (this.showKeyboardLater) {
            this.showKeyboardLater = false;
            this.onKeyboardClick();
        }
    }

    callinfoLayout() {
        this.callInfoOne = <CompositeView> this._view.findViewById("callInfoOne");
        this.avatar = <ImageView> this._view.findViewById("avatar");
        this.avatarDing = <ImageView> this._view.findViewById("avatarDing");
        this.callname = <TextView> this._view.findViewById("callname");
        this.callState = <TextView> this._view.findViewById("callState");
        this.callInfoTwo = <CompositeView> this._view.findViewById("callInfoTwo");
        this.avatarOne = <ImageView> this._view.findViewById("avatarOne");
        this.avatarOneDing = <ImageView> this._view.findViewById("avatarOneDing");
        this.callnameOne = <TextView> this._view.findViewById("callnameOne");
        this.callStateOne = <TextView> this._view.findViewById("callStateOne");
        this.avatarTwo = <ImageView> this._view.findViewById("avatarTwo");
        this.avatarTwoDing = <ImageView> this._view.findViewById("avatarTwoDing");
        this.callnameTwo = <TextView> this._view.findViewById("callnameTwo");
        this.callStateTwo = <TextView> this._view.findViewById("callStateTwo");
    }

    initVoiceControl() {
        (<IImageView><object> this.hangup).voiceEnabled = true;
        this.hangup.voiceSelectMode = View.VoiceSelectMode.Custom;
        let hangupCommand = new VoiceCommand();
        hangupCommand.customCommands = <Array<string>> Res.getConfig("hangup_commands");
        hangupCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>hangupCommand).sceneId = CALL_SCENEID;
        Log.d(TAG, "initVoiceControl,hangupCommand.localTaskEnd = true");
        (<IVoiceCommand><object>hangupCommand).localTaskEnd = true;
        this.hangup.addVoiceCommand(hangupCommand);
        this.hangup.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice hangup");
            let hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.PAD,
                hupWay: TrackerUtil.HANGUP_WAY.VOICE};
            this.callService.hangup(hangupType, "");
            trackerUtil.commitEvent(TrackerUtil.TrackerEvents.CallResult, {
                type: "voice", result: "hangUp"
            });
            trackerUtil.commitEvent(TrackerUtil.TrackerEvents.ClickIncomingHanup, {
                type: "voice", pageType: "twoWayCallPage"
            });
        });

        (<IImageView><object> this.answer).voiceEnabled = true;
        this.answer.voiceSelectMode = View.VoiceSelectMode.Custom;
        let answerCommand = new VoiceCommand();
        answerCommand.customCommands = <Array<string>> Res.getConfig("answer_commands");
        answerCommand.recognitionMode = VoiceCommand.RecognitionMode.Both;
        (<IVoiceCommand><object>answerCommand).sceneId = CALL_SCENEID;
        Log.d(TAG, "initVoiceControl,answerCommand.localTaskEnd = true");
        (<IVoiceCommand><object>answerCommand).localTaskEnd = true;
        this.answer.addVoiceCommand(answerCommand);
        this.answer.on("voice", () => {
            Log.d(TAG, "initVoiceControl, onVoice answer");
            this.callService.answer(0, null, 0); // no need to set answer type as it not used in tracker
            trackerUtil.commitEvent(TrackerUtil.TrackerEvents.CallResult,{
                type: "voice", result: "answer"});
        });
    }

    keyboardPanelInit() {
        let view = LayoutManager.loadSync("keyboardPanel.xml");
        this.root.addChild(view);

        this.keyboardPanel = <CompositeView> view.findViewById("keyboardPanel");
        this.scroll = <ScrollableView> view.findViewById("scroll");
        this.bigNumber = <TextView> view.findViewById("bigNumber");
        this.key1 = <TextView> view.findViewById("key1");
        this.key2 = <TextView> view.findViewById("key2");
        this.key3 = <TextView> view.findViewById("key3");
        this.key4 = <TextView> view.findViewById("key4");
        this.key5 = <TextView> view.findViewById("key5");
        this.key6 = <TextView> view.findViewById("key6");
        this.key7 = <TextView> view.findViewById("key7");
        this.key8 = <TextView> view.findViewById("key8");
        this.key9 = <TextView> view.findViewById("key9");
        this.key0 = <TextView> view.findViewById("key0");
        this.keyStar = <TextView> view.findViewById("keyStar");
        this.keySharp = <TextView> view.findViewById("keySharp");
        this.keyboardLayout = <CompositeView> view.findViewById("keyboardLayout");
        this.hidePanelButton = <ImageView> view.findViewById("hide");
        this.keyboardBackground = <View> view.findViewById("keyboardBackground");
        this.keyboardHangUp = <ImageView> view.findViewById("keyboardHangUp");

        let numberKeys = [
            this.key0, this.key1, this.key2, this.key3, this.key4, this.key5,
            this.key6, this.key7, this.key8, this.key9
        ];
        for (let key of numberKeys) {
            key.text = key.text.slice(0,1);
        }

        //this.keyboardBackground.width = this.root.width;
        //this.keyboardBackground.height = screenInstance.getPixelBySDp(1); // it's a line

        this.buttonGroup.push(this.hidePanelButton);
    }

    addListener() {
        Log.d(TAG, "addListener called");
        this.keyboardLayout.addEventListener("touchstart", this.onKeyTouchDown.bind(this));
        this.keyboardLayout.addEventListener("touchend", this.onKeyTouchUp.bind(this));

        this.hangup.addEventListener("touchend", this.onHangupClick.bind(this));
        this.answer.addEventListener("touchend", this.onAnswerClick.bind(this));
        this.hidePanelButton.addEventListener("touchend", this.onKeyboardHideClick.bind(this));
        this.keyboard.addEventListener("touchend", this.onKeyboardClick.bind(this));
        this.handsfree.addEventListener("touchend", this.onHandsfreeClick.bind(this));
        this.phoneSpeaker.addEventListener("touchend", this.onHandsfreeClick.bind(this));
        this.mute.addEventListener("touchend", this.onMuteClick.bind(this));
        this.hideFullWinBtn.addEventListener("touchend", this.onHideFullWindowButtonClicked.bind(this));
        this.holdAnswer.addEventListener("touchend", this.onHoldAnswerClick.bind(this));
        this.merge.addEventListener("touchend", this.onMergeClick.bind(this));
        this.switch.addEventListener("touchend", this.onSwitchClick.bind(this));
        this.keyboardHangUp.addEventListener("touchend", this.onHangupClick.bind(this));

        this.bigNumber.on("textchange", this.layoutNumberText.bind(this));
    }

    layoutNumberText() {
        Log.d(TAG, "layoutNumberText");
        let w = this.bigNumber.contentWidth;
        if (w >= this.scroll.width) {
            this.bigNumber.width = w;
            this.bigNumber.align = TextView.Align.Right;
            this.scroll.scrollX = w - this.scroll.width;
        } else {
            this.bigNumber.width = this.scroll.width;
            this.bigNumber.align = TextView.Align.Center;
        }
    }

    changeButtonWidth() {
        for (let i = 0; i < this.buttonGroup.length; i++) {
            this.buttonGroup[i].width = this.buttonMaxWidth;
        }
    }

    onKeyTouchDown(e: TouchEvent) {
        let key: CompositeView = <CompositeView><object> e.target;
        let keyImage = <CompositeView> key.findViewById(key.id + "s");
        if (keyImage) {
            key = keyImage;
        }
        if (key.id === "keyboardLayout") {
            Log.d(TAG, "invalid key");
            return ;
        }
        key.background = Res.getImageSrc("images/qms_call_bg_key_pressed.png");
        Log.d(TAG, "key press onKeyTouchDown");
        this.bigNumber.text += (<TextView><object>key).text;

        return;
    }

    onKeyTouchUp(e: TouchEvent) {
        let key = e.target;
        let keyImage = key.findViewById(key.id + "s");
        if (keyImage) {
            key = keyImage;
        }
        if (key.id === "keyboardLayout") {
            Log.d(TAG, "invalid key");
            return ;
        }
        key.background = "transparent";
        this.callService.sendDtmf((<TextView><object>key).text);
    }

    setOneCallButton() {//一个挂断按键显示
        this.newButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_ONE;
        this.answer.visibility = View.Visibility.None;
        this.holdAnswer.visibility = View.Visibility.None;
        this.merge.visibility = View.Visibility.None;
        this.switch.visibility = View.Visibility.None;
        (<ButtonBM><object>this.hangup).text = "";
        (<RowLayout>this.callHandler.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_one_sapcing"));//声道，拨号盘，静音按键的间距
        (<RowLayout>this.info.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_one_sapcing"));//声道，拨号盘，静音按键提示信息的间距
        this.hangup.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_width_1"));//挂断按键的宽度
        this.callButton.height = this.hangup.height = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_height_1"));//挂断等按键父容器高度
        this.callButton.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callbutton_width1"));//挂断等按键父容器宽度
        this.callButtonPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callButtonPanel_width1"));//挂断以及提示信息父容器宽度
        this.info.width = this.callHandler.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callHandler_width1")); //静音以及提示信息的宽度
        this.dialPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_dialPanel_width1"));//静音等按键父容器的宽度
        if (this.hangup.width === this.hangup.height) { //是否显示按键的提示信息,单按键信息显示
            this.callButtonInfo.visibility = View.Visibility.Visible;
            this.answerText.visibility = View.Visibility.None;
            this.holdText.visibility = View.Visibility.None;
            this.callButtonInfo.width = this.callButton.width;
        } else {
            this.callButtonInfo.visibility = View.Visibility.None;
        }
        //调整边距
        this.dialPanel.layout.setLayoutParam("callHandler", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("dialPanel_margin_top_one"))});
        this.root.layout.setLayoutParam("callButtonPanel", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_margin_top_one"))});
    }

    setTwoCallButton() {
        this.newButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_TWO;
        this.holdAnswer.visibility = View.Visibility.None;
        this.merge.visibility = View.Visibility.None;
        this.switch.visibility = View.Visibility.None;
        this.hangup.width = this.answer.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_width_2"));
        this.callButton.height = this.hangup.height = this.answer.height = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_height_2"));
        this.callButton.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callbutton_width2"));
        this.callButtonPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callButtonPanel_width2"));
        this.info.width = this.callHandler.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callHandler_width2"));
        this.dialPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_dialPanel_width2"));
        this.answer.visibility = View.Visibility.Visible;
        if (this.hangup.width === this.hangup.height) {
            this.callButtonInfo.visibility = View.Visibility.Visible;
            this.answerText.text = Res.getString("TEXT_ANSWER");
            this.answerText.visibility = View.Visibility.Visible;
            this.holdText.visibility = View.Visibility.None;
            (<RowLayout>this.callButtonInfo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_two_spacing"));
            this.callButtonInfo.width = this.callButton.width;
        } else {
            (<ButtonBM><object>this.answer).text = Res.getString("TEXT_ANSWER");
            (<ButtonBM><object>this.hangup).text = Res.getString("BTN_HANG_UP");
            this.callButtonInfo.visibility = View.Visibility.None;
        }
        (<RowLayout>this.callHandler.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_two_sapcing"));
        (<RowLayout>this.info.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_two_sapcing"));
        (<RowLayout>this.callButton.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_two_spacing"));
        this.dialPanel.layout.setLayoutParam("callHandler", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("dialPanel_margin_top_two"))});
        this.root.layout.setLayoutParam("callButtonPanel", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_margin_top_two"))});
    }

    setThreeCallButtonIncoming() {
        this.newButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_THREE_INCOMING;
        this.merge.visibility = View.Visibility.None;
        this.switch.visibility = View.Visibility.None;
        (<ButtonBM><object>this.answer).text = "";
        this.answer.visibility = View.Visibility.Visible;
        this.holdAnswer.visibility = View.Visibility.Visible;
        this.hangup.width = this.answer.width = this.holdAnswer.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_width_3"));
        this.callButton.height = this.hangup.height = this.answer.height = this.holdAnswer.height = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_height_3"));
        this.callButton.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callbutton_width3"));
        this.callButtonPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callButtonPanel_width3"));
        this.info.width = this.callHandler.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callHandler_width3"));
        this.dialPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_dialPanel_width3"));
        (<ButtonBM><object>this.hangup).text = "";
        this.answerText.text = Res.getString("TEXT_ANSWER");
        this.holdText.text = Res.getString("TEXT_ANSWER_HOLD");
        this.answerText.visibility = View.Visibility.Visible;
        this.holdText.visibility = View.Visibility.Visible;
        this.callButtonInfo.visibility = View.Visibility.Visible;
        (<RowLayout>this.callHandler.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_three_sapcing"));
        (<RowLayout>this.info.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_three_sapcing"));
        (<RowLayout>this.callButton.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_three_spacing"));
        (<RowLayout>this.callButtonInfo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_three_spacing"));
        this.dialPanel.layout.setLayoutParam("callHandler", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("dialPanel_margin_top_three"))});
        this.root.layout.setLayoutParam("callButtonPanel", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_margin_top_three"))});
    }

    setThreeCallButtonHold() {
        this.newButtonType = IncallingFullWindow.CALL_BUTTON_TYPE.CALLBUTTON_THREE_HOLD;
        this.answer.visibility = View.Visibility.None;
        this.holdAnswer.visibility = View.Visibility.None;
        this.merge.visibility = View.Visibility.Visible;
        this.switch.visibility = View.Visibility.Visible;
        this.answerText.text = Res.getString("TEXT_MERGE");
        this.holdText.text = Res.getString("TEXT_SWITCH");
        this.answerText.visibility = View.Visibility.Visible;
        this.holdText.visibility = View.Visibility.Visible;
        this.callButtonInfo.visibility = View.Visibility.Visible;
        this.callButton.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callbutton_width3"));
        this.callButtonPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callButtonPanel_width3"));
        this.info.width = this.callHandler.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_callHandler_width3"));
        this.dialPanel.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_dialPanel_width3"));
        (<ButtonBM><object>this.hangup).text = "";
        this.hangup.width = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_width_3"));
        this.callButton.height = this.hangup.height = screenInstance.getPixelBySDp(<number> Res.getConfig("fullview_onecallbutton_height_3"));
        (<RowLayout>this.callHandler.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_three_sapcing"));
        (<RowLayout>this.info.layout).spacing =  screenInstance.getPixelBySDp(<number> Res.getConfig("callHandler_three_sapcing"));
        (<RowLayout>this.callButton.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_three_spacing"));
        (<RowLayout>this.callButtonInfo.layout).spacing = screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_three_spacing"));
        this.dialPanel.layout.setLayoutParam("callHandler", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("dialPanel_margin_top_three"))});
        this.root.layout.setLayoutParam("callButtonPanel", "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callButton_margin_top_three"))});
    }

    update(param: Array<ICallCell>, timecount: number, audioState: CallAudioState) {
        // this function can update call views
        Log.i(TAG, "IncallingFullWindow update called");
        if (!param || param.length === 0) {
            Log.d(TAG, "IncallingFullWindow param null, return directly");
            return;
        }

        if (audioState) {
            Log.v(TAG, "update audio:", audioState);
            if ((<ICallAudioState><object>audioState)._muted) {
                this.mute.src = Res.getImageSrc("images/qms_call_bt_mute_activated.png");
            } else {
                this.mute.src = Res.getImageSrc("images/qms_call_bt_mute_normal.png");
            }
            if ((<ICallAudioState><object>audioState)._route === CallService.AUDIOROUTE.SPEAKER) {
                this.handsfree.visibility = View.Visibility.Visible;
                this.handsfreeText.visibility = View.Visibility.Visible;
                this.phoneSpeaker.visibility = View.Visibility.None;
                this.phoneSpeakerText.visibility = View.Visibility.None;
            } else {
                this.handsfree.visibility = View.Visibility.None;
                this.handsfreeText.visibility = View.Visibility.None;
                this.phoneSpeaker.visibility = View.Visibility.Visible;
                this.phoneSpeakerText.visibility = View.Visibility.Visible;
            }
        }

        let callCell = param[0];
        let call: ICallCell = callCell.callinfo || callCell;
        Log.d(TAG, "update call.callid = ", call.callid, ", state = ", call.status, "callType=",call.callType);
        switch (call.status) {
            case CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE:
            case CallService.CALLSTATE.TELE_CALL_STATUS_HOLDING:
                this.mute.enabled = true;
                this.handsfree.enabled = true;
                this.keyboard.enabled = true;
                break;
            case CallService.CALLSTATE.TELE_CALL_STATUS_DIALING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_ALERTING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_INCOMING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_WAITING:
            case CallService.CALLSTATE.TELE_CALL_STATUS_DISCONNECTED:
                this.mute.enabled = false;
                this.handsfree.enabled = false;
                this.keyboard.enabled = false;
                break;
            default:
                this.mute.enabled = false;
                this.handsfree.enabled = false;
                this.keyboard.enabled = false;
                break;
        }

        Log.v(TAG, "update isFastRenderRunning = " + this.controller.mainPage.isFastRenderRunning());
        if (!this.controller.mainPage.isFastRenderRunning()) {
            this.refreshCallInfoView(param, timecount);
        }

        this.oldButtonType = this.newButtonType;
        if (param.length > 1) {
            let callCell2 = param[1];
            let call2: ICallCell = callCell2.callinfo || callCell2;
            if (call.callType && call2.callType && call.callType !== call2.callType) {//车信和蓝牙时，需要显示接听挂断和挂断两个按键
                Log.d(TAG, "carcall and btcall");
                this.setTwoCallButton();
            } else if (this.callService.isIncomingCall(call)) {//第2路来电时，需要显示接听挂断，接听保留，挂断 三个按键
                Log.d(TAG, "isIncomingCall");
                this.setThreeCallButtonIncoming();
                (<IImageView><object> this.hangup).voiceEnabled = true;
                if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
                    this.mtGestureGuide.visibility = View.Visibility.Visible;
                }
            } else if (param[0].status === CallService.CALLSTATE.TELE_CALL_STATUS_ALERTING
                || param[0].status === CallService.CALLSTATE.TELE_CALL_STATUS_DIALING) {//第二路主叫时，显示挂断一个按键
                Log.d(TAG, "isEnterOutgoing two");
                this.setOneCallButton();
            } else {//两路通话时，显示切换，合并，挂断三个按键
                Log.d(TAG, "normal two");
                this.setThreeCallButtonHold();
            }
            (<IImageView><object> this.hangup).voiceEnabled = false;
            if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
                this.mtGestureGuide.visibility = View.Visibility.None;
            }
        } else {//单路通话，显示挂断
            this.setOneCallButton();
            (<IImageView><object> this.hangup).voiceEnabled = false;
            if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
                this.mtGestureGuide.visibility = View.Visibility.None;
            }
        }
        if (this.oldButtonType !== this.newButtonType) {//如果键盘展开，需要隐藏键盘
            if (this.keyboardPanel &&
                    this.keyboardPanel.visibility === View.Visibility.Visible) {
                Log.i(TAG, "callinfo changed , hide keyboard panel");
                this.onKeyboardHideClick();
            }
        }

        if (!this.visible) {
            Log.d(TAG, "update show window");
            this.resetPosition();
            if (param.length === 1 && call.callType !== DialsConstants.Customization.CALL_TYPE_BT) {
                //唯一三方通话时，不显示大卡
                Log.d(TAG, "no need show with one chart");
            } else {
                this.window.show();
                this.visible = true;
            }
            this.setFullScreenFlag(true);
            this.controller.sendLink2MainPage();
            trackerUtil.enterPage(TrackerUtil.TrackerPages.InCallMainPage, null, {});
        } else {
            Log.d(TAG, "has shown already, nothing need to do");
            if (param.length === 1 && call.callType !== DialsConstants.Customization.CALL_TYPE_BT) {  //唯一三方通话时，不显示大卡
                Log.d(TAG, "no need show with one chart, hide fullwindow");
                this.window.hide();
                this.visible = false;
            }
        }
    }

    //刷新callInfo的显示，分为单路通话和两路通话页面
    refreshCallInfoViewNew(param: Array<ICallCell>, timecount: number) {
        if (!param || param.length === 0) {
            Log.e(TAG, "refreshCallInfoView, there is no call");
            return;
        }
        let fontCell = param[0];
        let fontCall: ICallCell = fontCell.callinfo || fontCell;
        if (param.length === 1) {//单路时，显示头像，名称
            Log.i(TAG, "refreshCallInfoViewNew one call");
            this.callInfoOne.visibility = View.Visibility.Visible;
            this.callInfoTwo.visibility = View.Visibility.None;
            if (!fontCell.callerInfo) {
                this.callname.text = fontCall.lineid;
                this.avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
            } else {
                let info = fontCell.callerInfo;
                this.callname.text = info.name || this._callnameText[0];
                if (info.photoUri && info.photoUri.startsWith("page://")) {
                    this.avatar.src = info.photoUri;
                } else if (info.photoUri && !fontCell.isLocalContact) {
                    this.avatar.src = "file://" + info.photoUri;
                } else {
                    this.avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
                }
            }
            this.callState.text = this.controller.callStatusToString(fontCell, timecount);
            if (fontCall.callType !== DialsConstants.Customization.CALL_TYPE_BT) {
                this.avatarDing.visibility = View.Visibility.Visible;
            } else {
                this.avatarDing.visibility = View.Visibility.None;
            }
        } else {//多路时，分别设置两路的头像和名称
            Log.i(TAG, "refreshCallInfoViewNew two calls");
            this.callInfoOne.visibility = View.Visibility.None;
            this.callInfoTwo.visibility = View.Visibility.Visible;
            if (!fontCell.callerInfo) {
                this.callnameOne.text = fontCall.lineid;
            } else {
                this.callnameOne.text = this._callnameText[0] || fontCell.callerInfo.name;
            }

            let backCell = param[1];
            let backCall: ICallCell = backCell.callinfo || backCell;
            if (!backCell.callerInfo) {
                this.callnameTwo.text = backCall.lineid;
            } else {
                this.callnameTwo.text = this._callnameText[1] || backCell.callerInfo.name;
            }
            this.callStateOne.text = this.controller.callStatusToString(fontCell, timecount);
            this.callStateTwo.text = this.controller.callStatusToString(backCell, timecount);

            let twoButtom = false;
            if (fontCall.callType !== DialsConstants.Customization.CALL_TYPE_BT) {
                this.avatarOneDing.visibility = View.Visibility.Visible;
                this.avatarTwoDing.visibility = View.Visibility.None;
            } else if (backCall.callType !== DialsConstants.Customization.CALL_TYPE_BT){
                this.avatarOneDing.visibility = View.Visibility.None;
                this.avatarTwoDing.visibility = View.Visibility.Visible;
            } else {
                this.avatarOneDing.visibility = View.Visibility.None;
                this.avatarTwoDing.visibility = View.Visibility.None;
            }
            this.controller.fetchCallName(backCall.lineid, null, (err, name, photo, userData) => {
                if (!err && name) {
                    this._callnameText[1] = name;
                } else {
                    Log.d(TAG, "fetchCallName null");
                    this._callnameText[1] = backCall.lineid;
                }
            });
        }
        this.controller.fetchCallName(fontCall.lineid, null, (err, name, photo, userData) => {
            if (!err && name) {
                this._callnameText[0] = name;
            } else {
                Log.d(TAG, "fetchCallName null");
                this._callnameText[0] = fontCall.lineid;
            }
        });
    }

    // 更新call state的显示，call state会显示“通话中”，“正在挂断”，通话时间等信息
    // 对于多方通话，会有多个CallInfoView在全屏窗口中显示
    refreshCallInfoView(param: Array<ICallCell>, timecount: number) {
        Log.i(TAG, "refreshCallInfoView total calls:", param ? param.length : 0);
        let frontCell = this.callService.getFrontCall();
        if ((!param || param.length === 0) && !frontCell) {
            Log.e(TAG, "refreshCallInfoView, there is no call");
            return;
        } else if (param && param.length > MAX_CALL_ITEMS) {
            Log.w(TAG, "refreshCallInfoView, WHY are there too many calls????");
        }
        this.refreshCallInfoViewNew(param, timecount);
        /*else if (param && param.length < this.callInfoViewArray.length) {
            Log.d(TAG, "refreshCallInfoView some call lost, remove from UI:", param.length, this.callInfoViewArray.length);
            for (let i = 0; i < this.callInfoViewArray.length; i++) {
                let found = false;
                for (let callCell of param) {
                    let call: ICallCell = callCell.callinfo || callCell;
                    if (this.callInfoViewArray[i].id === call.callid.toString()) {
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    this.callInfoCompositeView.removeChild(this.callInfoViewArray[i]);
                    this.destroyCallInfoItemView(this.callInfoViewArray[i]);
                    this.callInfoViewArray.splice(i, 1);
                }
            }
        }

        let callInfoItemWidth = param ? this.callInfoCompositeView.width / param.length :
            this.callInfoCompositeView.width;

        for (let index = 0; index <= MAX_CALL_ITEMS - 1; index++) {
            let callCell = param ? param[index] : null;
            if (index === 0 && !callCell && frontCell) {
                callCell = frontCell;
            }
            if (!callCell) {
                continue;
            }
            let call: ICallCell = callCell.callinfo || callCell;
            Log.d(TAG, "refreshCallInfoView: index=", index,
                " callid=", call.callid, " status=", call.status);
            Log.d(TAG, "this.callInfoViewArray=", this.callInfoViewArray);
            if (!this.callInfoViewArray[index]) {
                this.callInfoViewArray[index] = new CallInfoView();
                this.callInfoViewArray[index].addEventListener("touchend",
                    this.onConvertClick.bind(this, this.callInfoViewArray[index]));
            }

            Log.v(TAG, "refreshCallInfoView, number = " + Utils.desensitizeNumber(call.lineid));
            if (callCell.callerInfo) {
                Log.v(TAG, "refreshCallInfoView, contactInfo = ", callCell.callerInfo);
                let info = callCell.callerInfo;
                this.callInfoViewArray[index].callname = info.name || this._callnameText[index];
                if (info.photoBuffer) {
                    // this.callInfoViewArray[index].avatar = info.photoBuffer;
                    this.callInfoViewArray[index].avatar = Res.getImageSrc("images/qms_call_icon_portrait.png");
                } else if (info.photoUri && info.photoUri.startsWith("page://")) {
                    this.callInfoViewArray[index].avatar = info.photoUri;
                } else if (info.photoUri && !callCell.isLocalContact) {
                    this.callInfoViewArray[index].avatar = "file://" + info.photoUri;
                } else {
                    this.callInfoViewArray[index].avatar = Res.getImageSrc("images/qms_call_icon_portrait.png");
                }
                Log.v(TAG, "refreshCallInfoView, avatar.src=", this.callInfoViewArray[index].avatar);
            } else {
                this.callInfoViewArray[index].callname = this._callnameText[index] || call.lineid;
                this.callInfoViewArray[index].avatar = Res.getImageSrc("images/qms_call_icon_portrait.png");
            }

            this.callInfoViewArray[index].callState =
                this.controller.callStatusToString(callCell, timecount);

            Log.d(TAG, "refreshCallInfoView, frontCell.callid =", frontCell.callid);
            Log.d(TAG, "refreshCallInfoView, callCell.callid =", callCell.callid);
            this.callInfoViewArray[index].setForegroundState(frontCell.callid === callCell.callid);

            this.callInfoViewArray[index].width = callInfoItemWidth;
            //this.callInfoViewArray[index].setCallnameWidth(callInfoItemWidth);

            if (callCell.hangStatus === CallService.HANGSTATUS.DISCONNECTED) {
                this.callInfoCompositeView.removeChild(this.callInfoViewArray[index]);
                this.destroyCallInfoItemView(this.callInfoViewArray[index]);
                this.callInfoViewArray.splice(index, 1);
            } else {
                if (!this.callInfoCompositeView.findViewById(call.callid.toString())) {
                    this.callInfoViewArray[index].id = call.callid.toString();
                    this.callInfoCompositeView.insertChild(this.callInfoViewArray[index], index);
                    if (index === 1) {
                        this.callInfoCompositeView.layout.setLayoutParam(1, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_margin_top_small"))});
                    }
                }

                this.callInfoViewArray[index].callCell = call;
            }
            if(call.lineid) {
                this.controller.fetchCallName(call.lineid, index,
                    (err: string, name: string, photo: string, idx: number) => {
                        if (!err && name) {
                            this._callnameText[idx] = name;
                            this.callInfoViewArray[idx] && (this.callInfoViewArray[idx].callname = name);
                        } else {
                            Log.d(TAG, "fetchCallName null");
                            this._callnameText[idx] = call.lineid;
                            this.callInfoViewArray[idx] && (this.callInfoViewArray[idx].callname = call.lineid);
                        }
                    }
                );
            }
        }*/
    }

    updateCallStateNew(callList: Array<ICallCell>, curtime: number) {
        Log.d(TAG, "updateCallState called, curtime=", curtime);
        let callCell = callList ? callList[0] : null;
        if (callCell) {
            if (callList.length === 1) {
                this.callState.text = this.controller.callStatusToString(callCell, curtime);
            } else {
                this.callStateOne.text = this.controller.callStatusToString(callCell, curtime);
                this.callStateTwo.text = this.controller.callStatusToString(callList[1], curtime);
            }
        }
    }

    updateCallState(callList: Array<ICallCell>, curtime: number) {
        Log.d(TAG, "updateCallState called, curtime=", curtime);
        this.updateCallStateNew(callList,curtime);
        /*
        let frontCell = this.callService.getFrontCall();
        for (let index = MAX_CALL_ITEMS - 1; index >= 0; index--) {
            let callCell = callList ? callList[index] : null;
            if (index === 0 && !callCell && frontCell) {
                callCell = frontCell;
            }
            if (!callCell) {
                continue;
            }
            if (this.callInfoViewArray[index]) {
                this.callInfoViewArray[index].callState =
                    this.controller.callStatusToString(callCell, curtime);
            }
            continue;
        }*/
    }

    onHangupClick() {
        Log.d(TAG, "onHangupClick called");
        let hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.PAD,
            hupWay: TrackerUtil.HANGUP_WAY.CLICK};
        this.callService.hangup(hangupType, "");
        trackerUtil.commitEvent(TrackerUtil.TrackerEvents.CallResult, {
            type:" byhand", result: "hangUp"
        });
        //Log.d(TAG,"TrackerUtil.TrackerEvents.CardShow on screen off");
        trackerUtil.commitEvent(TrackerUtil.TrackerEvents.ClickIncomingHanup, {
            type: "byhand", pageType: "twoWayCallPage"});
        if (Utils.isScrrenOff()){
            trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickCallCard, {
                type: "hangup"
            });
        }
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickReject, null);
    }

    onAnswerClick() {
        Log.d(TAG, "onAnswerClick called");
        let callList = this.callService.getExistCallList();
        if (callList.length > 1) {
            let hangupType = {hupScene: TrackerUtil.HANGUP_SCENE.PAD,
                hupWay: TrackerUtil.HANGUP_WAY.CLICK};
            this.callService.hangupSpecial(callList[1],hangupType);
        }
        this.callService.answer(TrackerUtil.ANSWER_TYPE.PAD, null, 0);
        trackerUtil.commitEvent(TrackerUtil.TrackerEvents.CallResult, {
            type: "byhand", result: "answer"});

        if (Utils.isScrrenOff()){
            trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickCallCard, {
                type: "answer"
            });
        }
    }

    onHoldAnswerClick() {
        Log.d(TAG, "onHoldAnswerClick called");
        this.callService.answer(TrackerUtil.ANSWER_TYPE.PAD, null, 0);
        trackerUtil.commitEvent(TrackerUtil.TrackerEvents.CallResult, {
            type: "byhand", result: "answer"});

        if (Utils.isScrrenOff()){
            trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickCallCard, {
                type: "answer"
            });
        }
    }

    onMergeClick() {
        Log.d(TAG, "onMergeClick called");
        this.callService.conference();
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickMergeTalk, null);
    }

    onSwitchClick() {
        Log.d(TAG, "onSwitchClick called");
        this.callService.hold();
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickSwitchTalk, null);
    }

    onHandsfreeClick() {
        Log.d(TAG, "onHandsfreeClick called");
        this.handsfree.src = Res.getImageSrc("images/qms_call_bt_speaker_car_activation.png");
        this.phoneSpeaker.src = Res.getImageSrc("images/qms_call_bt_speaker_phone_activation.png");
        this.callService.changeSpeakerphoneState();
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickAudioSwitch, null);
    }

    onMuteClick() {
        Log.d(TAG, "onMuteClick called");
        this.callService.changeMicrophoneMuteState();
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickMute, null);
    }

    onKeyboardClick() {
        Log.d(TAG, "onKeyboardClick enter");
        if (this.keyboardPanel &&
                this.keyboardPanel.visibility === View.Visibility.Visible) {
            Log.i(TAG, "onKeyboardClick has shown, return!!");
            return;
        }

        this.callInfoCompositeView.visibility = View.Visibility.None;
        this.callButtonPanel.visibility = View.Visibility.None;
        this.dialPanel.visibility = View.Visibility.None;
        this.keyboardPanel.visibility = View.Visibility.Visible
        /*if (!this.showPanelAnim) {
            this.initKeyboardShowanim();
        }
        if (this.showPanelAnim) {
            this.callInfoCompositeView.visibility = View.Visibility.None;
            this.showPanelAnim.start();
        } else {
            Log.w(TAG, "onKeyboardClick showPanelAnim is null, return!!");
        }*/
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickDial, null);

        return;
    }

    showKeyboard() {
        if (this.controller.isFullScreen === true) {
            this.onKeyboardClick();
        } else {
            Log.d(TAG, "showKeyboard, the keyboard panel not inited and wait to show it later");
            this.showKeyboardLater = true;
        }
    }

    onConvertClick(infoView: CallInfoView) {
        if (this.convertClickDelay && Date.now() - this.convertClickDelay < EVENT_INTERVAL) {
            Log.d(TAG, "onConvertClick too fast, ignore!!");
            return;
        }
        this.convertClickDelay = Date.now();

        if (!infoView.callCell) {
            Log.e(TAG, "onConvertClick, there is no callCell and this should not occurs");
            return;
        }

        if (this.callService.getCallLines() !== 2) {
            Log.d(TAG, "onConvertClick, just switch status when there is two calls");
            return;
        }

        if (infoView.callCell.status !== CallService.CALLSTATE.TELE_CALL_STATUS_HOLDING) {
            Log.d(TAG, "onConvertClick, just hold status supported: " + this.callService.transStatusToString(infoView.callCell.status));
            return;
        }

        let frontCall: ICallCell = this.callService.getFrontCall();
        if (frontCall.status !== CallService.CALLSTATE.TELE_CALL_STATUS_ACTIVE) {
            Log.d(TAG, "onConvertClick, just switch status when other call is active");
            return;
        }

        Log.d(TAG, "onConvertClick, will call hold");
        this.callService.hold();
    }

    onHideFullWindowButtonClicked() {
        Log.d(TAG, "onHideFullWindowButtonClicked");
        this.controller.toggleFloatFullWindow(0);
        trackerUtil.clickCtrl(TrackerUtil.TrackerCtrls.ClickShrink, null);
        // this.controller.hideFullWindow();
    }

    initKeyboardShowanim() {
        Log.d(TAG, "initKeyboardShowanim");
        // this.dialPanel.visibility = View.Visibility.None;
        // this.keyboardPanel.visibility = View.Visibility.Visible;
        var standTop = this.standTop = this.dialPanel.top; // 62 * 2;
        let self = this;
        function initAnim(showView: View, hideView: View) {
            if (!showView || !hideView) {
                Log.w(TAG, "initKeyboardShowanim initAnim failed, return!!");
                return null;
            }
            var anims = new AnimationGroup();
            var anim1 = new PropertyAnimation(showView);
            var anim2 = new PropertyAnimation(hideView);

            anim1.duration = anim2.duration = 200;

            anim1.from = {top: standTop + 100, opacity: 0, visibility: View.Visibility.Visible};
            anim1.to = {top: standTop, opacity: 1, visibility: View.Visibility.Visible};
            anim2.from = {top: standTop, opacity: 1};
            anim2.to = {top: standTop + 100, opacity: 0};

            anims.add(anim1);
            anims.add(anim2);

            anims.addEventListener("complete", function() {
                Log.v(TAG, "anims complete");
                hideView.visibility = View.Visibility.None;
                showView.visibility = View.Visibility.Visible;
                /*let keyGroup =
                    [self.key1, self.key2, self.key3, self.key4, self.key5, self.key6,
                        self.key7, self.key8, self.key9, self.keyStar, self.key0, self.keySharp];
                for (let key of keyGroup) {
                    Log.v(TAG, "update number key width");
                    key.width = self.keyboardLayout.width / 3;
                    key.height = self.keyboardLayout.height / 4;
                }*/
            });

            return anims;
        }

        (<ILayout><object> this.root.layout).removeLayoutParam("dialPanel", "align");
        (<ILayout><object> this.root.layout).removeLayoutParam("dialPanel", "margin");
        (<ILayout><object> this.root.layout).removeLayoutParam("keyboardPanel", "align");
        (<ILayout><object> this.root.layout).removeLayoutParam("keyboardPanel", "margin");

        this.showPanelAnim = initAnim(this.keyboardPanel, this.dialPanel);
        this.hidePanelAnim = initAnim(this.dialPanel, this.keyboardPanel);
    }

    onKeyboardHideClick() {
        Log.d(TAG, "onKeyboardHideClick enter");

        this.callInfoCompositeView.visibility = View.Visibility.Visible;
        this.callButtonPanel.visibility = View.Visibility.Visible;
        this.dialPanel.visibility = View.Visibility.Visible;
        this.keyboardPanel.visibility = View.Visibility.None

        /*if (this.hidePanelAnim) {
            this.hidePanelAnim.start();
        } else {
            Log.w(TAG, "onKeyboardHideClick hidePanelAnim is null, return!!");
        }*/

        return;
    }

    resetBigNumber() {
        if (this.bigNumber) {
            this.bigNumber.text = "";
            if (this.scroll) {
                this.scroll.scrollX = 0;
                this.layoutNumberText();
            }
        }
    }

    destroyCallInfoItemView(item: CallInfoView) {
        if (!item) {
            return;
        }
        item.removeEventListener("touchend", this.onConvertClick, true);
        item.destroy(true);
        item = null;
    }

    resetUIContent() {
        Log.d(TAG, "resetUIContent");
        //this.callInfoCompositeView.removeAllChildren();
        for (let item of this.callInfoViewArray) {
            this.destroyCallInfoItemView(item);
        }
        this.callInfoViewArray = [];
        this._callnameText = [];

        if (this.primaryCallInfoView) {
            this.primaryCallInfoView.resetUIContent();
        }
        if (this.handsfree) {
            this.handsfree.src = Res.getImageSrc("images/qms_call_bt_speaker_car.png");
        }
        if (this.phoneSpeaker) {
            this.phoneSpeaker.src = Res.getImageSrc("images/qms_call_bt_speaker_phone_normal.png");
        }
        if (this.mute) {
            this.mute.src = Res.getImageSrc("images/qms_call_bt_mute_normal.png");
        }
        this.resetBigNumber();
    }

    hide() {
        Log.d(TAG, "IncallingFullWindow hide called, this.visible=", this.visible);
        if (this.visible) {
            this.window.hide();
            Log.d(TAG, "IncallingFullWindow full window already hidden, window visibility=",
                this.window.visibility);
            if (this.keyboardPanel &&
                    this.keyboardPanel.visibility === View.Visibility.Visible) {
                Log.i(TAG, "IncallingFullWindow hide, hide keyboard panel");
                this.onKeyboardHideClick();
            }
            trackerUtil.leavePage(TrackerUtil.TrackerPages.CallPage, {from: "bluetooth"});
        }
        this.visible = false;
        this.setFullScreenFlag(false);
        this.controller.setThirdPartOutgoingCall(false);
        this.callInfoOne.visibility = View.Visibility.None;
        this.callInfoTwo.visibility = View.Visibility.None;
    }

    resetPosition(){
        Log.d(TAG, "resetPosition ");
        this.window.top = Utils.isLandscape() ?  Utils.getStatusBarHeight() : Utils.getPortscapeTopPos();
    }

    show() {
        Log.d(TAG, "IncallingFullWindow show called, this.visible=", this.visible);
        if (!this.visible) {
            this.resetPosition();
            this.window.show();
            this.visible = true;
            this.setFullScreenFlag(true);
            this.controller.sendLink2MainPage();
            trackerUtil.enterPage(TrackerUtil.TrackerPages.CallPage, null, {});
        }
    }

    setFullScreenFlag(isFull: boolean) {
        Log.d(TAG, "setFullScreenFlag set full screen flag to", isFull);
        this.controller.isFullScreen = isFull;
    }
}

export = IncallingFullWindow;
