<CompositeView id="root" layout="{layout.aiHoldFloatLayout}" background="{img(images/qms_call_bg_conversation_popup.png)}"
    width="{config.mo_float_window_width_sdp_two}" height="{config.mo_float_window_height_sdp_two}" >
    <ImageView id="icon" width="{sdp(30)}" height="{sdp(30)}" scaleType="{enum.ImageView.ScaleType.Center}"  src="{img(images/qms_call_ic_conversation.png)}"/>
    <TextView id="callname" fontSize="{sdp(24)}" color="{theme.color.White_1}" height="{sdp(24)}"
        width="{config.mo_float_text_width_two}" align = "{enum.TextView.Align.Left}" multiLine="false" maxLineCount="1" />
    <TextView id="callState" align = "{enum.TextView.Align.Left}" width="{sdp(50)}"
        height="{sdp(20)}" fontSize="{sdp(20)}" color="{theme.color.White_3}"/>
    <TextView id="aiTransfer" fontSize="{sdp(20)}" color="{theme.color.Brand_1}" width="{sdp(136)}" height="{sdp(20)}"
            align = "{enum.TextView.Align.Left}" text="{string.TEXT_AI_TRANSFER}"
            multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
</CompositeView>
