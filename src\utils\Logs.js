"use strict";
const slice = Array.prototype.slice;
const TAG = "[CallDisplayer]_";
const LOG_LEVEL = {
    VERBOSE: 1,
    DEBUG: 2,
    INFO: 3,
    WARNING: 4,
    ERROR: 5
};
var gLogLevel = LOG_LEVEL.VERBOSE;
class Log {
    static v(m, ...msg) {
    }
    static d(m, ...msg) {
    }
    static i(m, ...msg) {
    }
    static w(m, ...string) {
    }
    static e(m, ...string) {
    }
    static setLevel(level) {
        if (level < LOG_LEVEL.VERBOSE) {
            gLogLevel = LOG_LEVEL.VERBOSE;
        }
        else if (level > LOG_LEVEL.ERROR) {
            gLogLevel = LOG_LEVEL.ERROR;
        }
        else {
            gLogLevel = level;
        }
    }
    static getLevel() {
        return gLogLevel;
    }
    static insertTag(args) {
        let newArgs = slice.call(args, 0);
        newArgs.unshift(TAG);
        return newArgs;
    }
}
module.exports = Log;
