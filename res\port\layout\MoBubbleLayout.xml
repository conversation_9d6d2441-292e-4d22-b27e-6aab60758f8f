<CompositeView id="root" layout="{layout.moFloatLayoutTwo}" background="{img(images/qms_call_bg_conversation_popup.png)}"
    width="{config.mo_float_window_width_sdp_two}" height="{config.mo_float_window_height_sdp_two}" >
    <ButtonBM id="smallHangUp" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_hang_up_04_normal.png)}"
        buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
    <ImageView id="icon" width="{sdp(36)}" height="{sdp(36)}" scaleType="{enum.ImageView.ScaleType.Center}"  src="{img(images/qms_call_ic_conversation.png)}"/>
    <ImageView id="iconCar" width="{sdp(48)}" height="{sdp(48)}" scaleType="{enum.ImageView.ScaleType.Center}"  src="{img(images/qms_call_btn_dingding_medium.png)}"
        visibility="{enum.View.Visibility.None}"/>
    <CompositeView id="info" layout="{layout.moFloatLayoutTwoInfo}" width="{config.mo_float_text_width_two}" height="{sdp(72)}" >
        <TextView id="callState" align = "{enum.TextView.Align.Left}" width="{config.mo_float_text_width_two}"
            height="{config.mo_float_state_height_two}" propertySetName="extend/hdt/Caption2" color="{theme.color.White_3}"/>
        <TextView id="callname" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_1}" height="{config.mo_float_text_height_two}"
            width="{config.mo_float_text_width_two}" align = "{enum.TextView.Align.Left}" multiLine="false" maxLineCount="1" />
    </CompositeView>
</CompositeView>
