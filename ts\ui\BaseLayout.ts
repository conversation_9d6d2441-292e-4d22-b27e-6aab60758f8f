"use strict";

import CompositeView = require("yunos/ui/view/CompositeView");
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import ViewController = require("./ViewController");
import CallService = require("../services/CallService");
import DialsConstants = require("../utils/DialsConstants");
import Log = require("../utils/Logs");
const TAG = "BaseLayout";

interface IView {
    enableBehindWindowsBlur: boolean;
}

interface IStackInfo {
    status?: number;
}

class BaseLayout {
    readonly OUTSTACK = 3;
    public stackinfo: IStackInfo = {status: this.OUTSTACK};
    public controller: ViewController;
    public callService: CallService;
    public _view: CompositeView = null;

    constructor(controller: ViewController, callService: CallService) {
        Log.d(TAG, "constructor called");
        this.controller = controller;
        this.callService = callService;

        if (this._view && DialsConstants.Customization.ENABLE_BLUR) {
            (<IView><object> this._view).enableBehindWindowsBlur = true;
            this._view.background = <string>Res.getConfig("color_blur_cover");
        }
    }

}

export = BaseLayout;
