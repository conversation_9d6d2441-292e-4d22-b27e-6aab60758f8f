/* 通话中，全屏界面中的头像、联系人姓名、状态等item的view */
"use strict";

import Screen = require("yunos/device/Screen");
const screenInstance = Screen.getInstance();
import Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
import CompositeView = require("yunos/ui/view/CompositeView");
import ColumnLayout = require("yunos/ui/layout/ColumnLayout");
import TextView = require("yunos/ui/view/TextView");
import ImageView = require("yunos/ui/view/ImageView");
import Color = require("extend/hdt/preset/Color");
import Font = require("extend/hdt/preset/Font");

import Log = require("../utils/Logs");
const TAG = "CallInfoViewTwo";

interface ICallerInfo {
    _phoneNumber?: string;
    _number?: string;
    number?: string;
    _name?: string;
    name?: string;
    _photoBuffer?: Buffer;
    photoBuffer?: Buffer;
    _photoUri?: string;
    photoUri?: string;
    type?: number;
    subtype?: number;
    markCount?: number;
    country?: string;
    city?: string;
    province?: string;
    area?: string;
}

interface IMyCallCell {
    idx?: number;
    subid?: number; // slot id actually?
    lineid?: string; // phone number?
    callid?: number;
    status?: number;
    name?: string;
    multiparty?: number;
    emergency?: boolean;
    connectTime?: number;
    disconnectReason?: number;
    capability?: number;
    phoneType?: number;
    callinfo?: IMyCallCell;
    localstatus?: number;
    isIncoming?: boolean;
    wasConnected?: boolean;
    startCount?: number;
    inConf?: boolean;
    hangType?: IHangupType;
    hangStatus?: number;
    activeHangUp?: boolean;
    answerType?: number;
    operatorInfo?: IOperatorInfo;
    isVoLTECall?: boolean;
    callerInfo?: ICallerInfo;
    localInfo?: ILocalInfo;
    numberInfo?: INumberInfo;
    isStrangeNumber?: boolean;
    isCDMA?: boolean;
    originCallId?: number;
    isVoLTE?: boolean;
    conferencelist?: Array<IMyCallCell>;
    confInfo?: IMyCallCell;
}

interface ILocalInfo {
    uninited?: boolean;
    name?: string;
}

interface INumberInfo {
    name?: string;
}

interface IHangupType {
    hupScene?: number;
    hupWay?: number;
}

interface IOperatorInfo {
    subid?: number;
    operatorName?: string;
}

class CallInfoViewTwo extends CompositeView {
    private _avatar: ImageView;
    private _callname: TextView;
    private _callState: TextView;
    public callCell: IMyCallCell;

    constructor() {
        super();
        Log.d(TAG, "constructor");

        this._avatar = new ImageView();
        this._avatar.id = "_avatar";
        this._avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
        this._avatar.width = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
        this._avatar.height = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
        this._avatar.borderRadius = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width") / 2);
        this._avatar.scaleType = ImageView.ScaleType.Fitxy;

        this._callname = new TextView();
        this._callname.id = "_callname";
        this._callname.propertySetName = Font.Title3;
        this._callname.color = Color.White1
        this._callname.align = TextView.Align.Center;
        this._callname.multiLine = false;
        this._callname.maxLineCount = 1;
        this._callname.elideMode = TextView.ElideMode.ElideRight;
        this._callname.width = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
        this._callname.height = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callname_height"));

        this._callState = new TextView();
        this._callState.id = "_callState";
        this._callState.propertySetName = Font.Body2;
        this._callState.color = Color.White3
        this._callState.opacity = 0.6;
        this._callState.align = TextView.Align.Center;
        this._callState.width = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
        this._callState.height = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callState_height"));

        this.addChild(this._avatar);
        this.addChild(this._callname);
        this.addChild(this._callState);

        let layout = new ColumnLayout();
        layout.defaultLayoutParam = {align: ColumnLayout.Align.Center};
        layout.wrapContent = true;
        layout.setLayoutParam(0, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(1, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(1, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callname_margin_top"))});
        layout.setLayoutParam(2, "align", ColumnLayout.Align.Center);
        layout.setLayoutParam(2, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callstatus_margin_top"))});
        this.layout = layout;
    }

    set avatar(icon: string) {
        Log.v(TAG, "set avatar icon:", icon);
        if (this._avatar) {
            this._avatar.src = icon;
        } else {
            Log.d(TAG, "set avatar: not init...");
        }
    }

    get avatar(): string {
        return <string> this._avatar.src;
    }

    set callname(name: string) {
        Log.v(TAG, "set callname name:", name);
        if (this._callname) {
            this._callname.text = name;
        } else {
            Log.d(TAG, "set callname: not init...");
        }
    }

    get callname() {
        return this._callname.text;
    }

    set callState(state: string) {
        Log.v(TAG, "set callState state:", state);
        if (this._callState) {
            this._callState.text = state;
        } else {
            Log.d(TAG, "set callState: not init...");
        }
    }

    get callState() {
        return this._callState.text;
    }

    getWidth() {
        let callnameWidth = 0;
        let callStateWidth = 0;
        let avatarWidth = 0;
        if (this._callname && this._callname.text) {
            callnameWidth = this._callname.width;
        }

        if (this._callState && this._callState.text) {
            callStateWidth = this._callState.width;
        }

        if (this._avatar && this._avatar.src) {
            avatarWidth = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
        }

        Log.d(TAG, "getWidth:", avatarWidth, callnameWidth, callStateWidth);
        return Math.max(callnameWidth, callStateWidth, avatarWidth);
    }

    setCallnameWidth(width: number) {
        this._callname.width = width;
    }

    setForegroundState(isFrontCall: boolean) {
        if (isFrontCall) {
            if (this._avatar.src === Res.getImageSrc("images/qms_call_ic_portrait_medium.png")) {
                this._avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
            }
            this._avatar.width = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
            this._avatar.height = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width"));
            this.layout.setLayoutParam(1, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callname_margin_top"))});
            this._avatar.opacity = 1;
            this._callname.opacity = 1;
            this._callname.color = Color.White1
            this._callState.opacity = 1;
        } else {
            this._avatar.src = Res.getImageSrc("images/qms_call_ic_portrait_medium.png");
            this._avatar.width = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width_small"));
            this._avatar.height = screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_avatar_width_small"));
            this.layout.setLayoutParam(1, "margin", {top: screenInstance.getPixelBySDp(<number> Res.getConfig("callinfo_callname_margin_top_small"))});
            this._avatar.opacity = 0.4;
            this._callname.opacity = 0.4;
            this._callname.color = Color.White3
            this._callState.opacity = 0.4;
        }
    }

    resetUIContent() {
        if (this._avatar) {
            this._avatar.src = Res.getImageSrc("images/qms_call_icon_portrait.png");
        }
        if (this._callname) {
            this._callname.text = "";
        }
        if (this._callState) {
            this._callState.text = "";
        }
    }
}

export = CallInfoViewTwo;
