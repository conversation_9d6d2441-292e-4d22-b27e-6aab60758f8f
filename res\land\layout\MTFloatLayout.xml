<CompositeView id="root" layout="{layout.mtGuestureFloatLayout}" background="transparent">
    <CompositeView id="mtFloat" layout="{layout.mtFloatLayout}" height="{config.mt_float_window_height}"
            background="{config.mt_float_window_background_color}" opacity="0.9">
        <ImageView id="avatar" width="{dp(120)}" height="{dp(120)}" borderRadius="{dp(60)}"
            src="{img(images/qms_call_icon_portrait_toast.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
        <CompositeView id="userInfo" width="{dp(400)}" layout="{layout.floatCallInfoLayout}">
            <TextView id="callname" fontSize="28sp" color="#FFFFFF" width="{dp(400)}"
                    multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
            <TextView id="number" fontSize="20sp" color="#FFFFFF" width="{dp(400)}"
                    multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
        </CompositeView>
        <ImageView id="hangUp" src="{img(images/qms_call_icon_toast_hang_up_normal.png)}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"
            multiState="{config.button_multiState}"/>
        <ImageView id="answer" src="{img(images/qms_call_icon_toast_answer_normal.png)}"
            scaleType="{enum.ImageView.ScaleType.Fitxy}"
            multiState="{config.button_multiState}"/>
    </CompositeView>
    <CompositeView id="mtGestureGuide" height="{config.mt_float_guide_height}" background="#1C3861FF" opacity="0.9"
            layout="{layout.mtGestureGuideFloatLayout}" visibility="{enum.View.Visibility.None}">
        <ImageView id="twoFingered" src="{img(images/ic_gestures_hand1.png)}"/>
        <TextView id="guideText" fontSize="20sp" color="#FFFFFFFF" width="{dp(230)}" text="{string.TEXT_GUIDE_SMS_REJECT}"
                multiLine="false" maxLineCount="1" opacity="0.6" elideMode= "{enum.TextView.ElideMode.ElideLeft}"/>
    </CompositeView>
</CompositeView>
