"use strict";

class UiInterface {

    static getInstance() {
        if (!UiInterface._inst) {
            UiInterface._inst = new UiInterface();
        }

        return UiInterface._inst;
    }

    setUiRoot(root) {
        this._uiRoot = root;
    }

    getControler() {
        return this._uiRoot.callController;
    }

    getService() {
        return this._uiRoot.callController.callService;
    }

    getMTLayout() {
        return this._uiRoot.callController.mtCallViewLayout;
    }

    getMTFloatLayout() {
        return this._uiRoot.callController.mtFloat;
    }

    getMOLayout() {
        return this._uiRoot.callController.moCallViewLayout;
    }

    getConfLayout() {
        return this._uiRoot.callController.confListLayout;
    }

    getTopLayout() {
        return this._uiRoot.callController.layoutStack.getTop();
    }

    setOperatorInfo(subid, operatorName) {
        this._uiRoot.callController.callService.onWorkerMsg({ data: { msg: "operatorInfo", subid: subid, operatorName: operatorName } });
    }
}

module.exports = UiInterface.getInstance();
