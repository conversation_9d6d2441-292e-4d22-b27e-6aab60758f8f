// jshint ignore: start
/* eslint-disable */
"use strict";
//import Power = require("yunos/device/Power");
const InCallService = require("yunos/telecom/InCallService");
const Call = require("yunos/telecom/Call");
const CallAudioState = require("yunos/telecom/CallAudioState");
const TelecomManager = require("yunos/telecom/TelecomManager");
const Page = require("yunos/page/Page");
const PageLink = require("yunos/page/PageLink");
const Resource = require("yunos/content/resource/Resource");
const Res = Resource.getInstance();
const Utils = require("../utils/Utils");
const TrackerUtil = require("../utils/TrackerUtil");
const DialsConstants = require("../utils/DialsConstants");
const Log = require("../utils/Logs");
const TAG = "CallService";
;
const TELE_CALL_STATUS_ACTIVE = Call.STATE_ACTIVE;
const TELE_CALL_STATUS_HOLDING = Call.STATE_HOLDING;
const TELE_CALL_STATUS_DIALING = Call.STATE_DIALING;
const TELE_CALL_STATUS_ALERTING = Call.STATE_ALERTING;
const TELE_CALL_STATUS_INCOMING = Call.STATE_INCOMING;
const TELE_CALL_STATUS_WAITING = Call.STATE_WAITING;
const TELE_CALL_STATUS_DISCONNECTING = Call.STATE_DISCONNECTING;
const TELE_CALL_STATUS_DISCONNECTED = Call.STATE_DISCONNECTED;
const CONF_CALLID = 9999;
const DIAL_CALLID = 9998;
const DISCONNECTED_SHOWING_TIME = 100;
const MAX_CDMA_CALL_WAITING_TIME = 20 * 1000; // 20 second
class CallService {
    constructor(controller) {
        this.previousCallStatus = -1;
        this.hangupClickTimer = null;
        Log.i(TAG, "constructor called");
        this.mViewController = controller;
        this.myPage = this.mViewController.mainPage;
        this.init();
    }
    init() {
        Log.i(TAG, "init called");
        this.CallList = new Map();
        this.incomingMap = new Map();
        this._IncomingTTSTimer = new Map();
        this.callNum = 0;
        this.status = CallService.CALL_SERVICE_STATUS.IDLE_CALL;
        this.screenon = true;
        this.inCallService = InCallService.getInstance();
        this.inCallService.on("calladded", this.onCallChanged.bind(this));
        this.inCallService.on("callstatechanged", this.onCallChanged.bind(this));
        this.inCallService.on("callaudiostatechanged", this.onCallAudioStateChanged.bind(this));
        this.inCallService.on("postdialwait", this.onPostDialWait.bind(this));
        this.inCallService.on("servicedeath", this.onServiceDeath.bind(this));
        this.inCallService.on("conferenceparticipantsstatechanged", this.onConferenceParticipantsStateChanged.bind(this));
        this.inCallService.on("callerinfoqueried", this.onCallerInfoQueried.bind(this));
        this.inCallService.on("serviceBirth", this.onServiceBirth.bind(this));
        this.inCallService.on("btStatusChange", this.onBTStatusChanged.bind(this));
        this.callCells = [];
        for (let i = 0; i <= CallService.CALL_SERVICE_STATUS.END_CALL; i++) {
            this.callCells[i] = [];
        }
        this.timecount = 0;
        this.audioState = null;
        this.CallChangAction = [];
        this.CallChangAction[TELE_CALL_STATUS_ACTIVE] = this.activeAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_HOLDING] = this.activeAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_DIALING] = this.dialAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_ALERTING] = this.dialAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_INCOMING] = this.incomingAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_WAITING] = this.incomingAction.bind(this);
        this.CallChangAction[TELE_CALL_STATUS_DISCONNECTED] = this.disconnedAction.bind(this);
        this.radioStatus = [];
        this.operatorInfo = [];
    }
    onServiceBirth() {
        Log.i(TAG, "onServiceBirth");
    }
    onBTStatusChanged(btConnected, hfpAddress, pbapAddress) {
        Log.i(TAG, "onBTStatusChanged    1111111111111111111  btConnected = " + btConnected);
        if (!btConnected && this.mViewController) {
            this.mViewController.clearFloatTemporaryVariable(true);
        }
    }
    onServiceDeath() {
        Log.i(TAG, "onServiceDeath");
        this.mViewController.handleServiceDeath();
    }
    changeCallId(oldId, newId) {
        if (this.CallList.has(oldId) && oldId !== newId) {
            this.CallList.set(newId, this.CallList.get(oldId));
            this.CallList.delete(oldId);
            Log.v(TAG, "after change call list", this.CallList);
        }
    }
    screenOn() {
        if (!DialsConstants.Customization.ENABLE_SCREEN_POWER_CTRL) {
            return;
        }
        this.lightWL.send();
        Log.v(TAG, "acquire screen wakelock");
    }
    screenOff() {
        if (!DialsConstants.Customization.ENABLE_SCREEN_POWER_CTRL) {
            return;
        }
        this.lightWL.revoke();
        Log.v(TAG, "release screen wakelock");
    }
    dialAction(callCell, call) {
        Log.v(TAG, "dialAction enter this.status =", this.status);
        if (this.callCells && this.callCells[CallService.CALL_SERVICE_STATUS.DIALING_CALL].length > 0 &&
            call.callid != this.callCells[CallService.CALL_SERVICE_STATUS.DIALING_CALL][0].callid) {
            Log.w(TAG, "there are a dialing call already!! the 1st:", call.callid, ", the 2nd:", this.callCells[CallService.CALL_SERVICE_STATUS.DIALING_CALL][0].callid, "\nCall stack:");
            // global.log.trace("CallDisplayer");
            return -1;
        }
        let ret = 0;
        switch (this.status) {
            default:
                if (!callCell) {
                    callCell = this.callCells[CallService.CALL_SERVICE_STATUS.DIALING_CALL][0];
                    if (!callCell) {
                        callCell = {
                            callid: call.callid,
                            subid: call.subid,
                            lineid: call.lineid,
                            callinfo: call
                        };
                    }
                    if (callCell.callid === DIAL_CALLID) {
                        this.changeCallId(DIAL_CALLID, call.callid);
                    }
                    else {
                        this.addCallCell2List(callCell);
                    }
                    callCell.callid = call.callid;
                }
                break;
        }
        return ret;
    }
    getIncomingCall() {
        let callCell = this.getFrontCall();
        if (!callCell) {
            Log.w(TAG, "getIncomingCall NOT found front call, return null!!");
            return null;
        }
        let call = callCell.callinfo || callCell;
        if (callCell.localstatus === CallService.CALL_SERVICE_STATUS.INCOMING_CALL || call.aiHold === true) {
            return callCell;
        }
        else {
            Log.v(TAG, "getIncomingCall can't get incoming call: " + callCell);
        }
        return null;
    }
    inConference() {
        return this.confCell;
    }
    isConferenceCell(callCell) {
        return callCell.callid === CONF_CALLID;
    }
    incomingAction(callCell, call) {
        Log.v(TAG, "incomingAction enter this.status =", this.status);
        let ret = 0;
        switch (this.status) {
            case CallService.CALL_SERVICE_STATUS.IDLE_CALL:
            case CallService.CALL_SERVICE_STATUS.ACTIVE_CALL:
            case CallService.CALL_SERVICE_STATUS.DIALING_CALL:
            case CallService.CALL_SERVICE_STATUS.HOLD_CALL:
            case CallService.CALL_SERVICE_STATUS.INCOMING_CALL:
                // maybe call status change from waiting to incoming
                if (!callCell) {
                    callCell = {
                        callid: call.callid,
                        subid: call.subid,
                        callinfo: call
                    };
                    this.mViewController.closeNotificationCenter();
                    this.addCallCell2List(callCell);
                    Log.i(TAG, "start IncomingTTSTimer");
                    this._IncomingTTSTimer.set(call.callid, setTimeout(() => {
                        Log.i(TAG, "IncomingTTSTimer timeout" + call.callid);
                        this.silenceRinger(); //先停止铃音，再播放tts
                        this.mViewController && this.mViewController.showIncomingTTS(callCell);
                        this._IncomingTTSTimer.delete(call.callid);
                    }, 30000));
                }
                if (call.status === TELE_CALL_STATUS_INCOMING ||
                    call.status === TELE_CALL_STATUS_WAITING) {
                    callCell.isIncoming = true;
                }
                break;
            default:
                ret = -1;
        }
        return ret;
    }
    updateCallState() {
        let timecount = this.timecount = process.uptime();
        Log.v(TAG, "updateCallState: timecount=" + this.timecount);
        if (this.nextTime - timecount > 0.1) {
            // Log.d(TAG, "updateCallState", "next", this.nextTime - timecount);
            this.timerId = setTimeout(this.updateCallState.bind(this), (this.nextTime - timecount) * 1000);
        }
        else {
            // Log.d(TAG, "updateCallState: ", this.nextTime, this.nextTime - timecount);
            if (this.callCells[CallService.CALL_SERVICE_STATUS.ACTIVE_CALL].length > 0
                || this.callCells[CallService.CALL_SERVICE_STATUS.HOLD_CALL].length > 0) {
                // 更新接通后的时间显示
                this.mViewController.updateCallState(this.getExistCallList(), timecount);
            }
            while (this.nextTime - timecount < 0.1) {
                this.nextTime += this.timeInterval;
            }
            let timer = this.nextTime - timecount;
            if (timer > 0.5) {
                timer -= 0.3;
            }
            this.timerId = setTimeout(this.updateCallState.bind(this), timer);
        }
    }
    startCalculateCallTime(callCell, call) {
        this.timeInterval = 1;
        if (!this.timerId) {
            Log.v(TAG, "startCalculateCallTime", "START");
            this.timerId = setTimeout(this.updateCallState.bind(this), this.timeInterval - 0.3);
            this.nextTime = process.uptime() + this.timeInterval;
        }
        let offset = 0;
        if (call && call.connectTime) {
            let date = new Date();
            offset = (date.getTime() - call.connectTime) / 1000;
            Log.d(TAG, "startCalculateCallTime, offset = " + offset);
        }
        this.timecount = callCell.startCount = process.uptime() - offset; // this.timecount;
    }
    setScreenOn(on) {
        this.screenon = on;
        const callCell = this.getFrontCall();
        if (!callCell || callCell.status !== TELE_CALL_STATUS_ACTIVE) {
            return;
        }
        if (on) {
            this.stopCalculateCallTime();
            this.updateCallState();
        }
        else {
            this.stopCalculateCallTime();
        }
    }
    stopCalculateCallTime() {
        Log.v(TAG, "stopCalculateCallTime called, this.timerId=", this.timerId);
        if (this.timerId) {
            clearTimeout(this.timerId);
            this.timerId = null;
        }
    }
    removeFromArray(arr, item, compare) {
        if (!arr) {
            Log.i(TAG, "removeFromArray is null, reutrn!!");
            return;
        }
        for (let i = 0; i < arr.length; i++) {
            if (compare(arr[i], item)) {
                arr.splice(i, 1);
                break;
            }
        }
    }
    handleConference(callCell, call) {
        Log.d(TAG, "handle conference call 1: ", call.lineid, ", callid =", call.callid);
        if (call.multiparty === 1 && call.status !== TELE_CALL_STATUS_DISCONNECTED) { // enter conference
            if (!this.inConference()) { // merge action
                Log.v(TAG, "handle conference call 2: " + call.lineid, ", callid =", call.callid);
                this.confCell = {
                    callerInfo: {
                        name: Res.getString(Utils.isCDMACall(call) ? "TEXT_CDMA_MULTI_CALL" : "TEXT_CONFERENCE_CALL"),
                        photoUri: Res.getImageSrc("images/qms_call_ic_portrait_group.png")
                    },
                    lineid: "",
                    status: call.status,
                    subid: call.subid,
                    callid: CONF_CALLID,
                    confInfo: call,
                    originCallId: call.callid,
                    isCDMA: Utils.isCDMACall(call),
                    isVoLTE: Utils.isVoLTECall(call),
                    startCount: callCell.startCount
                };
                if (!Utils.isVoLTECall(call)) {
                    this.confCell.conferencelist = [callCell];
                }
                else {
                    this.confCell.conferencelist = [];
                }
                this.removeCallCellFromArray(callCell, null);
                this.AddCallCell2Array(this.confCell, null);
                // this.removeFromArray(this.callCells, call,
                //      function(a, b) { return a.callid === b.callid; });
                // this.callCells.unshift(this.confCell);
            }
            else {
                Log.v(TAG, "handle conference call 3: ", call.lineid, "length =", this.confCell.conferencelist.length, "callid =", call.callid);
                this.confCell.conferencelist.unshift(callCell);
                if (this.confCell.startCount > callCell.startCount) {
                    this.confCell.startCount = callCell.startCount;
                }
                this.removeCallCellFromArray(callCell, null);
            }
            callCell.callinfo = call;
            callCell.inConf = true;
        }
        else if (call.status === TELE_CALL_STATUS_DISCONNECTED &&
            call.disconnectReason === Call.DisconnectReason.IMS_SRVCC_FALLBACK) { // volte conference call fallback
            Log.d(TAG, "handle conference call fallback");
            this.releaseCallCellItem(this.confCell.confInfo);
            this.confCell.isVoLTE = false;
            this.confCell.confInfo = this.confCell.conferencelist[0].callinfo;
            this.confCell.originCallId = this.confCell.conferencelist[0].callinfo.callid;
            for (let i = 0; i < this.confCell.conferencelist.length; i++) {
                this.confCell.conferencelist[i].callinfo.phoneType = Utils.PHONE_TYPE_GSM;
            }
        }
        else { //  exit conference
            Log.d(TAG, "handle conference call, will exit conference: ", call.lineid, ", callid =", call.callid);
            if (Utils.isVoLTECall(call) && this.confCell &&
                call.callid === this.confCell.originCallId) {
                for (let i = 0; i < this.confCell.conferencelist.length; i++) {
                    this.releaseCallCellItem(this.confCell.conferencelist[i].callinfo);
                }
                this.confCell.conferencelist = [];
            }
            else if (this.confCell) {
                this.removeFromArray(this.confCell.conferencelist, call, function (a, b) {
                    return a.callid === b.callid;
                });
            }
            else {
                Log.d(TAG, "handleConference this.confCell has released");
                return;
            }
            /*
            for (let i = 0; i < this.confCell.conferencelist.length; i++) {
              if (this.confCell.conferencelist[i].callid === call.callid) {
                this.confCell.conferencelist.splice(i, 1);
                break;
              }
            }*/
            if (this.confCell.conferencelist.length === 0) {
                Log.v(TAG, "handleConference:", "remove confCell");
                if (Utils.isVoLTECall(call)) {
                    this.showCallDuration(callCell, call);
                }
                this.removeCallCellFromArray(this.confCell, null);
                this.confCell = null;
            }
            callCell.inConf = false;
        }
        // this.dumpCallCells("handleConference");
    }
    activeAction(callCell, call) {
        Log.v(TAG, "activeAction enter callCell.callinfo.status =", (callCell && callCell.callinfo) ? callCell.callinfo.status : "no defined");
        let ret = 0;
        let type = "normal";
        if (callCell && callCell.callinfo &&
            (callCell.callinfo.status === TELE_CALL_STATUS_DIALING ||
                callCell.callinfo.status === TELE_CALL_STATUS_ALERTING ||
                callCell.callinfo.status === TELE_CALL_STATUS_WAITING ||
                callCell.callinfo.status === TELE_CALL_STATUS_INCOMING)) {
            this.startCalculateCallTime(callCell, call);
        }
        let newConfCall = 0;
        if (!callCell) {
            callCell = {
                callid: call.callid,
                subid: call.subid,
                callinfo: call
            };
            this.addCallCell2List(callCell);
            // if the 1st call status is Active, need startCalculateCallTime at here
            if ((call.status === TELE_CALL_STATUS_ACTIVE || call.status === TELE_CALL_STATUS_HOLDING) &&
                (call.multiparty === 0 || call.multiparty === 1)) {
                this.startCalculateCallTime(callCell, call);
            }
            newConfCall = callCell.callinfo.multiparty;
        }
        if (newConfCall || callCell.callinfo.multiparty !== call.multiparty) { // conference call
            type = "meeting";
            this.handleConference(callCell, call);
        }
        else if (this.getExistCallList().length > 1) {
            type = "twoWayCallPage";
        }
        Log.i(TAG, "call type: ", type);
        if (call.status === TELE_CALL_STATUS_ACTIVE || call.status === TELE_CALL_STATUS_HOLDING) {
            callCell.wasConnected = true;
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.NoEvent, { type: type, from: call.callType === "bt" ? "bluetooth" : "carchat" });
            if (this.previousCallStatus !== TELE_CALL_STATUS_INCOMING) {
                let timeLength = Utils.getTimeLength(this.timecount, callCell.startCount, false);
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.InCallResult, { type: call.callType === "bt" ? "bluetooth" : "carchat",
                    time: timeLength, result: "success" });
            }
            else {
                let currentTime = new Date().getTime();
                let old = this.incomingMap.get(call.callid);
                Log.d(TAG, "send incoming call WAIT_BEFORE_ANSWERING signal old：", old + " , currentTime: ", currentTime);
                if (old > 0 && (currentTime - old) / 1000 > 8) {
                    TrackerUtil.getInstance().sendIncomingCallSignal(TrackerUtil.SCENE_SIGNAL_NAME.WAIT_BEFORE_ANSWERING, { "delay_time": (currentTime - old) / 1000 });
                    this.incomingMap.set(call.callid + 1, currentTime);
                }
                ;
            }
        }
        return ret;
    }
    disconnedAction(callCell, call) {
        Log.v(TAG, "disconnedAction enter: ", call ? call.callid : "no defined");
        let noHangup = this.timecount === 0 ||
            !callCell || !callCell.startCount || callCell.startCount === 0;
        let timeLength = 0;
        if (!noHangup) {
            timeLength = Utils.getTimeLengthForInt(this.timecount, callCell.startCount);
        }
        Log.v(TAG, "disconnedAction timeLength =", timeLength);
        TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.RESULT, { type: call.callType === "bt" ? "bluetooth" : "carchat",
            time: timeLength, result: "success" });
        if (callCell && callCell.hangType && callCell.hangType.hupWay === TrackerUtil.HANGUP_WAY.VOICE) {
            Log.d(TAG, "disconnedAction, will show voice disconnect TTS");
            this.mViewController.showIncomingOverTTS(true);
        }
        if (this.previousCallStatus !== TELE_CALL_STATUS_INCOMING) {
            TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.InCallResult, { type: callCell.callType === "bt" ? "bluetooth" : "carchat", time: timeLength, result: "fail" });
        }
        else {
            let currentTime = new Date().getTime();
            let old = this.incomingMap.get(call.callid + 1);
            let oldIncoming = this.incomingMap.get(call.callid);
            Log.d(TAG, "send incoming call END_AFTER_ANSWER signal old：", old + " , currentTime: ", currentTime);
            if (old > 0 && (currentTime - old) / 1000 <= 10) {
                TrackerUtil.getInstance().sendIncomingCallSignal(TrackerUtil.SCENE_SIGNAL_NAME.END_AFTER_ANSWER, { "is_strange_call": call.name ? false : true, "answer_time": (currentTime - old) / 1000 });
                this.incomingMap.delete(call.callid + 1);
            }
            else if (old === 0 && oldIncoming > 0) {
                Log.d(TAG, "send incoming call NOT_ANSWER_CALL signal");
                TrackerUtil.getInstance().sendIncomingCallSignal(TrackerUtil.SCENE_SIGNAL_NAME.NOT_ANSWER_CALL, { "wait_time": (currentTime - oldIncoming) / 1000 });
            }
            this.incomingMap.delete(call.callid);
        }
        if (!this.isHangupOrAnswer && this.previousCallStatus === TELE_CALL_STATUS_INCOMING) {
            if (callCell.aiTransfer) {
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.ComAssIncomingResult, { time: timeLength });
            }
            else {
                TrackerUtil.getInstance().commitEvent(TrackerUtil.TrackerEvents.IncomingResult, {
                    type: "else", pageType: this.getCallLines() >= 2 ? "twoWayCallPage" : "incomingWindow", result: "else"
                });
            }
        }
        this.isHangupOrAnswer = false;
        return 0;
    }
    teleStatus2LocalStatus(call) {
        let statuschange = [];
        statuschange[TELE_CALL_STATUS_DIALING] = CallService.CALL_SERVICE_STATUS.DIALING_CALL;
        statuschange[TELE_CALL_STATUS_ALERTING] = CallService.CALL_SERVICE_STATUS.DIALING_CALL;
        statuschange[TELE_CALL_STATUS_INCOMING] = CallService.CALL_SERVICE_STATUS.INCOMING_CALL;
        statuschange[TELE_CALL_STATUS_WAITING] = CallService.CALL_SERVICE_STATUS.INCOMING_CALL;
        statuschange[TELE_CALL_STATUS_ACTIVE] = CallService.CALL_SERVICE_STATUS.ACTIVE_CALL;
        statuschange[TELE_CALL_STATUS_HOLDING] = CallService.CALL_SERVICE_STATUS.HOLD_CALL;
        return statuschange[call.status];
    }
    AddCallCell2Array(callCell, call) {
        Log.i(TAG, "AddCallCell2Array localstatus: ", callCell.localstatus, callCell.callid);
        let localstatus; // need use old localstatus, so don't change callcell.localstatus till end
        if (call) {
            localstatus = this.teleStatus2LocalStatus(call);
            callCell.status = call.status;
            callCell.callinfo = call;
        }
        else {
            localstatus = this.teleStatus2LocalStatus(callCell);
        }
        if (callCell.inConf) {
            callCell.localstatus = localstatus;
            callCell = this.confCell;
            if (!Utils.isVoLTECall(call) || call.callid === this.confCell.originCallId) {
                callCell.status = call.status;
            }
        }
        // remove callCell from old queue, if different
        if (callCell.localstatus && localstatus !== callCell.localstatus) {
            this.removeCallCellFromArray(callCell, null);
        }
        // insert to new queue, if not found
        let found = false;
        let callCellArr = this.callCells[localstatus];
        for (let i = 0; i < callCellArr.length; i++) {
            if (callCellArr[i].callid === callCell.callid) {
                found = true;
                break;
            }
        }
        if (found !== true) {
            callCell.localstatus = localstatus;
            this.callCells[localstatus].unshift(callCell);
        }
    }
    removeCallCellFromArray(callCell, callback) {
        let callCellArr = this.callCells[callCell.localstatus];
        Log.d(TAG, "removeCallCellFromArray enter: ", callCellArr ? "callCellArr not null" : "callCellArr is null!");
        if (!callback) {
            callback = function (a, b) {
                return a.callid === b.callid;
            };
        }
        this.removeFromArray(callCellArr, callCell, callback);
        // this.dumpCallCells("removeCallCellFromArray");
    }
    isDialingCall(status) {
        return status === TELE_CALL_STATUS_DIALING || status === TELE_CALL_STATUS_ALERTING;
    }
    getStatus() {
        let ret = CallService.CALL_SERVICE_STATUS.IDLE_CALL;
        let callCell = this.getFrontCall();
        if (callCell) {
            ret = callCell.localstatus;
        }
        return ret;
    }
    isIncomingCall(callInfo) {
        let status = this.getCallCellStatus(callInfo);
        Log.d(TAG, "isIncomingCall status=", this.transStatusToString(status));
        return status === TELE_CALL_STATUS_INCOMING || status === TELE_CALL_STATUS_WAITING;
    }
    isLeaveIncoming(oldCallCell, newCall) {
        return oldCallCell && oldCallCell.status !== newCall.status &&
            (oldCallCell.status === TELE_CALL_STATUS_INCOMING ||
                oldCallCell.status === TELE_CALL_STATUS_WAITING);
    }
    isEnterOutgoing(oldCallCell, newCall) {
        return newCall.status === TELE_CALL_STATUS_DIALING ||
            newCall.status === TELE_CALL_STATUS_ALERTING && !oldCallCell;
    }
    isIncomingStatus() {
        return this.getStatus() === CallService.CALL_SERVICE_STATUS.INCOMING_CALL;
    }
    convertTelecomCall(telecomCall) {
        if (!telecomCall) {
            Log.e(TAG, "convertTelecomCall, wrong parameter");
            return null;
        }
        Log.v(TAG, "convertTelecomCall: telecomCall=", JSON.stringify(telecomCall));
        let uiCall = {
            subid: telecomCall._subId, // NOTICE: it's slot id actually!!
            callid: telecomCall._callId,
            status: telecomCall._state,
            name: telecomCall._displayName,
            lineid: telecomCall._callNumber,
            multiparty: telecomCall._multiParty,
            emergency: telecomCall._emergencyCall,
            connectTime: telecomCall._details ? telecomCall._details.connectTime : 0,
            disconnectReason: telecomCall._disconnectCause,
            capability: telecomCall._capability,
            phoneType: telecomCall._phoneType,
            callType: telecomCall._callType,
            aiHold: telecomCall._aiHold,
            aiTransfer: telecomCall._aiTransfer,
        };
        if (!Utils.isPhoneNumber(uiCall.lineid)) {
            let newNumber = Utils.getNameForSpecialCnapCases(uiCall.lineid);
            if (newNumber) {
                this.setCallCellNumber(uiCall, newNumber);
            }
        }
        return uiCall;
    }
    onCallChanged(telecomCall, isConfChild = false) {
        Log.i(TAG, "onCallChanged called");
        // check whether there has disconnected call whose timer isn't out
        this.checkDisconnectedCall();
        if (!telecomCall) {
            Log.w(TAG, "onCallChanged telecomCall is null!!");
            return;
        }
        Log.w(TAG, "onCallChanged telecomCall = " + JSON.stringify(telecomCall));
        if (!this.CallChangAction[telecomCall._state]) {
            Log.w(TAG, "onCallChanged telecomCall state error:", telecomCall._state);
            return;
        }
        let call = this.convertTelecomCall(telecomCall);
        let oldcall = this.getCallCellItem(call.callid);
        Log.v(TAG, "onCallChanged, old call: ", JSON.stringify(oldcall), ",\nnewcall = ", JSON.stringify(call));
        Log.d(TAG, "onCallChanged, new call, callid=", call.callid, " call.status=", this.transStatusToString(call.status), " phoneType=", call.phoneType, "number=", call.lineid);
        if (call && call.status === TELE_CALL_STATUS_INCOMING) {
            this.previousCallStatus = TELE_CALL_STATUS_INCOMING;
            this.incomingMap.set(call.callid, new Date().getTime());
        }
        if (oldcall && call) {
            if (call.callid === oldcall.callid &&
                call.status === TELE_CALL_STATUS_INCOMING && oldcall.isIncoming) {
                // 一次来电，onCallChanged会有两次 call.status === INCOMING 的回调
                Log.d(TAG, "onCallChanged, one incoming call has the second INCOMING callback, return directly");
                return;
            }
        }
        let isIncomingCall = this.isIncomingCall(call);
        if (!oldcall && call.status !== TELE_CALL_STATUS_DISCONNECTED && !isConfChild) {
            Log.d(TAG, "onCallChanged will call ViewController.onCallAddedHandle");
            this.mViewController.onCallAddedHandle(isIncomingCall, this.callListSize() <= 0);
        }
        if (this.CallChangAction[call.status](oldcall, call) !== 0) {
            Log.w(TAG, "dont think we are right?", this.status, " ", oldcall, call);
            return;
        }
        let leaveIncoming = this.isLeaveIncoming(oldcall, call);
        let enterOutgoing = this.isEnterOutgoing(oldcall, call);
        if (leaveIncoming === true) {
            this.mViewController.needScreenOn = false;
            this.screenOff();
            Log.i(TAG, "clear IncomingTTSTimer:");
            clearTimeout(this._IncomingTTSTimer.get(call.callid));
            this._IncomingTTSTimer.delete(call.callid);
            this.mViewController.stopTTS();
        }
        if (call.status !== TELE_CALL_STATUS_DISCONNECTED) {
            if (!oldcall) {
                oldcall = this.getCallCellItem(call.callid);
            }
            this.AddCallCell2Array(oldcall, call);
        }
        else if (call.status === TELE_CALL_STATUS_DISCONNECTED &&
            call.disconnectReason === Call.DisconnectReason.IMS_SRVCC_FALLBACK) {
            // volte conference call fallback
            this.handleConference(oldcall, call);
        }
        else {
            this.triggerCalllogsSync();
            let frontCall = this.getFrontCall();
            if (leaveIncoming === true ||
                frontCall && oldcall && frontCall.callid !== oldcall.callid ||
                call.disconnectReason === Call.DisconnectReason.IMS_MERGED_SUCCESSFULLY) {
                Log.v(TAG, "onCallChanged, directly call dealDisconnectedCell, ", leaveIncoming, frontCall.callid, oldcall.callid);
                this.dealDisconnectedCell(oldcall, call);
                if (DialsConstants.Customization.ENABLE_HAND_MOTION && leaveIncoming === true && this.mViewController.hasPostponedSms()) {
                    this.mViewController.triggerSmsSender(call.lineid);
                }
            }
            else if (call && oldcall) {
                Log.d(TAG, "onCallChanged, start a disconnected timer for callid = " + oldcall.callid);
                if (frontCall) {
                    frontCall.hangStatus = CallService.HANGSTATUS.DISCONNECTED;
                }
                this.disconnectedTimer = {
                    oldCall: oldcall,
                    newCall: call,
                    id: null
                };
                this.disconnectedTimer.id = setTimeout(this.dealDisconnectedCell.bind(this, oldcall, call), DISCONNECTED_SHOWING_TIME);
            }
            else {
                // do nothing
                return;
            }
        }
        if (Utils.isCDMACall(call)) {
            if (!this.cdmaCallWaitingTimer && this.getCallCellStatus(call) === TELE_CALL_STATUS_WAITING) {
                Log.d(TAG, "onCallChanged cdma incoming set timer");
                this.cdmaCallWaitingTimer = setTimeout(this.cdmaCallWaitingTimeout.bind(this, call), MAX_CDMA_CALL_WAITING_TIME);
            }
            else if (this.getCallCellStatus(call) !== TELE_CALL_STATUS_WAITING) {
                this.stopCDMACallWaitingTimer();
            }
        }
        if (DialsConstants.Customization.ENABLE_HAND_MOTION) {
            this.mViewController.checkMotionDialogStatus();
        }
        // 控制界面的显示和隐藏
        this.mViewController.controlView({ isLeaveIncoming: leaveIncoming, isEnterOutgoing: enterOutgoing });
        return;
    }
    onConferenceParticipantsStateChanged(telecomCall) {
        Log.d(TAG, "onConferenceParticipantsStateChanged", "call state =", telecomCall._state, ", phoneType =", telecomCall._phoneType, ", call id=", telecomCall._callId);
        this.onCallChanged(telecomCall, true);
    }
    onCallAudioStateChanged(audioState) {
        Log.i(TAG, "onCallAudioStateChanged, audioState._muted=", audioState._muted, " audioState._route=", audioState._route, " audioState._supportedRouteMask=", audioState._supportedRouteMask);
        this.audioState = audioState;
        Log.d(TAG, "will call ViewController controlView in onCallAudioStateChanged");
        this.mViewController.controlView({ audioStateChanged: true });
    }
    onCallerInfoQueried(callId, info) {
        Log.d(TAG, "onCallerInfoQueried, callId = " + callId + ", info._phoneNumber = " + info._phoneNumber);
        if (callId < 0 || !info) {
            Log.e(TAG, "onCallerInfoQueried, invalid parameter");
            return;
        }
        let callItem = this.getCallCellItem(callId);
        if (!callItem) {
            Log.e(TAG, "onCallerInfoQueried, call has been removed");
            return;
        }
        let numberTrimed = Utils.trimSpace(this.getCallCellNumber(callItem));
        let numberResult = Utils.trimSpace(info._phoneNumber);
        if (numberTrimed.indexOf(numberResult) < 0) {
            Log.e(TAG, "onCallerInfoQueried, number not right, numberTrimed = " + numberTrimed + ", numberResult = " + numberResult);
            return;
        }
        if (!info._photoBuffer) {
            Log.e(TAG, "onCallerInfoQueried, photo not existed");
            return;
        }
        let data = { number: info._phoneNumber, photoBuffer: new Buffer(info._photoBuffer) };
        this.ContactInfoCallback(callItem, "localInfo", data);
    }
    dealDisconnectedCell(oldcall, call) {
        if (this.disconnectedTimer && this.disconnectedTimer.id) {
            clearTimeout(this.disconnectedTimer.id);
            this.disconnectedTimer.id = null;
        }
        else {
            Log.v(TAG, "dealDisconnectedCell, disconnected timer is not exist");
        }
        if (this.hangupClickTimer) {
            clearTimeout(this.hangupClickTimer);
            this.hangupClickTimer = null;
        }
        switch (oldcall.callinfo.status) {
            case TELE_CALL_STATUS_DIALING:
            case TELE_CALL_STATUS_ALERTING:
                Log.d(TAG, "dealDisconnectedCell, .....from dialing to disconnect.....");
                break;
            case TELE_CALL_STATUS_INCOMING:
            case TELE_CALL_STATUS_WAITING:
                Log.d(TAG, "dealDisconnectedCell, .....from incoming to disconnect.....");
                break;
            case TELE_CALL_STATUS_ACTIVE:
            case TELE_CALL_STATUS_HOLDING:
                Log.d(TAG, "dealDisconnectedCell, .....from active to disconnect.....");
                break;
        }
        if (oldcall.callinfo && oldcall.callinfo.multiparty === 1) {
            this.handleConference(oldcall, call);
        }
        this.removeCallCellFromArray(oldcall, null);
        this.releaseCallCellItem(call);
        let leaveIncoming = this.isLeaveIncoming(oldcall, call);
        if (!leaveIncoming) {
            this.mViewController.controlView();
        }
        this.showCallDuration(oldcall, call);
        this.stopCDMACallWaitingTimer();
        if (this.callListSize() === 0) {
            this.audioState = null;
        }
        this.previousCallStatus = -1;
        return;
    }
    checkDisconnectedCall() {
        Log.v(TAG, "checkDisconnectedCall, disconnectedTimer exist: " + Boolean(this.disconnectedTimer && this.disconnectedTimer.id));
        if (this.disconnectedTimer && this.disconnectedTimer.id) {
            Log.d(TAG, "checkDisconnectedCall, disconnected timer exist and will deal it");
            clearTimeout(this.disconnectedTimer.id);
            this.disconnectedTimer.id = null;
        }
        else {
            Log.v(TAG, "checkDisconnectedCall, no disconneted timer and do nothing");
            return;
        }
        let frontCall = this.getFrontCall();
        if (!frontCall || frontCall.hangStatus !== CallService.HANGSTATUS.DISCONNECTED) {
            Log.e(TAG, "checkDisconnectedCall, current front call hangStatus is not DISCONNECTED");
        }
        this.dealDisconnectedCell(this.disconnectedTimer.oldCall, this.disconnectedTimer.newCall);
        return;
    }
    showCallDuration(callCell, call) {
        if (!callCell) {
            Log.e(TAG, "showCallDuration, wrong parameter");
            return;
        }
        Log.d(TAG, "showCallDuration:", call ? call.disconnectReason : undefined);
        if ((callCell.callinfo && callCell.callinfo.multiparty === 1 && !callCell.inConf) ||
            (call && call.disconnectReason === Call.DisconnectReason.IMS_MERGED_SUCCESSFULLY) ||
            (Utils.isVoLTECall(call) && this.confCell && call.callid !== this.confCell.originCallId)) {
            Log.d(TAG, "showCallDuration, callid " + callCell.callid + " is in conference call and won't show call duration");
            return;
        }
        // show bt disconnected infor if reason is HFP break
        if (call && call.disconnectReason === Call.DisconnectReason.DISCONNECT_REASON_HFP_BREAK) {
            this.mViewController.showToast(Res.getString("TEXT_HFP_BREAK"), true);
            return;
        }
        if (callCell.status !== TELE_CALL_STATUS_HOLDING && callCell.status !== TELE_CALL_STATUS_ACTIVE) {
            Log.d(TAG, "showCallDuration, the call should not be actived");
            return;
        }
        // align with Contacts module, use Math.ceil to get duration
        let timeLength = Utils.getTimeLength(this.timecount, callCell.startCount, false);
        let durationString = Res.getString("TEXT_END_CALL") + "(" + timeLength + ")";
        Log.v(TAG, "showCallDuration, durationString = " + durationString);
        this.mViewController.showToast(durationString, false);
    }
    setCallToDisconnecting(callCell) {
        Log.v(TAG, "setCallToDisconnecting, callid = ", callCell.callid);
        if (!callCell) {
            Log.e(TAG, "setCallToDisconnecting, wrong parameter");
            return;
        }
        if (callCell.hangStatus) {
            Log.e(TAG, "setCallToDisconnecting, ignore as hangStatus has been set to: " + callCell.hangStatus);
            return;
        }
        callCell.hangStatus = CallService.HANGSTATUS.DISCONNECTING;
        this.mViewController.updateCallState(this.getExistCallList(), this.timecount);
        return;
    }
    getExistCallList() {
        let callList = [];
        for (let i = 0; i < CallService.CALL_SERVICE_STATUS.END_CALL; i++) {
            Log.d(TAG, "getExistCallList callCells[" + i + "] length > 1 !!");
            for (let j = 0; j < this.callCells[i].length; j++) {
                callList.push(this.callCells[i][j]);
            }
        }
        return callList;
    }
    dumpStatus() {
        Log.v(TAG, ">>>>>Call service dump: ", "status: ", this.status, "\n", ">>>>>call items: ", this.CallList, "\n", ">>>>>call cells: ");
        this.dumpCallCells(">>>>>");
    }
    dumpCallCells(prefix) {
        for (let i = 0; i < this.callCells.length; i++) {
            Log.v(TAG, prefix, ">>", JSON.stringify(this.callCells[i]));
        }
    }
    addCallCell2List(callInfo) {
        this.CallList.set(callInfo.callid, callInfo);
        Log.d(TAG, "addCallCell2List, callid = ", callInfo.callid);
        let number = this.getCallCellNumber(callInfo);
        if (Utils.isPhoneNumber(Utils.trimSpace(number))) {
            let name = callInfo.callinfo ? callInfo.callinfo.name : callInfo.name;
            if (name) {
                this.ContactInfoCallback(callInfo, "localInfo", { number: number, name: name }, false);
            }
        }
        // Get sim operator name
        let subid = callInfo.subid;
        if (subid !== undefined) {
            if (!this.operatorInfo[subid]) {
                this.operatorInfo[subid] = {};
            }
            callInfo.operatorInfo = this.operatorInfo[subid];
        }
        if (Utils.isVoLTECall(callInfo)) {
            callInfo.isVoLTECall = true;
        }
    }
    ContactInfoCallback(callCell, field, data, needRefresh = true) {
        Log.v(TAG, "ContactInfoCallback field =", field, ", data =", data);
        Log.v(TAG, "ContactInfoCallback localInfo =", callCell.localInfo);
        Log.v(TAG, "ContactInfoCallback numberInfo =", callCell.numberInfo);
        Log.v(TAG, "ContactInfoCallback contactInfo =", callCell.callerInfo);
        if (!callCell.callerInfo) {
            callCell.callerInfo = { name: "" };
            callCell.numberInfo = {};
            callCell.localInfo = { uninited: true };
        }
        if (field === "localInfo") {
            Object.assign(callCell.localInfo, data);
            callCell.localInfo.uninited = false;
        }
        let copy = callCell.callerInfo;
        // set number service's data only before local info is not setted.
        if (field !== "numberInfo" || callCell.localInfo.uninited === true) {
            copy.name = callCell.localInfo.name || callCell.numberInfo.name;
            if (!copy.name) {
                copy.name = this.getCallCellNumber(callCell);
                callCell.isStrangeNumber = true;
            }
            // Contacts appliaction will support photo
            if (data.photoUri) {
                copy.photoUri = data.photoUri;
            }
            else if (data.photoBuffer) {
                copy.photoBuffer = data.photoBuffer;
            }
            else if (data.path) {
                // use number service icon to show photo
                copy.photoUri = data.path;
            }
        }
        else if (!copy.photoUri && data.path) {
            // use number service icon to show photo
            copy.photoUri = data.path;
        }
        if (field === "numberInfo") {
            copy.type = data.type ? data.type : copy.type;
            copy.subtype = data.subtype ? data.subtype : copy.subtype;
            copy.markCount = data.markCount ? data.markCount : copy.markCount;
        }
        if (data.country || data.city || data.province) {
            if (data.city === data.province) {
                copy.area = data.province;
            }
            else {
                copy.area = (data.province || "") + (data.city || "");
            }
            if (!copy.area) {
                copy.area = data.country;
            }
        }
        if (!copy.area) {
            copy.area = "";
        }
        if (data.isPresetPath && data.path) {
            copy.photoUri = Res.getImageSrc(data.path);
        }
        if (data.isPresetName && data.name) {
            copy.name = Res.getString(data.name);
        }
        Log.v(TAG, "ContactInfoCallback end contactInfo =", callCell.callerInfo);
        if (needRefresh === true) {
            this.mViewController.controlView();
        }
    }
    getCallCellNumber(callInfo) {
        let number;
        if (callInfo.callinfo) {
            number = callInfo.callinfo.lineid;
        }
        else {
            number = callInfo.lineid;
        }
        return number;
    }
    setCallCellNumber(callInfo, number) {
        if (!number || !callInfo) {
            return;
        }
        if (callInfo.callinfo) {
            callInfo.callinfo.lineid = number;
        }
        callInfo.lineid = number;
        return callInfo;
    }
    getCallCellStatus(callInfo) {
        let status;
        if (callInfo.callinfo) {
            status = callInfo.callinfo.status;
        }
        else {
            status = callInfo.status;
        }
        return status;
    }
    releaseCallCellItem(callInfo) {
        this.CallList.delete(callInfo.callid);
    }
    callListSize() {
        return this.CallList.size;
    }
    getCallCellItem(callid) {
        return this.CallList.get(callid);
    }
    cmCb(result, callId) {
        Log.v(TAG, "[cmCb] " + result + " " + callId);
    }
    cantDial(subid, isEmergency) {
        if (this.status === CallService.CALL_SERVICE_STATUS.DIALING_CALL ||
            this.status === CallService.CALL_SERVICE_STATUS.INCOMING_CALL) {
            return true;
        }
        if (this.getCallLines() > 1) {
            return true;
        }
        if (!isEmergency && this.radioStatus[subid] > 1) {
            return true;
        }
        return false;
    }
    getFrontCall() {
        return this.getCall(1);
    }
    getBackCall() {
        return this.getCall(2);
    }
    getCall(num) {
        let count = 0;
        for (let i = 0; i < CallService.CALL_SERVICE_STATUS.END_CALL; i++) {
            if (this.callCells[i][0]) {
                count++;
                if (count === num) {
                    return this.callCells[i][0];
                }
            }
        }
        return null;
    }
    getCallLines() {
        let num = 0;
        for (let i = 0; i < CallService.CALL_SERVICE_STATUS.END_CALL; i++) {
            if (this.callCells[i][0]) {
                num++;
            }
        }
        return num;
    }
    hangup(hangType, msg) {
        this.isHangupOrAnswer = true;
        let callinfo = this.getFrontCall();
        if (!callinfo) {
            Log.i(TAG, "hangup not found front call, return!");
            return;
        }
        Log.d(TAG, "hangup, msg=", msg, " hangType=", hangType, " callinfo.callid=", callinfo.callid);
        callinfo.hangType = hangType;
        this.setCallToDisconnecting(callinfo);
        if (this.isConferenceCell(callinfo)) {
            Log.d(TAG, "hangup callinfo.isCDMA =", callinfo.isCDMA);
            if (callinfo.isCDMA) {
                if (callinfo.originCallId) {
                    Log.d(TAG, "callinfo.cdmaCallId =", callinfo.originCallId);
                    this.inCallService.disconnectCall(callinfo.originCallId);
                }
                else {
                    for (let i = 0; i < this.confCell.conferencelist.length; i++) {
                        let loopCallCell = this.confCell.conferencelist[i];
                        Log.d(TAG, "loopCallCell.callinfo.callid =", loopCallCell.callinfo.callid);
                        this.inCallService.disconnectCall(loopCallCell.callinfo.callid);
                    }
                }
            }
            else {
                this.inCallService.disconnectConference();
            }
        }
        else if (msg === "") {
            if (callinfo && callinfo.isIncoming && !callinfo.wasConnected) {
                let showMissCallInd = Res.getConfig("show_miss_call_indication_for_reject_mt");
                if (showMissCallInd) {
                    // this interface will show miss call indication
                    Log.d(TAG, "hangup by rejectCall:", showMissCallInd);
                    this.inCallService.rejectCall(callinfo.callid, false, null);
                }
                else {
                    // this interface NOT show miss call indication
                    Log.d(TAG, "hangup by disconnectCall:", showMissCallInd);
                    this.inCallService.disconnectCall(callinfo.callid);
                }
            }
            else {
                Log.d(TAG, "hangup by disconnectCall");
                this.inCallService.disconnectCall(callinfo.callid);
            }
        }
        else {
            this.inCallService.rejectCall(callinfo.callid, true, msg);
        }
        this.stopCDMACallWaitingTimer();
        // 启动timer，如果10秒后仍然没有收到挂断的回调，就隐藏掉界面上所有的窗口
        this.hangupClickTimer = setTimeout(() => {
            Log.d(TAG, "onHangupClick hangup timer timeout");
            if (this.hangupClickTimer) {
                clearTimeout(this.hangupClickTimer);
                this.hangupClickTimer = null;
                if (this.mViewController.appVisible) {
                    this.mViewController.hideAllWindow();
                }
            }
        }, 10 * 1000);
    }
    hangupSpecial(callCell, hangType) {
        if (!callCell) {
            Log.w(TAG, "hangupSpecial callCell is null, return!!");
            return;
        }
        callCell.hangType = hangType;
        this.setCallToDisconnecting(callCell);
        callCell.activeHangUp = true;
        if (this.isConferenceCell(callCell)) {
            this.inCallService.disconnectConference();
            return;
        }
        let callinfo = callCell.callinfo;
        Log.d(TAG, "hangupSpecial, callid = " + callinfo.callid);
        this.inCallService.disconnectCall(callinfo.callid);
        this.stopCDMACallWaitingTimer();
    }
    answer(answerType, incomingCall, videoState) {
        this.isHangupOrAnswer = true;
        let callCell = incomingCall;
        if (!callCell) {
            callCell = this.getIncomingCall();
            if (!callCell) {
                Log.w(TAG, "answer NOT found incoming call, return!!");
                return;
            }
        }
        callCell.answerType = answerType;
        let callId = callCell.callinfo && callCell.callinfo.hasOwnProperty("callid") ? callCell.callinfo.callid : 0;
        let cardId = callCell.callinfo && callCell.callinfo.hasOwnProperty("subid") ? callCell.callinfo.subid : 0;
        Log.d(TAG, "answer, cardId = " + cardId + ", callId = " + callId);
        this.inCallService.answerCall(callId, videoState ? videoState : 0);
        this.stopCDMACallWaitingTimer();
    }
    answerAndEndActive() {
        let callCell = this.getIncomingCall();
        if (!callCell) {
            Log.w(TAG, "answerAndEndActive NOT found incoming call, return!!");
            return;
        }
        let callId = callCell.callinfo.callid;
        Log.d(TAG, "answerAndEndActive, callId = " + callId);
    }
    flashForCDMA(callCell) {
        const ACTIVE_CALL = CallService.CALL_SERVICE_STATUS.ACTIVE_CALL;
        if (Utils.isCDMACall(callCell.callinfo) && callCell.callinfo.status === TELE_CALL_STATUS_ACTIVE) {
            Log.d(TAG, "flashForCDMA length:", this.callCells[ACTIVE_CALL].length);
            if (this.callCells[ACTIVE_CALL].length > 1) {
                Log.d(TAG, "flashForCDMA callid:", this.callCells[ACTIVE_CALL][0].callid);
                let activeCallCell = this.callCells[ACTIVE_CALL][0];
                if (activeCallCell && activeCallCell.callid != CONF_CALLID) {
                    activeCallCell.callinfo.multiparty = 1;
                    this.handleConference(activeCallCell, activeCallCell.callinfo);
                }
                let otherCallCell = this.callCells[ACTIVE_CALL][1];
                if (otherCallCell && otherCallCell.callid != CONF_CALLID) {
                    otherCallCell.callinfo.multiparty = 1;
                    this.handleConference(otherCallCell, otherCallCell.callinfo);
                }
                this.mViewController.controlView();
            }
        }
    }
    conference() {
        let callCell = this.getFrontCall();
        if (!callCell) {
            Log.w(TAG, "conference NOT found front call, return!!");
            return;
        }
        let callid = this.getCallId(callCell);
        let ret = this.inCallService.conference(callid);
        Log.d(TAG, "conference, callid = " + callid + " conference ret = " + ret);
        if (ret === false) {
            this.mViewController.showToast(Res.getString("TEXT_MERGE_FAILED"), true);
        }
        this.flashForCDMA(callCell);
    }
    hold() {
        let callCell = this.getFrontCall();
        if (!callCell) {
            Log.w(TAG, "hold not found front call, return!!");
            return;
        }
        let callid = this.getCallId(callCell);
        Log.d(TAG, "hold, callid = " + callid);
        this.inCallService.holdCall(callid);
        this.flashForCDMA(callCell);
    }
    split(callCell) {
        let callinfo = callCell.callinfo;
        Log.d(TAG, "split, callid = " + callinfo.callid);
        this.inCallService.splitFromConference(callinfo.callid);
    }
    sendDtmf(digit) {
        if (digit === undefined || digit === null || digit === "") {
            Log.e(TAG, "sendDtmf, wrong parameter");
            return;
        }
        Log.d(TAG, "sendDtmf, digit = " + digit);
        let callinfo = this.getFrontCall();
        if (callinfo) {
            this.inCallService.playDtmfTone(this.getCallId(callinfo), digit.toString());
        }
    }
    onPostDialWait(callId, remaining) {
        Log.d(TAG, "onPostDialWait called: callId=", callId, " remaining=", remaining);
        this.mViewController.handlePostDialWait(callId, remaining);
    }
    silenceRinger() {
        Log.d(TAG, "silenceRinger");
        if (!this.telecomManager) {
            this.telecomManager = TelecomManager.getInstance();
        }
        this.telecomManager.silenceRinger();
    }
    setAudioRoute(route) {
        Log.d(TAG, "setAudioRoute, will set audio route to " + route);
        if (route === this.audioState._route) {
            Log.v(TAG, "the same as current route value and ignore it");
            return;
        }
        this.inCallService.setAudioRoute(route);
    }
    getAudioRoute() {
        return this.audioState._route;
    }
    isBTSupported() {
        return Boolean(this.audioState._supportedRouteMask & CallAudioState.ROUTE_BLUETOOTH);
    }
    isBTEnabled() {
        return Boolean(this.audioState._route & CallAudioState.ROUTE_BLUETOOTH);
    }
    isWiredHeadsetEnabled() {
        return Boolean(this.audioState._route & CallAudioState.ROUTE_WIRED_HEADSET);
    }
    isEarpieceEnabled() {
        return Boolean(this.audioState._route & CallAudioState.ROUTE_EARPIECE);
    }
    // change between the speaker of ivi or phone
    changeSpeakerphoneState() {
        let oldState = this.isSpeakerphoneEnable();
        Log.d(TAG, "changeSpeakerphoneState, will set speacker state to " + !oldState);
        this.inCallService.setAudioRoute(oldState ? CallAudioState.ROUTE_BLUETOOTH : CallAudioState.ROUTE_SPEAKER);
    }
    isSpeakerphoneEnable() {
        return Boolean(this.audioState._route & CallAudioState.ROUTE_SPEAKER);
    }
    changeMicrophoneMuteState() {
        let oldState = this.isMicrophoneMute();
        Log.d(TAG, "changeMicrophoneMuteState, will set muteState to " + !oldState);
        this.inCallService.setMute(!oldState);
    }
    isMicrophoneMute() {
        return this.audioState._muted;
    }
    resetMuteAndSpeakerState() {
        if (this.isSpeakerphoneEnable() !== true) {
            Log.d(TAG, "resetMuteAndSpeakerState, will set speaker state to true");
            this.inCallService.setAudioRoute(CallAudioState.ROUTE_SPEAKER);
        }
        this.resetMuteState();
    }
    resetMuteState() {
        if (this.isMicrophoneMute() === true) {
            Log.d(TAG, "resetMuteState, will set mute state to false");
            this.inCallService.setMute(false);
        }
    }
    getCallId(callInfo) {
        if (this.isConferenceCell(callInfo)) {
            if ((callInfo.isCDMA || callInfo.isVoLTE) && callInfo.originCallId !== undefined) {
                return callInfo.originCallId;
            }
            else if (callInfo.conferencelist && callInfo.conferencelist[0]) { // for gsm call
                return callInfo.conferencelist[0].callid;
            }
            else {
                Log.e(TAG, "getCallId, there is no call item in coference list");
            }
        }
        return callInfo.callinfo ? callInfo.callinfo.callid : callInfo.callid;
    }
    cdmaCallWaitingTimeout(callinfo) {
        if (callinfo && this.getCallCellStatus(callinfo) === TELE_CALL_STATUS_WAITING) {
            Log.d(TAG, "cdmaCallWaitingTimeout", "rejectCall:", this.getCallId(callinfo));
            if (this.getCallId(callinfo) !== undefined) {
                this.inCallService.rejectCall(this.getCallId(callinfo), false, null);
                this.stopCDMACallWaitingTimer();
            }
        }
    }
    stopCDMACallWaitingTimer() {
        if (this.cdmaCallWaitingTimer) {
            Log.d(TAG, "stopCDMACallWaitingTimer", "STOP");
            clearTimeout(this.cdmaCallWaitingTimer);
            this.cdmaCallWaitingTimer = null;
        }
    }
    stopInCallAdapterProxy(disableCallback) {
        Log.d(TAG, "stopInCallAdapterProxy:", disableCallback);
        this.inCallService.stopInCallAdapterProxy(disableCallback);
    }
    isHoldState() {
        return this.getStatus() === CallService.CALL_SERVICE_STATUS.HOLD_CALL;
    }
    isAnyCallExist() {
        let calllist = this.getExistCallList();
        return calllist.length > 0;
    }
    triggerCalllogsSync() {
        let self = this;
        let clearContactUpdateTimer = function () {
            if (self.contactUpdateTimer) {
                Log.d(TAG, "triggerCalllogsSync, stop timer");
                clearTimeout(self.contactUpdateTimer);
                self.contactUpdateTimer = null;
            }
        };
        let onContactUpdateTimeout = function () {
            clearContactUpdateTimer();
            let pageLink = new PageLink("page://contacts.yunos.com/btservice");
            pageLink.eventName = "pullcalllog";
            pageLink.data = JSON.stringify({ progress: false });
            Page.getInstance().sendLink(pageLink, (err) => {
                if (err) {
                    Log.d(TAG, "triggerCalllogsSync, sendLink error:", err);
                    return;
                }
                Log.d(TAG, "triggerCalllogsSync, sendLink ok");
            });
        };
        clearContactUpdateTimer();
        Log.d(TAG, "triggerCalllogsSync, start timer");
        this.contactUpdateTimer = setTimeout(onContactUpdateTimeout, 20);
    }
    transStatusToString(status) {
        let statusString = "";
        switch (status) {
            case TELE_CALL_STATUS_ACTIVE:
                statusString = "ACTIVE";
                break;
            case TELE_CALL_STATUS_HOLDING:
                statusString = "HOLD";
                break;
            case TELE_CALL_STATUS_DIALING:
                statusString = "DIALING";
                break;
            case TELE_CALL_STATUS_ALERTING:
                statusString = "ALERTING";
                break;
            case TELE_CALL_STATUS_INCOMING:
                statusString = "INCOMING";
                break;
            case TELE_CALL_STATUS_WAITING:
                statusString = "WAITING";
                break;
            case TELE_CALL_STATUS_DISCONNECTING:
                statusString = "DISCONNECTING";
                break;
            case TELE_CALL_STATUS_DISCONNECTED:
                statusString = "DISCONNECTED";
                break;
            default:
                statusString = status.toString();
                break;
        }
        return statusString;
    }
}
CallService.SLITID = {
    SLOT1: 0,
    SLOT2: 1,
    SIP: 2,
    SLOT_NULL: 101,
    SLOT_SELECT: 102
};
CallService.res = {
    SCESS: 1,
    FAIL: 2
};
CallService.CALLSTATE = {
    TELE_CALL_STATUS_ACTIVE: TELE_CALL_STATUS_ACTIVE,
    TELE_CALL_STATUS_HOLDING: TELE_CALL_STATUS_HOLDING,
    TELE_CALL_STATUS_DIALING: TELE_CALL_STATUS_DIALING,
    TELE_CALL_STATUS_ALERTING: TELE_CALL_STATUS_ALERTING,
    TELE_CALL_STATUS_INCOMING: TELE_CALL_STATUS_INCOMING,
    TELE_CALL_STATUS_WAITING: TELE_CALL_STATUS_WAITING,
    TELE_CALL_STATUS_DISCONNECTED: TELE_CALL_STATUS_DISCONNECTED
};
CallService.AUDIOROUTE = {
    EARPIECE: CallAudioState.ROUTE_EARPIECE,
    BLUETOOTH: CallAudioState.ROUTE_BLUETOOTH,
    WIRED_HEADSET: CallAudioState.ROUTE_WIRED_HEADSET,
    SPEAKER: CallAudioState.ROUTE_SPEAKER,
    WIRED_OR_EARPIECE: CallAudioState.ROUTE_WIRED_OR_EARPIECE
};
CallService.HANGSTATUS = {
    NULL: 0,
    DISCONNECTING: 1,
    DISCONNECTED: 2
};
CallService.CALL_SERVICE_STATUS = {
    IDLE_CALL: 0,
    INCOMING_CALL: 1,
    DIALING_CALL: 2,
    ACTIVE_CALL: 3,
    HOLD_CALL: 4,
    END_CALL: 5
};
module.exports = CallService;
