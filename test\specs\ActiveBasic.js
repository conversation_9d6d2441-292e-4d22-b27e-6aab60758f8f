"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");
const TouchEvent = require("yunos/ui/event/TouchEvent");
var View = require("yunos/ui/view/View");
var CallAudioState = require("yunos/telecom/CallAudioState");

const addCall = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 1
};

// huangup call
const huangupcall = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const ringcall = {
    _subId: 0,
    _callId: 1,
    _state: 4,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const activecall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const holdCall = {
    _subId: 0,
    _callId: 1,
    _state: 2,
    _displayName: "",
    _callNumber: "10086",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const touchup = new TouchEvent({
    type: "touchend",
    changedTouches: [{
        clientX: 100,
        clientY: 10,
        identifier: 0
    }],
    eventPhase: TouchEvent.EventPhase.Target
});

const muteEvent = {_muted: true, _route: 1, _supportedRouteMask: 9};
const unMuteEvent = {_muted: false, _route: 1, _supportedRouteMask: 9};

const speakerEvent = {_muted: false, _route: 8, _supportedRouteMask: 9};
const unSpeakerEvent = {_muted: false, _route: 1, _supportedRouteMask: 9};

const recordFilePath = "/storage/emulated/0/PhoneRecord/19700316_044822";

describe("ActiveBasic", function() {
    utTools.log("ActiveBasic start");

    var originalTimeout;

    beforeAll(() => {
        utTools.log("ActiveBasic beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("ActiveBasic afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("ActiveBasic beforeEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("calladded", addCall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", ringcall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            const controler = uiInf.getControler();
            const moLayout = uiInf.getMOLayout();
            utTools.equal(controler.appVisible, true);
            utTools.equal(uiInf.getTopLayout(), moLayout);
        }());
    });

    afterEach((done) => {
        utTools.log("ActiveBasic afterEach");

        utTools.asyncRun(done, function *() {
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            const controler = uiInf.getControler();
            utTools.equal(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
            yield 3000;
        }());
    });

    // recod -> end record
    it("record", function(done) {
        utTools.log("ActiveBasic record in");
        utTools.asyncRun(done, function *() {
            utTools.log("ActiveBasic record run");
            const moLayout = uiInf.getMOLayout();
            utTools.setRecordPermission(true);
            moLayout.record.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("recorderstatechanged", "started", null);
            yield 3000;
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.record.text);

            moLayout.record.dispatchEvent("touchend", touchup);
            utTools.MockInCallService.emit("recorderstatechanged", "stopped", recordFilePath);
            yield 3000;
            utTools.strEqual(moLayout.record.text, "BTN_RECORDE");
            utTools.setRecordPermission(false);

            // utTools.equal(moLayout.keyboardPanel.visibility, View.Visibility.Visible);
        }());
    });

    // speaker -> receiver
    it("handfree", function(done) {
        utTools.log("ActiveBasic handfree in");
        utTools.asyncRun(done, function *() {
            utTools.log("ActiveBasic handfree run");
            const moLayout = uiInf.getMOLayout();
            moLayout.handsfree.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.setAudioRouteParams, [CallAudioState.ROUTE_SPEAKER]);
            utTools.MockInCallService.emit("callaudiostatechanged", speakerEvent);
            yield 3000;
            utTools.notEqual(moLayout.handsfree.color.toUpperCase(), "#FFFFFF7F");

            moLayout.handsfree.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.setAudioRouteParams, [CallAudioState.ROUTE_WIRED_OR_EARPIECE]);
            utTools.MockInCallService.emit("callaudiostatechanged", unSpeakerEvent);
            yield 3000;
            utTools.equal(moLayout.handsfree.color.toUpperCase(), "#FFFFFF7F");

            // utTools.equal(moLayout.keyboardPanel.visibility, View.Visibility.Visible);
        }());
    });

    // mute -> unmute
    it("mute", function(done) {
        utTools.log("ActiveBasic mute in");
        utTools.asyncRun(done, function *() {
            utTools.log("ActiveBasic mute run");
            const moLayout = uiInf.getMOLayout();
            moLayout.mute.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.setMuteParams, [true]);
            utTools.MockInCallService.emit("callaudiostatechanged", muteEvent);
            yield 300;
            utTools.notEqual(moLayout.mute.color.toUpperCase(), "#FFFFFF7F");

            moLayout.mute.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.setMuteParams, [false]);
            utTools.MockInCallService.emit("callaudiostatechanged", unMuteEvent);
            yield 300;
            utTools.equal(moLayout.mute.color.toUpperCase(), "#FFFFFF7F");

            // utTools.MockInCallService.emit("callstatechanged", holdCall);
            // utTools.equal(moLayout.keyboardPanel.visibility, View.Visibility.Visible);
        }());
    });

    // hold -> unhold -> hold
    it("hold", function(done) {
        utTools.log("ActiveBasic hold in");
        utTools.asyncRun(done, function *() {
            utTools.log("ActiveBasic hold run");
            const moLayout = uiInf.getMOLayout();

            // active state
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            utTools.equal(moLayout.holdCall.selected, false);

            // click hold button
            moLayout.holdCall.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.holdCallParams, [huangupcall._callId]);
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            yield 3000;
            utTools.equal(moLayout.callState.text, global.res.getString("BTN_HOLD_ON"));
            utTools.equal(moLayout.holdCall.selected, true);

            // click hold button again
            moLayout.holdCall.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.holdCallParams, [huangupcall._callId]);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;

            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            utTools.equal(moLayout.holdCall.selected, false);
            yield 3000;
        }());
    });

    // show pad -> click some key -> hide pad -> show pad
    it("keyboard", function(done) {
        utTools.log("ActiveBasic keyboard in");
        utTools.asyncRun(done, function *() {
            utTools.log("ActiveBasic keyboard run");
            const moLayout = uiInf.getMOLayout();

            var getKeyTouchEvent = function(isStart, key) {
                return new TouchEvent({
                    target: key,
                    type: isStart ? "touchstart" : "touchend",
                    changedTouches: [{
                        clientX: 100,
                        clientY: 10,
                        identifier: 0
                    }],
                    eventPhase: TouchEvent.EventPhase.Target
                });
            };

            // show
            moLayout.keyboard.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(moLayout.keyboardPanel.visibility, View.Visibility.Visible);
            utTools.equal(moLayout.dialPanel.visibility, View.Visibility.None);

            // click
            var touchStartEvent = getKeyTouchEvent(true, moLayout.key2);
            var touchEndEvent = getKeyTouchEvent(false, moLayout.key2);
            moLayout.keyboardLayout.dispatchEvent("touchstart", touchStartEvent);
            yield 500;
            moLayout.keyboardLayout.dispatchEvent("touchend", touchEndEvent);
            utTools.ObjectEqual(utTools.MockInCallService.playDtmfToneParams, [1, "2"]);

            touchStartEvent = getKeyTouchEvent(true, moLayout.key5);
            touchEndEvent = getKeyTouchEvent(false, moLayout.key5);
            moLayout.keyboardLayout.dispatchEvent("touchstart", touchStartEvent);
            yield 500;
            moLayout.keyboardLayout.dispatchEvent("touchend", touchEndEvent);
            utTools.ObjectEqual(utTools.MockInCallService.playDtmfToneParams, [1, "5"]);

            touchStartEvent = getKeyTouchEvent(true, moLayout.key7);
            touchEndEvent = getKeyTouchEvent(false, moLayout.key7);
            moLayout.keyboardLayout.dispatchEvent("touchstart", touchStartEvent);
            yield 500;
            moLayout.keyboardLayout.dispatchEvent("touchend", touchEndEvent);
            utTools.ObjectEqual(utTools.MockInCallService.playDtmfToneParams, [1, "7"]);

            yield 3000;
            utTools.equal(moLayout.bigNumber.text, "257");

            // hide
            moLayout.hide.dispatchEvent("touchend", touchup);
            yield 3000;
            utTools.equal(moLayout.keyboardPanel.visibility, View.Visibility.None);
            utTools.equal(moLayout.dialPanel.visibility, View.Visibility.Visible);

            // show
            moLayout.keyboard.dispatchEvent("touchend", touchup);
            utTools.equal(moLayout.bigNumber.text, "");
        }());
    });


});
