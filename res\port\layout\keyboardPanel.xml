<CompositeView id="keyboardPanel" width="{sdp(872)}" height="{sdp(894)}" layout="{layout.keyboardPanelLayout}" visibility="{enum.View.Visibility.Hidden}" >
    <ScrollableView id="scroll" width="{config.keyboard_scroll_width}" height="{config.keyboard_scroll_height}"
            orientation="{enum.ScrollableView.Orientation.Both}"
            overScroll="false" background="transparent" layout="{layout.scrollLayout}">
        <TextView id="bigNumber" width="{config.keyboard_text_width}" color="{theme.color.White_1}" height="{config.keyboard_text_height}" fontSize="{config.keyboard_text_size}" />
    </ScrollableView>
    <View id="keyboardBackground" background="{theme.color.White_5}" width="{sdp(872)}" height="{sdp(1)}"/>
    <include markup="Keyboard" id="keyboardLayout" layout="{layout.keyboardGridLayout}"  width="{sdp(626)}" height="{sdp(456)}"/>
    <ImageView id="hide" src="{img(images/qms_call_ic_conceal_normal.png)}"
        height="{sdp(80)}" width="{sdp(80)}"
        scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
    <ButtonBM id="keyboardHangUp" iconSrc="{img(images/qms_call_bt_hang_up_long.png)}"
        buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"
        height="{sdp(80)}" width="{sdp(744)}"/>
</CompositeView>
