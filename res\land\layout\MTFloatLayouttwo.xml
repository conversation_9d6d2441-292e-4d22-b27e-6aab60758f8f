<CompositeView id="root" left="{sdp(25)}" layout="{layout.mtGuestureFloatLayoutTwo}" background="{img(images/qms_call_bg_Incomingcall.png)}" width="{config.app_width}" height="{sdp(672)}">
    <CompositeView id="mtFloat" layout="{layout.mtFloatLayoutTwo}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent">
        <CompositeView id="mtFloatOneDingding" layout="{layout.mtFloatLayoutOneDingding}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent">
            <CompositeView id="userInfo" width="{sdp(390)}" height="{sdp(96)}" layout="{layout.floatCallInfoLayoutTwo}">
                <CompositeView id="avatarInfo" width="{sdp(96)}" height="{sdp(96)}" layout="{layout.floatCallInfoLayoutTwoavatarInfo}">
                    <ImageView id="avatar" width="{sdp(96)}" height="{sdp(96)}"
                        src="{img(images/qms_call_ic_portrait_small.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                    <ImageView id="avatarChart" width="{sdp(26)}" height="{sdp(26)}"
                        src="{img(images/qms_call_btn_dingding_small.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                </CompositeView>
                <TextView id="callname" propertySetName="extend/hdt/FontTitle1" color="{theme.color.White_1}" width="{sdp(262)}" height="{sdp(40)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="number" propertySetName="extend/hdt/FontBody2" color="{theme.color.White_3}" width="{sdp(96)}" height="{sdp(28)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_INCOMING_CALL}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="aiTransfer" fontSize="{sdp(18)}" color="{theme.color.Brand_1}" width="{sdp(136)}" height="{sdp(20)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_AI_TRANSFER}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
            </CompositeView>

            <CompositeView id="anwerhanup"  clipBound="false" layout="{layout.anwerhanupLayout}" height = "{sdp(88)}" width="{sdp(264)}">
                <ButtonBM id="hangUp" width="{sdp(88)}" height="{sdp(88)}" iconSrc="{img(images/qms_call_bt_hang_up_long.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="answer" width="{sdp(88)}" height="{sdp(88)}" iconSrc="{img(images/qms_call_btn_answer_01_normal.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
            </CompositeView>
            <ImageView id="minimize" width="{sdp(56)}" height="{sdp(56)}" src="{img(images/qms_call_ic_down_normal.png)}"
                scaleType="{enum.ImageView.ScaleType.Centerinside}" multiState="{config.button_icDownMultiState}"/>
        </CompositeView>
        <CompositeView id="mtFloatTwoDingding" layout="{layout.mtFloatLayoutTwoDingding}" height="{config.mt_float_window_height_sdp_two}" width="{config.mt_float_window_width_sdp_two}" background="transparent" visibility="{enum.View.Visibility.None}">
            <CompositeView id="userInfoTwo" width="{sdp(593)}" height="{sdp(72)}" layout="{layout.floatCallInfoLayoutTwoDingding}">
                <ImageView id="avatarIncoming" width="{sdp(72)}" height="{sdp(72)}"
                    src="{img(images/qms_call_ic_portrait_verysmall.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                <TextView id="callnameIncoming" propertySetName="extend/hdt/Headline" color="{theme.color.White_2}" width="{sdp(160)}" height="{sdp(32)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="numberIncoming" propertySetName="extend/hdt/Caption2" color="{theme.color.White_3}" width="{sdp(80)}" height="{sdp(24)}"
                        align = "{enum.TextView.Align.Left}" text="{string.TEXT_INCOMING_CALL}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <View id="individer" background="{theme.color.White_5}" width="{sdp(1)}" height="{sdp(48)}"/>
                <ImageView id="avatarTwo" width="{sdp(72)}" height="{sdp(72)}"
                    src="{img(images/qms_call_ic_portrait_verysmall.png)}" scaleType="{enum.ImageView.ScaleType.Fitxy}"/>
                <TextView id="callnameTwo" propertySetName="extend/hdt/Headline" color="{theme.color.White_3}" width="{sdp(160)}" height="{sdp(32)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
                <TextView id="numberTwo" propertySetName="extend/hdt/Caption2" color="{theme.color.White_3}" width="{sdp(80)}" height="{sdp(24)}"
                        align = "{enum.TextView.Align.Left}"
                        multiLine="false" maxLineCount="1" elideMode= "{enum.TextView.ElideMode.ElideRight}"/>
            </CompositeView>
        <CompositeView id="anwerhanupTwoPanel"  clipBound="false" layout="{layout.anwerhanupLayoutTwoPanel}" height = "{sdp(72)}" width="{sdp(352)}">
            <CompositeView id="anwerhanupTwo"  clipBound="false" layout="{layout.anwerhanupLayoutTwo}" height = "{sdp(72)}" width="{sdp(352)}">
                <ButtonBM id="answerHangup" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_answer_48_normal.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="answerHold" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_answer_reservation_48_normal.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Primary}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
                <ButtonBM id="hangUpTwo" width="{sdp(72)}" height="{sdp(72)}" iconSrc="{img(images/qms_call_btn_hangup_48_normal.png)}"
                    buttonType="{enum.ButtonBM.ButtonType.Real}" colorType = "{enum.ButtonBM.ColorType.Warning}" contentType = "{enum.ButtonBM.ContentType.IconOnly}"/>
            </CompositeView>
            <CompositeView id="anwerhanupTwoInfo"  clipBound="false" layout="{layout.anwerhanupTwoInfo}" height = "{sdp(24)}" width="{sdp(352)}">
                <TextView id="answerHangupInfo" width="{sdp(90)}" height="{sdp(24)}" text="{string.TEXT_ANSWER}"
                    fontSize="{sdp(18)}"  align="{enum.TextView.Align.Center}" color="{theme.color.White_3}"/>
                <TextView id="answerHoldInfo" width="{sdp(90)}" height="{sdp(24)}" text="{string.TEXT_ANSWER_HOLD_1}"
                    fontSize="{sdp(18)}"  align="{enum.TextView.Align.Center}" color="{theme.color.White_3}"/>
                <TextView id="hangUpTwoInfo" width="{sdp(90)}" height="{sdp(24)}" text="{string.BTN_HANG_UP}"
                    fontSize="{sdp(18)}"  align="{enum.TextView.Align.Center}" color="{theme.color.White_3}"/>
            </CompositeView>
        </CompositeView>
            <ImageView id="minimizeTwo" width="{sdp(56)}" height="{sdp(56)}" src="{img(images/qms_call_ic_down_normal.png)}"
                scaleType="{enum.ImageView.ScaleType.Centerinside}" multiState="{config.button_icDownMultiState}"/>
        </CompositeView>
    </CompositeView>
    <CompositeView id="mtGestureGuide" height="{config.mt_float_guide_height}" background="#1C3861FF" opacity="0.9"
            layout="{layout.mtGestureGuideFloatLayout}" visibility="{enum.View.Visibility.None}">
        <ImageView id="twoFingered" src="{img(images/ic_gestures_hand1.png)}"/>
        <TextView id="guideText" fontSize="20sp" color="#FFFFFFFF" width="{dp(230)}" text="{string.TEXT_GUIDE_SMS_REJECT}"
                multiLine="false" maxLineCount="1" opacity="0.6" elideMode= "{enum.TextView.ElideMode.ElideLeft}"/>
    </CompositeView>
</CompositeView>
