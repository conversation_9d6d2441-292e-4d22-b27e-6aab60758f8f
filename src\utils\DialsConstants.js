"use strict";
const Feature = require("yunos/util/Feature");
class DialsConstants {
}
DialsConstants.Customization = {
    ENABLE_FLOAT_WINDOW: true,
    ENABLE_BLUR: !true,
    ENABLE_NAV_BAR: !true,
    <PERSON>NA<PERSON>E_FIT_SYSTEM_WINDOW: !true,
    <PERSON><PERSON><PERSON><PERSON>_SCREEN_POWER_CTRL: !true,
    ENABLE_KEYGUARD: !true,
    ENABLE_SECURITY_CHECK: true,
    ENABLE_CLOSE_NOTIFICATION: !true,
    ENABLE_EXPAND_FULL_SCREEN: !true,
    ENABLE_HAND_MOTION: Feature.has("YUNOS_SYSCAP_VISION.HAND_MOTION"),
    CALL_TYPE_CHART: "carchart",
    CALL_TYPE_BT: "bt"
};
module.exports = DialsConstants;
