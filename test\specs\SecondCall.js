"use strict";

const utTools = require("../UTTools");
const uiInf = require("../UiInterface");
const TouchEvent = require("yunos/ui/event/TouchEvent");
var View = require("yunos/ui/view/View");
// var CallAudioState = require("yunos/telecom/CallAudioState");

const addCall = {
    _subId: 0,
    _callId: 1,
    _state: 3,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 574235240,
    _phoneType: 1
};

// huangup call
const huangupcall = {
    _subId: 0,
    _callId: 1,
    _state: 7,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const ringcall = {
    _subId: 0,
    _callId: 1,
    _state: 4,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const activecall = {
    _subId: 0,
    _callId: 1,
    _state: 1,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 2037280616,
    _phoneType: 1
};

const holdCall = {
    _subId: 0,
    _callId: 1,
    _state: 2,
    _displayName: "",
    _callNumber: "15800000000",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 1734955885,
    _phoneType: 1
};

const secondAddCall = {
    _subId: 0,
    _callId: 2,
    _state: 3,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondAddIncomingCall = {
    _subId: 0,
    _callId: 2,
    _state: 6,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 34,
    _phoneType: 1
};

const secondRingingCall = {
    _subId: 0,
    _callId: 2,
    _state: 4,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondActiveCall = {
    _subId: 0,
    _callId: 2,
    _state: 1,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const secondHuangupCall = {
    _subId: 0,
    _callId: 2,
    _state: 7,
    _displayName: "",
    _callNumber: "13911727181",
    _multiParty: 0,
    _emergencyCall: 0,
    _disconnectCause: -1,
    _capability: 0,
    _phoneType: 1
};

const touchup = new TouchEvent({
    type: "touchend",
    changedTouches: [{
        clientX: 100,
        clientY: 10,
        identifier: 0
    }],
    eventPhase: TouchEvent.EventPhase.Target
});

function oneCallCheck() {
    utTools.log("SecondCall oneCallCheck");
    const controler = uiInf.getControler();
    const moLayout = uiInf.getMOLayout();
    utTools.equal(controler.appVisible, true);
    utTools.viewEqual(uiInf.getTopLayout(), moLayout);
    utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
    const service = uiInf.getService();
    utTools.equal(service.callItemSize(), 1);
    // hiden views
    utTools.equal(moLayout.convertCall.visibility, View.Visibility.None);
    utTools.equal(moLayout.mergeCall.visibility, View.Visibility.None);

    // visible views
    utTools.equal(moLayout.holdCall.visibility, View.Visibility.Visible);
    utTools.equal(moLayout.addCall.visibility, View.Visibility.Visible);
}

function twoCallCheck() {
    utTools.log("SecondCall twoCallCheck");
    const controler = uiInf.getControler();
    const moLayout = uiInf.getMOLayout();
    utTools.equal(controler.appVisible, true);

    const service = uiInf.getService();
    utTools.equal(service.callItemSize(), 2);
    // visible views
    utTools.equal(moLayout.convertCall.visibility, View.Visibility.Visible);
    utTools.equal(moLayout.mergeCall.visibility, View.Visibility.Visible);

    // hiden views
    utTools.equal(moLayout.holdCall.visibility, View.Visibility.None);
    utTools.equal(moLayout.addCall.visibility, View.Visibility.None);

    utTools.equal(moLayout.call1Number.text, addCall._callNumber);
}

describe("SecondCall", function() {
    utTools.log("SecondCall start");

    let originalTimeout;
    let huanupSecondCall = false;
    let callAfterEach = true;

    beforeAll(() => {
        utTools.log("SecondCall beforeAll");
        originalTimeout = jasmine.DEFAULT_TIMEOUT_INTERVAL;
        jasmine.DEFAULT_TIMEOUT_INTERVAL = 60000;
    });

    afterAll(() => {
        utTools.log("SecondCall afterAll");
        jasmine.DEFAULT_TIMEOUT_INTERVAL = originalTimeout;
    });

    beforeEach((done) => {
        utTools.log("SecondCall beforeEach");

        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall beforeEach run");
            utTools.MockInCallService.emit("calladded", addCall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", ringcall);
            yield 3000;
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
            oneCallCheck();
        }());
    });

    afterEach((done) => {
        utTools.log("SecondCall afterEach");

        if (!callAfterEach) {
            done();
            callAfterEach = true;
            return;
        }

        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall afterEach run");
            oneCallCheck();

            if (huanupSecondCall) {
                utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
                huanupSecondCall = false;
            } else {
                utTools.MockInCallService.emit("callstatechanged", huangupcall);
            }
            yield 3000;
            const controler = uiInf.getControler();
            utTools.viewEqual(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);
        }());

    });

    // add second out -> ringing -> manual huangup second
    it("out ringing", function(done) {
        utTools.log("SecondCall \"out ringing\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"out ringing\" run");
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            utTools.MockInCallService.emit("callstatechanged", secondAddCall);
            yield 3000;
            twoCallCheck();
            const moLayout = uiInf.getMOLayout();
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.strEqual(moLayout.callState.text, "TEXT_DIALING");
            utTools.MockInCallService.emit("callstatechanged", secondRingingCall);
            utTools.strEqual(moLayout.callState.text, "TEXT_RINGING");
            moLayout.hangUp.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.discParams, [2]);
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
        }());
    });

    // add second out -> ringing -> manual huangup
    it("out ringing, manual huangup", function(done) {
        utTools.log("SecondCall \"out ringing, manual huangup\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"out ringing, manual huangup\" run");
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            utTools.MockInCallService.emit("callstatechanged", secondAddCall);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.strEqual(moLayout.callState.text, "TEXT_DIALING");
            utTools.MockInCallService.emit("callstatechanged", secondRingingCall);
            utTools.strEqual(moLayout.callState.text, "TEXT_RINGING");
            twoCallCheck();

            moLayout.hangUp.dispatchEvent("touchend", touchup);
            utTools.ObjectEqual(utTools.MockInCallService.discParams, [2]);
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            utTools.MockInCallService.emit("callstatechanged", activecall);
            yield 3000;
        }());
    });

    // add second out -> active -> huangup first
    it("out ringing, active, hangup first ", function(done) {
        utTools.log("SecondCall \"out ringing, active, hangup first\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"out ringing, active, hangup first\" run");
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            utTools.MockInCallService.emit("callstatechanged", secondAddCall);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.strEqual(moLayout.callState.text, "TEXT_DIALING");
            utTools.MockInCallService.emit("callstatechanged", secondRingingCall);
            utTools.strEqual(moLayout.callState.text, "TEXT_RINGING");
            utTools.MockInCallService.emit("callstatechanged", secondActiveCall);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            twoCallCheck();

            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            utTools.equal(moLayout.callname.text, secondAddCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            huanupSecondCall = true;
        }());
    });

    // add second incoming -> hangup
    it("incoming ringing, hangup", function(done) {
        utTools.log("SecondCall \"incoming ringing, hangup\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"incoming ringing, hangup\" run");
            utTools.MockInCallService.emit("calladded", secondAddIncomingCall);
            yield 3000;
            const mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);
            utTools.strEqual(mtLayout.callState.text, "TEXT_INCOMING_CALL");
            const service = uiInf.getService();
            utTools.equal(service.callItemSize(), 2);

            //  hangup second
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            yield 3000;
            const moLayout = uiInf.getMOLayout();
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.callname.text, addCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
        }());
    });

    // add second incoming -> active -> hangup
    it("incoming ringing, active, hangup", function(done) {
        utTools.log("SecondCall \"incoming ringing, active, hangup\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"incoming ringing, active, hangup\" run");
            utTools.MockInCallService.emit("calladded", secondAddIncomingCall);
            yield 3000;
            const mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);
            utTools.strEqual(mtLayout.callState.text, "TEXT_INCOMING_CALL");
            const service = uiInf.getService();
            utTools.equal(service.callItemSize(), 2);

            utTools.log("incoming ringing, hangup 0");
            utTools.MockInCallService.emit("callstatechanged", holdCall);
            utTools.MockInCallService.emit("callstatechanged", secondActiveCall);
            let moLayout = uiInf.getMOLayout();
            yield 20000;
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.equal(moLayout.callname.text, secondAddIncomingCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            twoCallCheck();
            utTools.log("incoming ringing, hangup 1");

            //  hangup second
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            yield 3000;
            moLayout = uiInf.getMOLayout();
            utTools.log("incoming ringing, hangup 3");
            utTools.viewEqual(uiInf.getTopLayout(), moLayout);
            utTools.log("incoming ringing, hangup 4");
            utTools.equal(moLayout.callname.text, addCall._callNumber);
            utTools.strEqual(moLayout.callState.text, "TEXT_HOLD_ON");
            utTools.MockInCallService.emit("callstatechanged", activecall);
            utTools.log("incoming ringing, hangup 2");
        }());
    });

    // add second incoming -> huang first -> hangup second
    it("incoming ringing, hangup first, hangup second", function(done) {
        utTools.log("SecondCall \"incoming ringing, hangup first, hangup second\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"incoming ringing, hangup first, hangup second\" run");

            //  add second
            utTools.MockInCallService.emit("calladded", secondAddIncomingCall);
            yield 3000;
            let mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);
            utTools.strEqual(mtLayout.callState.text, "TEXT_INCOMING_CALL");
            const service = uiInf.getService();
            utTools.equal(service.callItemSize(), 2);

            //  hangup first
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            utTools.equal(service.callItemSize(), 1);
            mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);

            //  hangup second
            utTools.MockInCallService.emit("callstatechanged", secondHuangupCall);
            yield 3000;
            utTools.equal(service.callItemSize(), 0);
            const controler = uiInf.getControler();
            utTools.viewEqual(uiInf.getTopLayout(), undefined);
            utTools.equal(controler.appVisible, false);

            callAfterEach = false;
        }());
    });

    // add second incoming -> huang first -> active second
    it("incoming ringing, hangup first, active second", function(done) {
        utTools.log("SecondCall \"incoming ringing, hangup first, active second\" in");
        utTools.asyncRun(done, function *() {
            utTools.log("SecondCall \"incoming ringing, hangup first, active second\" run");

            //  add second
            utTools.MockInCallService.emit("calladded", secondAddIncomingCall);
            yield 3000;
            let mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);
            utTools.strEqual(mtLayout.callState.text, "TEXT_INCOMING_CALL");
            const service = uiInf.getService();
            utTools.equal(service.callItemSize(), 2);

            //  hangup first
            utTools.MockInCallService.emit("callstatechanged", huangupcall);
            yield 3000;
            utTools.equal(service.callItemSize(), 1);
            mtLayout = uiInf.getMTLayout();
            utTools.viewEqual(uiInf.getTopLayout(), mtLayout);

            //  active second
            utTools.MockInCallService.emit("callstatechanged", secondActiveCall);
            let moLayout = uiInf.getMOLayout();
            utTools.equal(moLayout.callname.text, secondAddCall._callNumber);
            utTools.pattern(/\d{1,3}:[0-5][0-9]$/, moLayout.callState.text);
            huanupSecondCall = true;
        }());
    });
});
